(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7581],{7106:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var a=t(5696),r=t(5765),x=t.n(r);function m(e){let{children:s}=e;return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("nav",{className:"bg-white shadow-sm border-b",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,a.jsx)(x(),{href:"/admin",className:"text-xl font-semibold text-gray-900",children:"管理后台"}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsx)(x(),{href:"/admin/posts",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium",children:"文章管理"}),(0,a.jsx)(x(),{href:"/admin/comments",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium",children:"评论管理"}),(0,a.jsx)(x(),{href:"/admin/categories",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium",children:"分类管理"}),(0,a.jsx)(x(),{href:"/admin/tags",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium",children:"标签管理"}),(0,a.jsx)(x(),{href:"/admin/settings",className:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium",children:"站点设置"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(x(),{href:"/",className:"text-gray-600 hover:text-gray-900 text-sm",children:"查看网站"}),(0,a.jsx)("button",{onClick:()=>{fetch("/api/auth/logout",{method:"POST"}).then(()=>window.location.href="/login")},className:"text-gray-600 hover:text-gray-900 text-sm",children:"退出登录"})]})]})})}),(0,a.jsx)("main",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"px-4 py-6 sm:px-0",children:s})})]})}},8896:(e,s,t)=>{Promise.resolve().then(t.bind(t,7106))}},e=>{e.O(0,[5765,9571,1266,7358],()=>e(e.s=8896)),_N_E=e.O()}]);