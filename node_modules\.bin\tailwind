#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/YQ_SOURCE_CODE/MY_PROJ/personal-blog/node_modules/.pnpm/tailwindcss@3.4.17/node_modules/tailwindcss/lib/node_modules:/mnt/d/YQ_SOURCE_CODE/MY_PROJ/personal-blog/node_modules/.pnpm/tailwindcss@3.4.17/node_modules/tailwindcss/node_modules:/mnt/d/YQ_SOURCE_CODE/MY_PROJ/personal-blog/node_modules/.pnpm/tailwindcss@3.4.17/node_modules:/mnt/d/YQ_SOURCE_CODE/MY_PROJ/personal-blog/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/YQ_SOURCE_CODE/MY_PROJ/personal-blog/node_modules/.pnpm/tailwindcss@3.4.17/node_modules/tailwindcss/lib/node_modules:/mnt/d/YQ_SOURCE_CODE/MY_PROJ/personal-blog/node_modules/.pnpm/tailwindcss@3.4.17/node_modules/tailwindcss/node_modules:/mnt/d/YQ_SOURCE_CODE/MY_PROJ/personal-blog/node_modules/.pnpm/tailwindcss@3.4.17/node_modules:/mnt/d/YQ_SOURCE_CODE/MY_PROJ/personal-blog/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../tailwindcss/lib/cli.js" "$@"
else
  exec node  "$basedir/../tailwindcss/lib/cli.js" "$@"
fi
