(()=>{var a={};a.id=7757,a.ids=[7757],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15168:()=>{},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44544:()=>{},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51702:(a,b,c)=>{"use strict";c.d(b,{z:()=>e});var d=c(96330);let e=global.prisma??new d.PrismaClient},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74776:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>G,patchFetch:()=>F,routeModule:()=>B,serverHooks:()=>E,workAsyncStorage:()=>C,workUnitAsyncStorage:()=>D});var d={};c.r(d),c.d(d,{DELETE:()=>A,GET:()=>y,PUT:()=>z});var e=c(27372),f=c(83729),g=c(17064),h=c(20762),i=c(19096),j=c(261),k=c(78006),l=c(62876),m=c(44724),n=c(60167),o=c(88305),p=c(28675),q=c(25133),r=c(79082),s=c(86439),t=c(40904),u=c(93701),v=c(51702),w=c(17293);let x=w.Ik({name:w.Yj().min(1).max(30),slug:w.Yj().min(1).max(30)});async function y(a,b){try{let a=await b.params,c=parseInt(a.id),d=await v.z.tag.findUnique({where:{id:c},include:{posts:{include:{post:{select:{id:!0,title:!0,slug:!0,status:!0}}}}}});if(!d)return u.NextResponse.json({success:!1,error:{code:"NOT_FOUND",message:"标签不存在"}},{status:404});return u.NextResponse.json({success:!0,data:{...d,postCount:d.posts.length}})}catch(a){return console.error("Get tag error:",a),u.NextResponse.json({success:!1,error:{code:"INTERNAL_ERROR",message:"获取标签失败"}},{status:500})}}async function z(a,b){try{let c=await b.params,d=parseInt(c.id),e=await a.json(),f=x.safeParse(e);if(!f.success)return u.NextResponse.json({success:!1,error:{code:"VALIDATION_ERROR",message:"请检查输入内容",details:f.error.issues}},{status:400});let{name:g,slug:h}=f.data;if(await v.z.tag.findFirst({where:{slug:h,id:{not:d}}}))return u.NextResponse.json({success:!1,error:{code:"SLUG_EXISTS",message:"Slug 已被其他标签使用"}},{status:400});let i=await v.z.tag.update({where:{id:d},data:{name:g,slug:h}});return u.NextResponse.json({success:!0,data:i,message:"标签更新成功"})}catch(a){return console.error("Update tag error:",a),u.NextResponse.json({success:!1,error:{code:"INTERNAL_ERROR",message:"更新标签失败"}},{status:500})}}async function A(a,b){try{let a=await b.params,c=parseInt(a.id);if((await v.z.postTag.findMany({where:{tagId:c},include:{post:{select:{title:!0}}}})).length>0)return u.NextResponse.json({success:!1,error:{code:"TAG_IN_USE",message:"无法删除正在使用的标签，请先移除相关文章的标签关联"}},{status:400});return await v.z.tag.delete({where:{id:c}}),u.NextResponse.json({success:!0,message:"标签删除成功"})}catch(a){return console.error("Delete tag error:",a),u.NextResponse.json({success:!1,error:{code:"INTERNAL_ERROR",message:"删除标签失败"}},{status:500})}}let B=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/admin/tags/[id]/route",pathname:"/api/admin/tags/[id]",filename:"route",bundlePath:"app/api/admin/tags/[id]/route"},distDir:".next",projectDir:"",resolvedPagePath:"D:\\YQ_SOURCE_CODE\\MY_PROJ\\personal-blog\\app\\api\\admin\\tags\\[id]\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:C,workUnitAsyncStorage:D,serverHooks:E}=B;function F(){return(0,g.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:D})}async function G(a,b,c){var d;let e="/api/admin/tags/[id]/route";"/index"===e&&(e="/");let g=await B.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[D]);if(F&&!x){let a=!!y.routes[D],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||B.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===B.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>B.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>B.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&A&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await B.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})},z),b}},l=await B.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await B.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96330:a=>{"use strict";a.exports=require("@prisma/client")}};var b=require("../../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[223,7526,7293],()=>b(b.s=74776));module.exports=c})();