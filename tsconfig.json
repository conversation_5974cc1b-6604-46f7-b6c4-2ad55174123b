{"compilerOptions": {"target": "ES2021", "lib": ["ES2021", "DOM", "DOM.Iterable"], "allowJs": false, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./*"]}, "plugins": [{"name": "next"}], "noImplicitAny": false}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}