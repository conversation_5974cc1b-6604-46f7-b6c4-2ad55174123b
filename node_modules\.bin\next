#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/YQ_SOURCE_CODE/MY_PROJ/personal-blog/node_modules/.pnpm/next@15.4.6_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/bin/node_modules:/mnt/d/YQ_SOURCE_CODE/MY_PROJ/personal-blog/node_modules/.pnpm/next@15.4.6_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/node_modules:/mnt/d/YQ_SOURCE_CODE/MY_PROJ/personal-blog/node_modules/.pnpm/next@15.4.6_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/node_modules:/mnt/d/YQ_SOURCE_CODE/MY_PROJ/personal-blog/node_modules/.pnpm/next@15.4.6_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules:/mnt/d/YQ_SOURCE_CODE/MY_PROJ/personal-blog/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/YQ_SOURCE_CODE/MY_PROJ/personal-blog/node_modules/.pnpm/next@15.4.6_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/bin/node_modules:/mnt/d/YQ_SOURCE_CODE/MY_PROJ/personal-blog/node_modules/.pnpm/next@15.4.6_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/node_modules:/mnt/d/YQ_SOURCE_CODE/MY_PROJ/personal-blog/node_modules/.pnpm/next@15.4.6_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/node_modules:/mnt/d/YQ_SOURCE_CODE/MY_PROJ/personal-blog/node_modules/.pnpm/next@15.4.6_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules:/mnt/d/YQ_SOURCE_CODE/MY_PROJ/personal-blog/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../next/dist/bin/next" "$@"
else
  exec node  "$basedir/../next/dist/bin/next" "$@"
fi
