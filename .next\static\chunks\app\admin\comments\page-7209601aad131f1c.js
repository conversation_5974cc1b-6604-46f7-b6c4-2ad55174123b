(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6409],{3868:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var a=s(5696),n=s(6060);function c(){let[e,t]=(0,n.useState)([]),[s,c]=(0,n.useState)(!0),[i,l]=(0,n.useState)([]),[r,d]=(0,n.useState)("all"),[o,m]=(0,n.useState)({page:1,limit:20,total:0,pages:0}),[p,h]=(0,n.useState)("");async function x(){c(!0);try{let e=new URLSearchParams({page:o.page.toString(),limit:o.limit.toString()});"all"!==r&&e.append("status",r.toUpperCase());let s=await fetch("/api/admin/comments?".concat(e)),a=await s.json();a.success&&(t(a.data.comments),m(a.data.pagination))}catch(e){console.error("Failed to fetch comments:",e)}finally{c(!1)}}async function g(e,t){try{let s=await fetch("/api/admin/comments/".concat(e),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:t})}),a=await s.json();a.success&&(h(a.message),x(),setTimeout(()=>h(""),3e3))}catch(e){console.error("Failed to update comment:",e)}}async function u(e){if(0===i.length)return void alert("请选择要操作的评论");if(confirm("确定要".concat("approve"===e?"通过":"reject"===e?"拒绝":"删除","选中的 ").concat(i.length," 条评论吗？")))try{let t=await fetch("/api/admin/comments",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({commentIds:i,action:e})}),s=await t.json();s.success&&(h(s.message),l([]),x(),setTimeout(()=>h(""),3e3))}catch(e){console.error("Batch operation failed:",e)}}return(0,n.useEffect)(()=>{x()},[r,o.page]),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:"评论管理"}),(0,a.jsx)("div",{className:"flex items-center gap-4",children:(0,a.jsxs)("select",{value:r,onChange:e=>d(e.target.value),className:"border rounded px-3 py-2",children:[(0,a.jsx)("option",{value:"all",children:"全部状态"}),(0,a.jsx)("option",{value:"pending",children:"待审核"}),(0,a.jsx)("option",{value:"approved",children:"已通过"}),(0,a.jsx)("option",{value:"rejected",children:"已拒绝"})]})})]}),p&&(0,a.jsx)("div",{className:"p-3 bg-green-100 text-green-800 rounded-lg",children:p}),i.length>0&&(0,a.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-blue-50 rounded-lg",children:[(0,a.jsxs)("span",{className:"text-sm",children:["已选择 ",i.length," 条评论"]}),(0,a.jsx)("button",{onClick:()=>u("approve"),className:"px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700",children:"批量通过"}),(0,a.jsx)("button",{onClick:()=>u("reject"),className:"px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700",children:"批量拒绝"}),(0,a.jsx)("button",{onClick:()=>u("delete"),className:"px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700",children:"批量删除"})]}),s?(0,a.jsx)("div",{className:"text-center py-8",children:"加载中..."}):0===e.length?(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"暂无评论"}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("input",{type:"checkbox",checked:i.length===e.length&&e.length>0,onChange:function(){l(t=>t.length===e.length?[]:e.map(e=>e.id))},className:"mr-3"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"全选"})]}),e.map(e=>(0,a.jsx)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("input",{type:"checkbox",checked:i.includes(e.id),onChange:()=>{var t;return t=e.id,void l(e=>e.includes(t)?e.filter(e=>e!==t):[...e,t])},className:"mt-1"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"font-medium",children:e.author}),e.email&&(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["(",e.email,")"]}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded ".concat((e=>{switch(e){case"APPROVED":return"bg-green-100 text-green-800";case"REJECTED":return"bg-red-100 text-red-800";default:return"bg-yellow-100 text-yellow-800"}})(e.status)),children:(e=>{switch(e){case"APPROVED":return"已通过";case"REJECTED":return"已拒绝";default:return"待审核"}})(e.status)})]}),(0,a.jsx)("time",{className:"text-sm text-gray-500",children:new Date(e.createdAt).toLocaleString("zh-CN")})]}),(0,a.jsx)("p",{className:"text-gray-700 mb-2 whitespace-pre-wrap",children:e.content}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["文章：",(0,a.jsx)("a",{href:"/post/".concat(e.post.slug),target:"_blank",className:"text-blue-600 hover:underline",children:e.post.title})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:["APPROVED"!==e.status&&(0,a.jsx)("button",{onClick:()=>g(e.id,"APPROVED"),className:"px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700",children:"通过"}),"REJECTED"!==e.status&&(0,a.jsx)("button",{onClick:()=>g(e.id,"REJECTED"),className:"px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700",children:"拒绝"}),(0,a.jsx)("button",{onClick:()=>{confirm("确定要删除这条评论吗？")&&fetch("/api/admin/comments/".concat(e.id),{method:"DELETE"}).then(()=>x())},className:"px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700",children:"删除"})]})]})]})]})},e.id)),o.pages>1&&(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)("button",{onClick:()=>m(e=>({...e,page:Math.max(1,e.page-1)})),disabled:1===o.page,className:"px-3 py-1 border rounded disabled:opacity-50",children:"上一页"}),(0,a.jsxs)("span",{className:"px-3 py-1",children:["第 ",o.page," 页，共 ",o.pages," 页"]}),(0,a.jsx)("button",{onClick:()=>m(e=>({...e,page:Math.min(e.pages,e.page+1)})),disabled:o.page===o.pages,className:"px-3 py-1 border rounded disabled:opacity-50",children:"下一页"})]})]})]})}},5785:(e,t,s)=>{Promise.resolve().then(s.bind(s,3868))}},e=>{e.O(0,[9571,1266,7358],()=>e(e.s=5785)),_N_E=e.O()}]);