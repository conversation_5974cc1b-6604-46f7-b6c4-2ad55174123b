(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6631],{1498:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(5696),r=s(6060);function l(){let[e,t]=(0,r.useState)([]),[s,l]=(0,r.useState)(!0),[n,i]=(0,r.useState)(!1),[c,d]=(0,r.useState)(null),[o,u]=(0,r.useState)([]),[h,m]=(0,r.useState)(""),[x,g]=(0,r.useState)(""),[p,b]=(0,r.useState)(""),[f,y]=(0,r.useState)("");async function j(){l(!0);try{let e=await fetch("/api/admin/categories"),s=await e.json();s.success&&t(s.data)}catch(e){console.error("Failed to fetch categories:",e)}finally{l(!1)}}function v(){g(""),b(""),y(""),d(null),i(!1)}async function N(e){e.preventDefault();try{let e=c?"/api/admin/categories/".concat(c.id):"/api/admin/categories",s=c?"PUT":"POST",a=await fetch(e,{method:s,headers:{"Content-Type":"application/json"},body:JSON.stringify({name:x,slug:p,description:f})}),r=await a.json();if(r.success)m(r.message),v(),j(),setTimeout(()=>m(""),3e3);else{var t;alert((null==(t=r.error)?void 0:t.message)||"操作失败")}}catch(e){console.error("Submit error:",e),alert("操作失败")}}async function w(e){if(confirm("确定要删除这个分类吗？"))try{let s=await fetch("/api/admin/categories/".concat(e),{method:"DELETE"}),a=await s.json();if(a.success)m(a.message),j(),setTimeout(()=>m(""),3e3);else{var t;alert((null==(t=a.error)?void 0:t.message)||"删除失败")}}catch(e){console.error("Delete error:",e),alert("删除失败")}}async function S(){if(0===o.length)return void alert("请选择要删除的分类");if(confirm("确定要删除选中的 ".concat(o.length," 个分类吗？")))try{let t=await fetch("/api/admin/categories",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({categoryIds:o})}),s=await t.json();if(s.success)m(s.message),u([]),j(),setTimeout(()=>m(""),3e3);else{var e;alert((null==(e=s.error)?void 0:e.message)||"删除失败")}}catch(e){console.error("Batch delete error:",e),alert("删除失败")}}return(0,r.useEffect)(()=>{j()},[]),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:"分类管理"}),(0,a.jsx)("button",{onClick:()=>i(!n),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:n?"取消":"新建分类"})]}),h&&(0,a.jsx)("div",{className:"p-3 bg-green-100 text-green-800 rounded-lg",children:h}),n&&(0,a.jsxs)("form",{onSubmit:N,className:"p-4 border rounded-lg bg-gray-50",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:c?"编辑分类":"新建分类"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,a.jsx)("input",{type:"text",placeholder:"分类名称",value:x,onChange:e=>g(e.target.value),required:!0,className:"px-3 py-2 border rounded-lg"}),(0,a.jsx)("input",{type:"text",placeholder:"URL Slug",value:p,onChange:e=>b(e.target.value),required:!0,className:"px-3 py-2 border rounded-lg"})]}),(0,a.jsx)("textarea",{placeholder:"分类描述（可选）",value:f,onChange:e=>y(e.target.value),rows:3,className:"w-full px-3 py-2 border rounded-lg mb-4"}),(0,a.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,a.jsx)("button",{type:"button",onClick:v,className:"px-4 py-2 text-gray-600 hover:text-gray-800",children:"取消"}),(0,a.jsx)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:c?"更新":"创建"})]})]}),o.length>0&&(0,a.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-blue-50 rounded-lg",children:[(0,a.jsxs)("span",{className:"text-sm",children:["已选择 ",o.length," 个分类"]}),(0,a.jsx)("button",{onClick:S,className:"px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700",children:"批量删除"})]}),s?(0,a.jsx)("div",{className:"text-center py-8",children:"加载中..."}):0===e.length?(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"暂无分类"}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("input",{type:"checkbox",checked:o.length===e.length&&e.length>0,onChange:function(){u(t=>t.length===e.length?[]:e.map(e=>e.id))},className:"mr-3"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"全选"})]}),e.map(e=>(0,a.jsx)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("input",{type:"checkbox",checked:o.includes(e.id),onChange:()=>{var t;return t=e.id,void u(e=>e.includes(t)?e.filter(e=>e!==t):[...e,t])}}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Slug: ",e.slug]}),e.description&&(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[e.postCount," 篇文章"]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>{d(e),g(e.name),b(e.slug),y(e.description||""),i(!0)},className:"px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700",children:"编辑"}),(0,a.jsx)("button",{onClick:()=>w(e.id),className:"px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700",children:"删除"})]})]})]})},e.id))]})]})}},6055:(e,t,s)=>{Promise.resolve().then(s.bind(s,1498))}},e=>{e.O(0,[9571,1266,7358],()=>e(e.s=6055)),_N_E=e.O()}]);