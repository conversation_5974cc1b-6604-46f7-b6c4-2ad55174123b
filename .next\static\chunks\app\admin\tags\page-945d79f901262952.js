(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1626],{2738:(e,s,t)=>{Promise.resolve().then(t.bind(t,5351))},5351:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(5696),l=t(6060);function n(){let[e,s]=(0,l.useState)([]),[t,n]=(0,l.useState)(!0),[r,i]=(0,l.useState)(!1),[c,d]=(0,l.useState)(null),[o,u]=(0,l.useState)([]),[h,m]=(0,l.useState)(""),[g,x]=(0,l.useState)(""),[p,b]=(0,l.useState)("");async function f(){n(!0);try{let e=await fetch("/api/admin/tags"),t=await e.json();t.success&&s(t.data)}catch(e){console.error("Failed to fetch tags:",e)}finally{n(!1)}}function y(){x(""),b(""),d(null),i(!1)}async function j(e){e.preventDefault();try{let e=c?"/api/admin/tags/".concat(c.id):"/api/admin/tags",t=c?"PUT":"POST",a=await fetch(e,{method:t,headers:{"Content-Type":"application/json"},body:JSON.stringify({name:g,slug:p})}),l=await a.json();if(l.success)m(l.message),y(),f(),setTimeout(()=>m(""),3e3);else{var s;alert((null==(s=l.error)?void 0:s.message)||"操作失败")}}catch(e){console.error("Submit error:",e),alert("操作失败")}}async function v(e){if(confirm("确定要删除这个标签吗？"))try{let t=await fetch("/api/admin/tags/".concat(e),{method:"DELETE"}),a=await t.json();if(a.success)m(a.message),f(),setTimeout(()=>m(""),3e3);else{var s;alert((null==(s=a.error)?void 0:s.message)||"删除失败")}}catch(e){console.error("Delete error:",e),alert("删除失败")}}async function N(){if(0===o.length)return void alert("请选择要删除的标签");if(confirm("确定要删除选中的 ".concat(o.length," 个标签吗？")))try{let s=await fetch("/api/admin/tags",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({tagIds:o})}),t=await s.json();if(t.success)m(t.message),u([]),f(),setTimeout(()=>m(""),3e3);else{var e;alert((null==(e=t.error)?void 0:e.message)||"删除失败")}}catch(e){console.error("Batch delete error:",e),alert("删除失败")}}return(0,l.useEffect)(()=>{f()},[]),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:"标签管理"}),(0,a.jsx)("button",{onClick:()=>i(!r),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:r?"取消":"新建标签"})]}),h&&(0,a.jsx)("div",{className:"p-3 bg-green-100 text-green-800 rounded-lg",children:h}),r&&(0,a.jsxs)("form",{onSubmit:j,className:"p-4 border rounded-lg bg-gray-50",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:c?"编辑标签":"新建标签"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,a.jsx)("input",{type:"text",placeholder:"标签名称",value:g,onChange:e=>x(e.target.value),required:!0,className:"px-3 py-2 border rounded-lg"}),(0,a.jsx)("input",{type:"text",placeholder:"URL Slug",value:p,onChange:e=>b(e.target.value),required:!0,className:"px-3 py-2 border rounded-lg"})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,a.jsx)("button",{type:"button",onClick:y,className:"px-4 py-2 text-gray-600 hover:text-gray-800",children:"取消"}),(0,a.jsx)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:c?"更新":"创建"})]})]}),o.length>0&&(0,a.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-blue-50 rounded-lg",children:[(0,a.jsxs)("span",{className:"text-sm",children:["已选择 ",o.length," 个标签"]}),(0,a.jsx)("button",{onClick:N,className:"px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700",children:"批量删除"})]}),t?(0,a.jsx)("div",{className:"text-center py-8",children:"加载中..."}):0===e.length?(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"暂无标签"}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("input",{type:"checkbox",checked:o.length===e.length&&e.length>0,onChange:function(){u(s=>s.length===e.length?[]:e.map(e=>e.id))},className:"mr-3"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"全选"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:e.map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:o.includes(e.id),onChange:()=>{var s;return s=e.id,void u(e=>e.includes(s)?e.filter(e=>e!==s):[...e,s])}}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[e.postCount," 篇文章"]})]}),(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("h3",{className:"font-semibold",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Slug: ",e.slug]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>{d(e),x(e.name),b(e.slug),i(!0)},className:"flex-1 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700",children:"编辑"}),(0,a.jsx)("button",{onClick:()=>v(e.id),className:"flex-1 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700",children:"删除"})]})]},e.id))})]})]})}}},e=>{e.O(0,[9571,1266,7358],()=>e(e.s=2738)),_N_E=e.O()}]);