exports.id=3473,exports.ids=[3473],exports.modules={13473:(a,b,c)=>{"use strict";let d;c.d(b,{renderMarkdown:()=>fE});var e={};c.r(e),c.d(e,{attentionMarkers:()=>a6,contentInitial:()=>a0,disable:()=>a7,document:()=>a_,flow:()=>a2,flowInitial:()=>a1,insideSpan:()=>a5,string:()=>a3,text:()=>a4});var f={};function g(a){if(a)throw a}c.r(f),c.d(f,{boolean:()=>ez,booleanish:()=>eA,commaOrSpaceSeparated:()=>eF,commaSeparated:()=>eE,number:()=>eC,overloadedBoolean:()=>eB,spaceSeparated:()=>eD});var h=c(41891);function i(a){if("object"!=typeof a||null===a)return!1;let b=Object.getPrototypeOf(a);return(null===b||b===Object.prototype||null===Object.getPrototypeOf(b))&&!(Symbol.toStringTag in a)&&!(Symbol.iterator in a)}function j(a){return a&&"object"==typeof a?"position"in a||"type"in a?l(a.position):"start"in a||"end"in a?l(a):"line"in a||"column"in a?k(a):"":""}function k(a){return m(a&&a.line)+":"+m(a&&a.column)}function l(a){return k(a&&a.start)+"-"+k(a&&a.end)}function m(a){return a&&"number"==typeof a?a:1}class n extends Error{constructor(a,b,c){super(),"string"==typeof b&&(c=b,b=void 0);let d="",e={},f=!1;if(b&&(e="line"in b&&"column"in b||"start"in b&&"end"in b?{place:b}:"type"in b?{ancestors:[b],place:b.position}:{...b}),"string"==typeof a?d=a:!e.cause&&a&&(f=!0,d=a.message,e.cause=a),!e.ruleId&&!e.source&&"string"==typeof c){let a=c.indexOf(":");-1===a?e.ruleId=c:(e.source=c.slice(0,a),e.ruleId=c.slice(a+1))}if(!e.place&&e.ancestors&&e.ancestors){let a=e.ancestors[e.ancestors.length-1];a&&(e.place=a.position)}let g=e.place&&"start"in e.place?e.place.start:e.place;this.ancestors=e.ancestors||void 0,this.cause=e.cause||void 0,this.column=g?g.column:void 0,this.fatal=void 0,this.file="",this.message=d,this.line=g?g.line:void 0,this.name=j(e.place)||"1:1",this.place=e.place||void 0,this.reason=this.message,this.ruleId=e.ruleId||void 0,this.source=e.source||void 0,this.stack=f&&e.cause&&"string"==typeof e.cause.stack?e.cause.stack:"",this.actual=void 0,this.expected=void 0,this.note=void 0,this.url=void 0}}n.prototype.file="",n.prototype.name="",n.prototype.reason="",n.prototype.message="",n.prototype.stack="",n.prototype.column=void 0,n.prototype.line=void 0,n.prototype.ancestors=void 0,n.prototype.cause=void 0,n.prototype.fatal=void 0,n.prototype.place=void 0,n.prototype.ruleId=void 0,n.prototype.source=void 0;var o=c(76760),p=c(1708);function q(a){return!!(null!==a&&"object"==typeof a&&"href"in a&&a.href&&"protocol"in a&&a.protocol&&void 0===a.auth)}var r=c(73136);let s=["history","path","basename","stem","extname","dirname"];class t{constructor(a){let b,c;b=a?q(a)?{path:a}:"string"==typeof a||function(a){return!!(a&&"object"==typeof a&&"byteLength"in a&&"byteOffset"in a)}(a)?{value:a}:a:{},this.cwd="cwd"in b?"":p.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let d=-1;for(;++d<s.length;){let a=s[d];a in b&&void 0!==b[a]&&null!==b[a]&&(this[a]="history"===a?[...b[a]]:b[a])}for(c in b)s.includes(c)||(this[c]=b[c])}get basename(){return"string"==typeof this.path?o.basename(this.path):void 0}set basename(a){v(a,"basename"),u(a,"basename"),this.path=o.join(this.dirname||"",a)}get dirname(){return"string"==typeof this.path?o.dirname(this.path):void 0}set dirname(a){w(this.basename,"dirname"),this.path=o.join(a||"",this.basename)}get extname(){return"string"==typeof this.path?o.extname(this.path):void 0}set extname(a){if(u(a,"extname"),w(this.dirname,"extname"),a){if(46!==a.codePointAt(0))throw Error("`extname` must start with `.`");if(a.includes(".",1))throw Error("`extname` cannot contain multiple dots")}this.path=o.join(this.dirname,this.stem+(a||""))}get path(){return this.history[this.history.length-1]}set path(a){q(a)&&(a=(0,r.fileURLToPath)(a)),v(a,"path"),this.path!==a&&this.history.push(a)}get stem(){return"string"==typeof this.path?o.basename(this.path,this.extname):void 0}set stem(a){v(a,"stem"),u(a,"stem"),this.path=o.join(this.dirname||"",a+(this.extname||""))}fail(a,b,c){let d=this.message(a,b,c);throw d.fatal=!0,d}info(a,b,c){let d=this.message(a,b,c);return d.fatal=void 0,d}message(a,b,c){let d=new n(a,b,c);return this.path&&(d.name=this.path+":"+d.name,d.file=this.path),d.fatal=!1,this.messages.push(d),d}toString(a){return void 0===this.value?"":"string"==typeof this.value?this.value:new TextDecoder(a||void 0).decode(this.value)}}function u(a,b){if(a&&a.includes(o.sep))throw Error("`"+b+"` cannot be a path: did not expect `"+o.sep+"`")}function v(a,b){if(!a)throw Error("`"+b+"` cannot be empty")}function w(a,b){if(!a)throw Error("Setting `"+b+"` requires `path` to be set too")}let x=function(a){let b=this.constructor.prototype,c=b[a],d=function(){return c.apply(d,arguments)};return Object.setPrototypeOf(d,b),d},y={}.hasOwnProperty;class z extends x{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=function(){let a=[],b={run:function(...b){let c=-1,d=b.pop();if("function"!=typeof d)throw TypeError("Expected function as last argument, not "+d);!function e(f,...g){let h=a[++c],i=-1;if(f)return void d(f);for(;++i<b.length;)(null===g[i]||void 0===g[i])&&(g[i]=b[i]);b=g,h?(function(a,b){let c;return function(...b){let f,g=a.length>b.length;g&&b.push(d);try{f=a.apply(this,b)}catch(a){if(g&&c)throw a;return d(a)}g||(f&&f.then&&"function"==typeof f.then?f.then(e,d):f instanceof Error?d(f):e(f))};function d(a,...e){c||(c=!0,b(a,...e))}function e(a){d(null,a)}})(h,e)(...g):d(null,...g)}(null,...b)},use:function(c){if("function"!=typeof c)throw TypeError("Expected `middelware` to be a function, not "+c);return a.push(c),b}};return b}()}copy(){let a=new z,b=-1;for(;++b<this.attachers.length;){let c=this.attachers[b];a.use(...c)}return a.data(h(!0,{},this.namespace)),a}data(a,b){return"string"==typeof a?2==arguments.length?(D("data",this.frozen),this.namespace[a]=b,this):y.call(this.namespace,a)&&this.namespace[a]||void 0:a?(D("data",this.frozen),this.namespace=a,this):this.namespace}freeze(){if(this.frozen)return this;for(;++this.freezeIndex<this.attachers.length;){let[a,...b]=this.attachers[this.freezeIndex];if(!1===b[0])continue;!0===b[0]&&(b[0]=void 0);let c=a.call(this,...b);"function"==typeof c&&this.transformers.use(c)}return this.frozen=!0,this.freezeIndex=1/0,this}parse(a){this.freeze();let b=G(a),c=this.parser||this.Parser;return B("parse",c),c(String(b),b)}process(a,b){let c=this;return this.freeze(),B("process",this.parser||this.Parser),C("process",this.compiler||this.Compiler),b?d(void 0,b):new Promise(d);function d(d,e){let f=G(a),g=c.parse(f);function h(a,c){a||!c?e(a):d?d(c):b(void 0,c)}c.run(g,f,function(a,b,d){var e,f;if(a||!b||!d)return h(a);let g=c.stringify(b,d);"string"==typeof(e=g)||(f=e)&&"object"==typeof f&&"byteLength"in f&&"byteOffset"in f?d.value=g:d.result=g,h(a,d)})}}processSync(a){let b,c=!1;return this.freeze(),B("processSync",this.parser||this.Parser),C("processSync",this.compiler||this.Compiler),this.process(a,function(a,d){c=!0,g(a),b=d}),F("processSync","process",c),b}run(a,b,c){E(a),this.freeze();let d=this.transformers;return c||"function"!=typeof b||(c=b,b=void 0),c?e(void 0,c):new Promise(e);function e(e,f){let g=G(b);d.run(a,g,function(b,d,g){let h=d||a;b?f(b):e?e(h):c(void 0,h,g)})}}runSync(a,b){let c,d=!1;return this.run(a,b,function(a,b){g(a),c=b,d=!0}),F("runSync","run",d),c}stringify(a,b){this.freeze();let c=G(b),d=this.compiler||this.Compiler;return C("stringify",d),E(a),d(a,c)}use(a,...b){let c=this.attachers,d=this.namespace;if(D("use",this.frozen),null==a);else if("function"==typeof a)g(a,b);else if("object"==typeof a)Array.isArray(a)?f(a):e(a);else throw TypeError("Expected usable value, not `"+a+"`");return this;function e(a){if(!("plugins"in a)&&!("settings"in a))throw Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");f(a.plugins),a.settings&&(d.settings=h(!0,d.settings,a.settings))}function f(a){let b=-1;if(null==a);else if(Array.isArray(a))for(;++b<a.length;){var c=a[b];if("function"==typeof c)g(c,[]);else if("object"==typeof c)if(Array.isArray(c)){let[a,...b]=c;g(a,b)}else e(c);else throw TypeError("Expected usable value, not `"+c+"`")}else throw TypeError("Expected a list of plugins, not `"+a+"`")}function g(a,b){let d=-1,e=-1;for(;++d<c.length;)if(c[d][0]===a){e=d;break}if(-1===e)c.push([a,...b]);else if(b.length>0){let[d,...f]=b,g=c[e][1];i(g)&&i(d)&&(d=h(!0,g,d)),c[e]=[a,d,...f]}}}}let A=new z().freeze();function B(a,b){if("function"!=typeof b)throw TypeError("Cannot `"+a+"` without `parser`")}function C(a,b){if("function"!=typeof b)throw TypeError("Cannot `"+a+"` without `compiler`")}function D(a,b){if(b)throw Error("Cannot call `"+a+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function E(a){if(!i(a)||"string"!=typeof a.type)throw TypeError("Expected node, got `"+a+"`")}function F(a,b,c){if(!c)throw Error("`"+a+"` finished async. Use `"+b+"` instead")}function G(a){var b;return(b=a)&&"object"==typeof b&&"message"in b&&"messages"in b?a:new t(a)}let H={};function I(a,b){let c=b||H;return J(a,"boolean"!=typeof c.includeImageAlt||c.includeImageAlt,"boolean"!=typeof c.includeHtml||c.includeHtml)}function J(a,b,c){var d;if((d=a)&&"object"==typeof d){if("value"in a)return"html"!==a.type||c?a.value:"";if(b&&"alt"in a&&a.alt)return a.alt;if("children"in a)return K(a.children,b,c)}return Array.isArray(a)?K(a,b,c):""}function K(a,b,c){let d=[],e=-1;for(;++e<a.length;)d[e]=J(a[e],b,c);return d.join("")}function L(a,b,c,d){let e,f=a.length,g=0;if(b=b<0?-b>f?0:f+b:b>f?f:b,c=c>0?c:0,d.length<1e4)(e=Array.from(d)).unshift(b,c),a.splice(...e);else for(c&&a.splice(b,c);g<d.length;)(e=d.slice(g,g+1e4)).unshift(b,0),a.splice(...e),g+=1e4,b+=1e4}function M(a,b){return a.length>0?(L(a,a.length,0,b),a):b}class N{constructor(a){this.left=a?[...a]:[],this.right=[]}get(a){if(a<0||a>=this.left.length+this.right.length)throw RangeError("Cannot access index `"+a+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return a<this.left.length?this.left[a]:this.right[this.right.length-a+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(a,b){let c=null==b?1/0:b;return c<this.left.length?this.left.slice(a,c):a>this.left.length?this.right.slice(this.right.length-c+this.left.length,this.right.length-a+this.left.length).reverse():this.left.slice(a).concat(this.right.slice(this.right.length-c+this.left.length).reverse())}splice(a,b,c){this.setCursor(Math.trunc(a));let d=this.right.splice(this.right.length-(b||0),1/0);return c&&O(this.left,c),d.reverse()}pop(){return this.setCursor(1/0),this.left.pop()}push(a){this.setCursor(1/0),this.left.push(a)}pushMany(a){this.setCursor(1/0),O(this.left,a)}unshift(a){this.setCursor(0),this.right.push(a)}unshiftMany(a){this.setCursor(0),O(this.right,a.reverse())}setCursor(a){if(a!==this.left.length&&(!(a>this.left.length)||0!==this.right.length)&&(!(a<0)||0!==this.left.length))if(a<this.left.length){let b=this.left.splice(a,1/0);O(this.right,b.reverse())}else{let b=this.right.splice(this.left.length+this.right.length-a,1/0);O(this.left,b.reverse())}}}function O(a,b){let c=0;if(b.length<1e4)a.push(...b);else for(;c<b.length;)a.push(...b.slice(c,c+1e4)),c+=1e4}function P(a){let b,c,d,e,f,g,h,i={},j=-1,k=new N(a);for(;++j<k.length;){for(;j in i;)j=i[j];if(b=k.get(j),j&&"chunkFlow"===b[1].type&&"listItemPrefix"===k.get(j-1)[1].type&&((d=0)<(g=b[1]._tokenizer.events).length&&"lineEndingBlank"===g[d][1].type&&(d+=2),d<g.length&&"content"===g[d][1].type))for(;++d<g.length&&"content"!==g[d][1].type;)"chunkText"===g[d][1].type&&(g[d][1]._isInFirstContentOfListItem=!0,d++);if("enter"===b[0])b[1].contentType&&(Object.assign(i,function(a,b){let c,d,e=a.get(b)[1],f=a.get(b)[2],g=b-1,h=[],i=e._tokenizer;!i&&(i=f.parser[e.contentType](e.start),e._contentTypeTextTrailing&&(i._contentTypeTextTrailing=!0));let j=i.events,k=[],l={},m=-1,n=e,o=0,p=0,q=[0];for(;n;){for(;a.get(++g)[1]!==n;);h.push(g),!n._tokenizer&&(c=f.sliceStream(n),n.next||c.push(null),d&&i.defineSkip(n.start),n._isInFirstContentOfListItem&&(i._gfmTasklistFirstContentOfListItem=!0),i.write(c),n._isInFirstContentOfListItem&&(i._gfmTasklistFirstContentOfListItem=void 0)),d=n,n=n.next}for(n=e;++m<j.length;)"exit"===j[m][0]&&"enter"===j[m-1][0]&&j[m][1].type===j[m-1][1].type&&j[m][1].start.line!==j[m][1].end.line&&(p=m+1,q.push(p),n._tokenizer=void 0,n.previous=void 0,n=n.next);for(i.events=[],n?(n._tokenizer=void 0,n.previous=void 0):q.pop(),m=q.length;m--;){let b=j.slice(q[m],q[m+1]),c=h.pop();k.push([c,c+b.length-1]),a.splice(c,2,b)}for(k.reverse(),m=-1;++m<k.length;)l[o+k[m][0]]=o+k[m][1],o+=k[m][1]-k[m][0]-1;return l}(k,j)),j=i[j],h=!0);else if(b[1]._container){for(d=j,c=void 0;d--;)if("lineEnding"===(e=k.get(d))[1].type||"lineEndingBlank"===e[1].type)"enter"===e[0]&&(c&&(k.get(c)[1].type="lineEndingBlank"),e[1].type="lineEnding",c=d);else if("linePrefix"===e[1].type||"listItemIndent"===e[1].type);else break;c&&(b[1].end={...k.get(c)[1].start},(f=k.slice(c,j)).unshift(b),k.splice(c,j-c+1,f))}}return L(a,0,1/0,k.slice(0)),!h}let Q={}.hasOwnProperty;function R(a){let b={},c=-1;for(;++c<a.length;)!function(a,b){let c;for(c in b){let d,e=(Q.call(a,c)?a[c]:void 0)||(a[c]={}),f=b[c];if(f)for(d in f){Q.call(e,d)||(e[d]=[]);let a=f[d];!function(a,b){let c=-1,d=[];for(;++c<b.length;)("after"===b[c].add?a:d).push(b[c]);L(a,0,0,d)}(e[d],Array.isArray(a)?a:a?[a]:[])}}}(b,a[c]);return b}let S=ac(/[A-Za-z]/),T=ac(/[\dA-Za-z]/),U=ac(/[#-'*+\--9=?A-Z^-~]/);function V(a){return null!==a&&(a<32||127===a)}let W=ac(/\d/),X=ac(/[\dA-Fa-f]/),Y=ac(/[!-/:-@[-`{-~]/);function Z(a){return null!==a&&a<-2}function $(a){return null!==a&&(a<0||32===a)}function _(a){return -2===a||-1===a||32===a}let aa=ac(/\p{P}|\p{S}/u),ab=ac(/\s/);function ac(a){return function(b){return null!==b&&b>-1&&a.test(String.fromCharCode(b))}}function ad(a,b,c,d){let e=d?d-1:1/0,f=0;return function(d){return _(d)?(a.enter(c),function d(g){return _(g)&&f++<e?(a.consume(g),d):(a.exit(c),b(g))}(d)):b(d)}}let ae={tokenize:function(a){let b,c=a.attempt(this.parser.constructs.contentInitial,function(b){return null===b?void a.consume(b):(a.enter("lineEnding"),a.consume(b),a.exit("lineEnding"),ad(a,c,"linePrefix"))},function(c){return a.enter("paragraph"),function c(d){let e=a.enter("chunkText",{contentType:"text",previous:b});return b&&(b.next=e),b=e,function b(d){if(null===d){a.exit("chunkText"),a.exit("paragraph"),a.consume(d);return}return Z(d)?(a.consume(d),a.exit("chunkText"),c):(a.consume(d),b)}(d)}(c)});return c}},af={tokenize:function(a){let b,c,d,e=this,f=[],g=0;return h;function h(b){if(g<f.length){let c=f[g];return e.containerState=c[1],a.attempt(c[0].continuation,i,j)(b)}return j(b)}function i(a){if(g++,e.containerState._closeFlow){let c;e.containerState._closeFlow=void 0,b&&r();let d=e.events.length,f=d;for(;f--;)if("exit"===e.events[f][0]&&"chunkFlow"===e.events[f][1].type){c=e.events[f][1].end;break}q(g);let h=d;for(;h<e.events.length;)e.events[h][1].end={...c},h++;return L(e.events,f+1,0,e.events.slice(d)),e.events.length=h,j(a)}return h(a)}function j(c){if(g===f.length){if(!b)return m(c);if(b.currentConstruct&&b.currentConstruct.concrete)return o(c);e.interrupt=!!(b.currentConstruct&&!b._gfmTableDynamicInterruptHack)}return e.containerState={},a.check(ag,k,l)(c)}function k(a){return b&&r(),q(g),m(a)}function l(a){return e.parser.lazy[e.now().line]=g!==f.length,d=e.now().offset,o(a)}function m(b){return e.containerState={},a.attempt(ag,n,o)(b)}function n(a){return g++,f.push([e.currentConstruct,e.containerState]),m(a)}function o(d){if(null===d){b&&r(),q(0),a.consume(d);return}return b=b||e.parser.flow(e.now()),a.enter("chunkFlow",{_tokenizer:b,contentType:"flow",previous:c}),function b(c){if(null===c){p(a.exit("chunkFlow"),!0),q(0),a.consume(c);return}return Z(c)?(a.consume(c),p(a.exit("chunkFlow")),g=0,e.interrupt=void 0,h):(a.consume(c),b)}(d)}function p(a,f){let h=e.sliceStream(a);if(f&&h.push(null),a.previous=c,c&&(c.next=a),c=a,b.defineSkip(a.start),b.write(h),e.parser.lazy[a.start.line]){let a,c,f=b.events.length;for(;f--;)if(b.events[f][1].start.offset<d&&(!b.events[f][1].end||b.events[f][1].end.offset>d))return;let h=e.events.length,i=h;for(;i--;)if("exit"===e.events[i][0]&&"chunkFlow"===e.events[i][1].type){if(a){c=e.events[i][1].end;break}a=!0}for(q(g),f=h;f<e.events.length;)e.events[f][1].end={...c},f++;L(e.events,i+1,0,e.events.slice(h)),e.events.length=f}}function q(b){let c=f.length;for(;c-- >b;){let b=f[c];e.containerState=b[1],b[0].exit.call(e,a)}f.length=b}function r(){b.write([null]),c=void 0,b=void 0,e.containerState._closeFlow=void 0}}},ag={tokenize:function(a,b,c){return ad(a,a.attempt(this.parser.constructs.document,b,c),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}},ah={partial:!0,tokenize:function(a,b,c){return function(b){return _(b)?ad(a,d,"linePrefix")(b):d(b)};function d(a){return null===a||Z(a)?b(a):c(a)}}},ai={resolve:function(a){return P(a),a},tokenize:function(a,b){let c;return function(b){return a.enter("content"),c=a.enter("chunkContent",{contentType:"content"}),d(b)};function d(b){return null===b?e(b):Z(b)?a.check(aj,f,e)(b):(a.consume(b),d)}function e(c){return a.exit("chunkContent"),a.exit("content"),b(c)}function f(b){return a.consume(b),a.exit("chunkContent"),c.next=a.enter("chunkContent",{contentType:"content",previous:c}),c=c.next,d}}},aj={partial:!0,tokenize:function(a,b,c){let d=this;return function(b){return a.exit("chunkContent"),a.enter("lineEnding"),a.consume(b),a.exit("lineEnding"),ad(a,e,"linePrefix")};function e(e){if(null===e||Z(e))return c(e);let f=d.events[d.events.length-1];return!d.parser.constructs.disable.null.includes("codeIndented")&&f&&"linePrefix"===f[1].type&&f[2].sliceSerialize(f[1],!0).length>=4?b(e):a.interrupt(d.parser.constructs.flow,c,b)(e)}}},ak={tokenize:function(a){let b=this,c=a.attempt(ah,function(d){return null===d?void a.consume(d):(a.enter("lineEndingBlank"),a.consume(d),a.exit("lineEndingBlank"),b.currentConstruct=void 0,c)},a.attempt(this.parser.constructs.flowInitial,d,ad(a,a.attempt(this.parser.constructs.flow,d,a.attempt(ai,d)),"linePrefix")));return c;function d(d){return null===d?void a.consume(d):(a.enter("lineEnding"),a.consume(d),a.exit("lineEnding"),b.currentConstruct=void 0,c)}}},al={resolveAll:ap()},am=ao("string"),an=ao("text");function ao(a){return{resolveAll:ap("text"===a?aq:void 0),tokenize:function(b){let c=this,d=this.parser.constructs[a],e=b.attempt(d,f,g);return f;function f(a){return i(a)?e(a):g(a)}function g(a){return null===a?void b.consume(a):(b.enter("data"),b.consume(a),h)}function h(a){return i(a)?(b.exit("data"),e(a)):(b.consume(a),h)}function i(a){if(null===a)return!0;let b=d[a],e=-1;if(b)for(;++e<b.length;){let a=b[e];if(!a.previous||a.previous.call(c,c.previous))return!0}return!1}}}}function ap(a){return function(b,c){let d,e=-1;for(;++e<=b.length;)void 0===d?b[e]&&"data"===b[e][1].type&&(d=e,e++):b[e]&&"data"===b[e][1].type||(e!==d+2&&(b[d][1].end=b[e-1][1].end,b.splice(d+2,e-d-2),e=d+2),d=void 0);return a?a(b,c):b}}function aq(a,b){let c=0;for(;++c<=a.length;)if((c===a.length||"lineEnding"===a[c][1].type)&&"data"===a[c-1][1].type){let d,e=a[c-1][1],f=b.sliceStream(e),g=f.length,h=-1,i=0;for(;g--;){let a=f[g];if("string"==typeof a){for(h=a.length;32===a.charCodeAt(h-1);)i++,h--;if(h)break;h=-1}else if(-2===a)d=!0,i++;else if(-1===a);else{g++;break}}if(b._contentTypeTextTrailing&&c===a.length&&(i=0),i){let f={type:c===a.length||d||i<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:g?h:e.start._bufferIndex+h,_index:e.start._index+g,line:e.end.line,column:e.end.column-i,offset:e.end.offset-i},end:{...e.end}};e.end={...f.start},e.start.offset===e.end.offset?Object.assign(e,f):(a.splice(c,0,["enter",f,b],["exit",f,b]),c+=2)}c++}return a}let ar={name:"thematicBreak",tokenize:function(a,b,c){let d,e=0;return function(f){var g;return a.enter("thematicBreak"),d=g=f,function f(g){return g===d?(a.enter("thematicBreakSequence"),function b(c){return c===d?(a.consume(c),e++,b):(a.exit("thematicBreakSequence"),_(c)?ad(a,f,"whitespace")(c):f(c))}(g)):e>=3&&(null===g||Z(g))?(a.exit("thematicBreak"),b(g)):c(g)}(g)}}},as={continuation:{tokenize:function(a,b,c){let d=this;return d.containerState._closeFlow=void 0,a.check(ah,function(c){return d.containerState.furtherBlankLines=d.containerState.furtherBlankLines||d.containerState.initialBlankLine,ad(a,b,"listItemIndent",d.containerState.size+1)(c)},function(c){return d.containerState.furtherBlankLines||!_(c)?(d.containerState.furtherBlankLines=void 0,d.containerState.initialBlankLine=void 0,e(c)):(d.containerState.furtherBlankLines=void 0,d.containerState.initialBlankLine=void 0,a.attempt(au,b,e)(c))});function e(e){return d.containerState._closeFlow=!0,d.interrupt=void 0,ad(a,a.attempt(as,b,c),"linePrefix",d.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(e)}}},exit:function(a){a.exit(this.containerState.type)},name:"list",tokenize:function(a,b,c){let d=this,e=d.events[d.events.length-1],f=e&&"linePrefix"===e[1].type?e[2].sliceSerialize(e[1],!0).length:0,g=0;return function(b){let e=d.containerState.type||(42===b||43===b||45===b?"listUnordered":"listOrdered");if("listUnordered"===e?!d.containerState.marker||b===d.containerState.marker:W(b)){if(d.containerState.type||(d.containerState.type=e,a.enter(e,{_container:!0})),"listUnordered"===e)return a.enter("listItemPrefix"),42===b||45===b?a.check(ar,c,h)(b):h(b);if(!d.interrupt||49===b)return a.enter("listItemPrefix"),a.enter("listItemValue"),function b(e){return W(e)&&++g<10?(a.consume(e),b):(!d.interrupt||g<2)&&(d.containerState.marker?e===d.containerState.marker:41===e||46===e)?(a.exit("listItemValue"),h(e)):c(e)}(b)}return c(b)};function h(b){return a.enter("listItemMarker"),a.consume(b),a.exit("listItemMarker"),d.containerState.marker=d.containerState.marker||b,a.check(ah,d.interrupt?c:i,a.attempt(at,k,j))}function i(a){return d.containerState.initialBlankLine=!0,f++,k(a)}function j(b){return _(b)?(a.enter("listItemPrefixWhitespace"),a.consume(b),a.exit("listItemPrefixWhitespace"),k):c(b)}function k(c){return d.containerState.size=f+d.sliceSerialize(a.exit("listItemPrefix"),!0).length,b(c)}}},at={partial:!0,tokenize:function(a,b,c){let d=this;return ad(a,function(a){let e=d.events[d.events.length-1];return!_(a)&&e&&"listItemPrefixWhitespace"===e[1].type?b(a):c(a)},"listItemPrefixWhitespace",d.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},au={partial:!0,tokenize:function(a,b,c){let d=this;return ad(a,function(a){let e=d.events[d.events.length-1];return e&&"listItemIndent"===e[1].type&&e[2].sliceSerialize(e[1],!0).length===d.containerState.size?b(a):c(a)},"listItemIndent",d.containerState.size+1)}},av={continuation:{tokenize:function(a,b,c){let d=this;return function(b){return _(b)?ad(a,e,"linePrefix",d.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(b):e(b)};function e(d){return a.attempt(av,b,c)(d)}}},exit:function(a){a.exit("blockQuote")},name:"blockQuote",tokenize:function(a,b,c){let d=this;return function(b){if(62===b){let c=d.containerState;return c.open||(a.enter("blockQuote",{_container:!0}),c.open=!0),a.enter("blockQuotePrefix"),a.enter("blockQuoteMarker"),a.consume(b),a.exit("blockQuoteMarker"),e}return c(b)};function e(c){return _(c)?(a.enter("blockQuotePrefixWhitespace"),a.consume(c),a.exit("blockQuotePrefixWhitespace"),a.exit("blockQuotePrefix"),b):(a.exit("blockQuotePrefix"),b(c))}}};function aw(a,b,c,d,e,f,g,h,i){let j=i||1/0,k=0;return function(b){return 60===b?(a.enter(d),a.enter(e),a.enter(f),a.consume(b),a.exit(f),l):null===b||32===b||41===b||V(b)?c(b):(a.enter(d),a.enter(g),a.enter(h),a.enter("chunkString",{contentType:"string"}),o(b))};function l(c){return 62===c?(a.enter(f),a.consume(c),a.exit(f),a.exit(e),a.exit(d),b):(a.enter(h),a.enter("chunkString",{contentType:"string"}),m(c))}function m(b){return 62===b?(a.exit("chunkString"),a.exit(h),l(b)):null===b||60===b||Z(b)?c(b):(a.consume(b),92===b?n:m)}function n(b){return 60===b||62===b||92===b?(a.consume(b),m):m(b)}function o(e){return!k&&(null===e||41===e||$(e))?(a.exit("chunkString"),a.exit(h),a.exit(g),a.exit(d),b(e)):k<j&&40===e?(a.consume(e),k++,o):41===e?(a.consume(e),k--,o):null===e||32===e||40===e||V(e)?c(e):(a.consume(e),92===e?p:o)}function p(b){return 40===b||41===b||92===b?(a.consume(b),o):o(b)}}function ax(a,b,c,d,e,f){let g,h=this,i=0;return function(b){return a.enter(d),a.enter(e),a.consume(b),a.exit(e),a.enter(f),j};function j(l){return i>999||null===l||91===l||93===l&&!g||94===l&&!i&&"_hiddenFootnoteSupport"in h.parser.constructs?c(l):93===l?(a.exit(f),a.enter(e),a.consume(l),a.exit(e),a.exit(d),b):Z(l)?(a.enter("lineEnding"),a.consume(l),a.exit("lineEnding"),j):(a.enter("chunkString",{contentType:"string"}),k(l))}function k(b){return null===b||91===b||93===b||Z(b)||i++>999?(a.exit("chunkString"),j(b)):(a.consume(b),g||(g=!_(b)),92===b?l:k)}function l(b){return 91===b||92===b||93===b?(a.consume(b),i++,k):k(b)}}function ay(a,b,c,d,e,f){let g;return function(b){return 34===b||39===b||40===b?(a.enter(d),a.enter(e),a.consume(b),a.exit(e),g=40===b?41:b,h):c(b)};function h(c){return c===g?(a.enter(e),a.consume(c),a.exit(e),a.exit(d),b):(a.enter(f),i(c))}function i(b){return b===g?(a.exit(f),h(g)):null===b?c(b):Z(b)?(a.enter("lineEnding"),a.consume(b),a.exit("lineEnding"),ad(a,i,"linePrefix")):(a.enter("chunkString",{contentType:"string"}),j(b))}function j(b){return b===g||null===b||Z(b)?(a.exit("chunkString"),i(b)):(a.consume(b),92===b?k:j)}function k(b){return b===g||92===b?(a.consume(b),j):j(b)}}function az(a,b){let c;return function d(e){return Z(e)?(a.enter("lineEnding"),a.consume(e),a.exit("lineEnding"),c=!0,d):_(e)?ad(a,d,c?"linePrefix":"lineSuffix")(e):b(e)}}function aA(a){return a.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}let aB={partial:!0,tokenize:function(a,b,c){return function(b){return $(b)?az(a,d)(b):c(b)};function d(b){return ay(a,e,c,"definitionTitle","definitionTitleMarker","definitionTitleString")(b)}function e(b){return _(b)?ad(a,f,"whitespace")(b):f(b)}function f(a){return null===a||Z(a)?b(a):c(a)}}},aC={name:"codeIndented",tokenize:function(a,b,c){let d=this;return function(b){return a.enter("codeIndented"),ad(a,e,"linePrefix",5)(b)};function e(b){let e=d.events[d.events.length-1];return e&&"linePrefix"===e[1].type&&e[2].sliceSerialize(e[1],!0).length>=4?function b(c){return null===c?f(c):Z(c)?a.attempt(aD,b,f)(c):(a.enter("codeFlowValue"),function c(d){return null===d||Z(d)?(a.exit("codeFlowValue"),b(d)):(a.consume(d),c)}(c))}(b):c(b)}function f(c){return a.exit("codeIndented"),b(c)}}},aD={partial:!0,tokenize:function(a,b,c){let d=this;return e;function e(b){return d.parser.lazy[d.now().line]?c(b):Z(b)?(a.enter("lineEnding"),a.consume(b),a.exit("lineEnding"),e):ad(a,f,"linePrefix",5)(b)}function f(a){let f=d.events[d.events.length-1];return f&&"linePrefix"===f[1].type&&f[2].sliceSerialize(f[1],!0).length>=4?b(a):Z(a)?e(a):c(a)}}},aE={name:"setextUnderline",resolveTo:function(a,b){let c,d,e,f=a.length;for(;f--;)if("enter"===a[f][0]){if("content"===a[f][1].type){c=f;break}"paragraph"===a[f][1].type&&(d=f)}else"content"===a[f][1].type&&a.splice(f,1),e||"definition"!==a[f][1].type||(e=f);let g={type:"setextHeading",start:{...a[c][1].start},end:{...a[a.length-1][1].end}};return a[d][1].type="setextHeadingText",e?(a.splice(d,0,["enter",g,b]),a.splice(e+1,0,["exit",a[c][1],b]),a[c][1].end={...a[e][1].end}):a[c][1]=g,a.push(["exit",g,b]),a},tokenize:function(a,b,c){let d,e=this;return function(b){var g;let h,i=e.events.length;for(;i--;)if("lineEnding"!==e.events[i][1].type&&"linePrefix"!==e.events[i][1].type&&"content"!==e.events[i][1].type){h="paragraph"===e.events[i][1].type;break}return!e.parser.lazy[e.now().line]&&(e.interrupt||h)?(a.enter("setextHeadingLine"),d=b,g=b,a.enter("setextHeadingLineSequence"),function b(c){return c===d?(a.consume(c),b):(a.exit("setextHeadingLineSequence"),_(c)?ad(a,f,"lineSuffix")(c):f(c))}(g)):c(b)};function f(d){return null===d||Z(d)?(a.exit("setextHeadingLine"),b(d)):c(d)}}},aF=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],aG=["pre","script","style","textarea"],aH={partial:!0,tokenize:function(a,b,c){return function(d){return a.enter("lineEnding"),a.consume(d),a.exit("lineEnding"),a.attempt(ah,b,c)}}},aI={partial:!0,tokenize:function(a,b,c){let d=this;return function(b){return Z(b)?(a.enter("lineEnding"),a.consume(b),a.exit("lineEnding"),e):c(b)};function e(a){return d.parser.lazy[d.now().line]?c(a):b(a)}}},aJ={partial:!0,tokenize:function(a,b,c){let d=this;return function(b){return null===b?c(b):(a.enter("lineEnding"),a.consume(b),a.exit("lineEnding"),e)};function e(a){return d.parser.lazy[d.now().line]?c(a):b(a)}}},aK={concrete:!0,name:"codeFenced",tokenize:function(a,b,c){let d,e=this,f={partial:!0,tokenize:function(a,b,c){let f=0;return function(b){return a.enter("lineEnding"),a.consume(b),a.exit("lineEnding"),g};function g(b){return a.enter("codeFencedFence"),_(b)?ad(a,i,"linePrefix",e.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(b):i(b)}function i(b){return b===d?(a.enter("codeFencedFenceSequence"),function b(e){return e===d?(f++,a.consume(e),b):f>=h?(a.exit("codeFencedFenceSequence"),_(e)?ad(a,j,"whitespace")(e):j(e)):c(e)}(b)):c(b)}function j(d){return null===d||Z(d)?(a.exit("codeFencedFence"),b(d)):c(d)}}},g=0,h=0;return function(b){var f=b;let j=e.events[e.events.length-1];return g=j&&"linePrefix"===j[1].type?j[2].sliceSerialize(j[1],!0).length:0,d=f,a.enter("codeFenced"),a.enter("codeFencedFence"),a.enter("codeFencedFenceSequence"),function b(e){return e===d?(h++,a.consume(e),b):h<3?c(e):(a.exit("codeFencedFenceSequence"),_(e)?ad(a,i,"whitespace")(e):i(e))}(f)};function i(f){return null===f||Z(f)?(a.exit("codeFencedFence"),e.interrupt?b(f):a.check(aJ,k,o)(f)):(a.enter("codeFencedFenceInfo"),a.enter("chunkString",{contentType:"string"}),function b(e){return null===e||Z(e)?(a.exit("chunkString"),a.exit("codeFencedFenceInfo"),i(e)):_(e)?(a.exit("chunkString"),a.exit("codeFencedFenceInfo"),ad(a,j,"whitespace")(e)):96===e&&e===d?c(e):(a.consume(e),b)}(f))}function j(b){return null===b||Z(b)?i(b):(a.enter("codeFencedFenceMeta"),a.enter("chunkString",{contentType:"string"}),function b(e){return null===e||Z(e)?(a.exit("chunkString"),a.exit("codeFencedFenceMeta"),i(e)):96===e&&e===d?c(e):(a.consume(e),b)}(b))}function k(b){return a.attempt(f,o,l)(b)}function l(b){return a.enter("lineEnding"),a.consume(b),a.exit("lineEnding"),m}function m(b){return g>0&&_(b)?ad(a,n,"linePrefix",g+1)(b):n(b)}function n(b){return null===b||Z(b)?a.check(aJ,k,o)(b):(a.enter("codeFlowValue"),function b(c){return null===c||Z(c)?(a.exit("codeFlowValue"),n(c)):(a.consume(c),b)}(b))}function o(c){return a.exit("codeFenced"),b(c)}}},aL={AElig:"\xc6",AMP:"&",Aacute:"\xc1",Abreve:"Ă",Acirc:"\xc2",Acy:"А",Afr:"\uD835\uDD04",Agrave:"\xc0",Alpha:"Α",Amacr:"Ā",And:"⩓",Aogon:"Ą",Aopf:"\uD835\uDD38",ApplyFunction:"⁡",Aring:"\xc5",Ascr:"\uD835\uDC9C",Assign:"≔",Atilde:"\xc3",Auml:"\xc4",Backslash:"∖",Barv:"⫧",Barwed:"⌆",Bcy:"Б",Because:"∵",Bernoullis:"ℬ",Beta:"Β",Bfr:"\uD835\uDD05",Bopf:"\uD835\uDD39",Breve:"˘",Bscr:"ℬ",Bumpeq:"≎",CHcy:"Ч",COPY:"\xa9",Cacute:"Ć",Cap:"⋒",CapitalDifferentialD:"ⅅ",Cayleys:"ℭ",Ccaron:"Č",Ccedil:"\xc7",Ccirc:"Ĉ",Cconint:"∰",Cdot:"Ċ",Cedilla:"\xb8",CenterDot:"\xb7",Cfr:"ℭ",Chi:"Χ",CircleDot:"⊙",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",Colon:"∷",Colone:"⩴",Congruent:"≡",Conint:"∯",ContourIntegral:"∮",Copf:"ℂ",Coproduct:"∐",CounterClockwiseContourIntegral:"∳",Cross:"⨯",Cscr:"\uD835\uDC9E",Cup:"⋓",CupCap:"≍",DD:"ⅅ",DDotrahd:"⤑",DJcy:"Ђ",DScy:"Ѕ",DZcy:"Џ",Dagger:"‡",Darr:"↡",Dashv:"⫤",Dcaron:"Ď",Dcy:"Д",Del:"∇",Delta:"Δ",Dfr:"\uD835\uDD07",DiacriticalAcute:"\xb4",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",Diamond:"⋄",DifferentialD:"ⅆ",Dopf:"\uD835\uDD3B",Dot:"\xa8",DotDot:"⃜",DotEqual:"≐",DoubleContourIntegral:"∯",DoubleDot:"\xa8",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrow:"↓",DownArrowBar:"⤓",DownArrowUpArrow:"⇵",DownBreve:"̑",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVector:"↽",DownLeftVectorBar:"⥖",DownRightTeeVector:"⥟",DownRightVector:"⇁",DownRightVectorBar:"⥗",DownTee:"⊤",DownTeeArrow:"↧",Downarrow:"⇓",Dscr:"\uD835\uDC9F",Dstrok:"Đ",ENG:"Ŋ",ETH:"\xd0",Eacute:"\xc9",Ecaron:"Ě",Ecirc:"\xca",Ecy:"Э",Edot:"Ė",Efr:"\uD835\uDD08",Egrave:"\xc8",Element:"∈",Emacr:"Ē",EmptySmallSquare:"◻",EmptyVerySmallSquare:"▫",Eogon:"Ę",Eopf:"\uD835\uDD3C",Epsilon:"Ε",Equal:"⩵",EqualTilde:"≂",Equilibrium:"⇌",Escr:"ℰ",Esim:"⩳",Eta:"Η",Euml:"\xcb",Exists:"∃",ExponentialE:"ⅇ",Fcy:"Ф",Ffr:"\uD835\uDD09",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",Fopf:"\uD835\uDD3D",ForAll:"∀",Fouriertrf:"ℱ",Fscr:"ℱ",GJcy:"Ѓ",GT:">",Gamma:"Γ",Gammad:"Ϝ",Gbreve:"Ğ",Gcedil:"Ģ",Gcirc:"Ĝ",Gcy:"Г",Gdot:"Ġ",Gfr:"\uD835\uDD0A",Gg:"⋙",Gopf:"\uD835\uDD3E",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"\uD835\uDCA2",Gt:"≫",HARDcy:"Ъ",Hacek:"ˇ",Hat:"^",Hcirc:"Ĥ",Hfr:"ℌ",HilbertSpace:"ℋ",Hopf:"ℍ",HorizontalLine:"─",Hscr:"ℋ",Hstrok:"Ħ",HumpDownHump:"≎",HumpEqual:"≏",IEcy:"Е",IJlig:"Ĳ",IOcy:"Ё",Iacute:"\xcd",Icirc:"\xce",Icy:"И",Idot:"İ",Ifr:"ℑ",Igrave:"\xcc",Im:"ℑ",Imacr:"Ī",ImaginaryI:"ⅈ",Implies:"⇒",Int:"∬",Integral:"∫",Intersection:"⋂",InvisibleComma:"⁣",InvisibleTimes:"⁢",Iogon:"Į",Iopf:"\uD835\uDD40",Iota:"Ι",Iscr:"ℐ",Itilde:"Ĩ",Iukcy:"І",Iuml:"\xcf",Jcirc:"Ĵ",Jcy:"Й",Jfr:"\uD835\uDD0D",Jopf:"\uD835\uDD41",Jscr:"\uD835\uDCA5",Jsercy:"Ј",Jukcy:"Є",KHcy:"Х",KJcy:"Ќ",Kappa:"Κ",Kcedil:"Ķ",Kcy:"К",Kfr:"\uD835\uDD0E",Kopf:"\uD835\uDD42",Kscr:"\uD835\uDCA6",LJcy:"Љ",LT:"<",Lacute:"Ĺ",Lambda:"Λ",Lang:"⟪",Laplacetrf:"ℒ",Larr:"↞",Lcaron:"Ľ",Lcedil:"Ļ",Lcy:"Л",LeftAngleBracket:"⟨",LeftArrow:"←",LeftArrowBar:"⇤",LeftArrowRightArrow:"⇆",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVector:"⇃",LeftDownVectorBar:"⥙",LeftFloor:"⌊",LeftRightArrow:"↔",LeftRightVector:"⥎",LeftTee:"⊣",LeftTeeArrow:"↤",LeftTeeVector:"⥚",LeftTriangle:"⊲",LeftTriangleBar:"⧏",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVector:"↿",LeftUpVectorBar:"⥘",LeftVector:"↼",LeftVectorBar:"⥒",Leftarrow:"⇐",Leftrightarrow:"⇔",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",LessLess:"⪡",LessSlantEqual:"⩽",LessTilde:"≲",Lfr:"\uD835\uDD0F",Ll:"⋘",Lleftarrow:"⇚",Lmidot:"Ŀ",LongLeftArrow:"⟵",LongLeftRightArrow:"⟷",LongRightArrow:"⟶",Longleftarrow:"⟸",Longleftrightarrow:"⟺",Longrightarrow:"⟹",Lopf:"\uD835\uDD43",LowerLeftArrow:"↙",LowerRightArrow:"↘",Lscr:"ℒ",Lsh:"↰",Lstrok:"Ł",Lt:"≪",Map:"⤅",Mcy:"М",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"\uD835\uDD10",MinusPlus:"∓",Mopf:"\uD835\uDD44",Mscr:"ℳ",Mu:"Μ",NJcy:"Њ",Nacute:"Ń",Ncaron:"Ň",Ncedil:"Ņ",Ncy:"Н",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:"\n",Nfr:"\uD835\uDD11",NoBreak:"⁠",NonBreakingSpace:"\xa0",Nopf:"ℕ",Not:"⫬",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",NotLeftTriangle:"⋪",NotLeftTriangleBar:"⧏̸",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangle:"⋫",NotRightTriangleBar:"⧐̸",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",Nscr:"\uD835\uDCA9",Ntilde:"\xd1",Nu:"Ν",OElig:"Œ",Oacute:"\xd3",Ocirc:"\xd4",Ocy:"О",Odblac:"Ő",Ofr:"\uD835\uDD12",Ograve:"\xd2",Omacr:"Ō",Omega:"Ω",Omicron:"Ο",Oopf:"\uD835\uDD46",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",Or:"⩔",Oscr:"\uD835\uDCAA",Oslash:"\xd8",Otilde:"\xd5",Otimes:"⨷",Ouml:"\xd6",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",PartialD:"∂",Pcy:"П",Pfr:"\uD835\uDD13",Phi:"Φ",Pi:"Π",PlusMinus:"\xb1",Poincareplane:"ℌ",Popf:"ℙ",Pr:"⪻",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",Prime:"″",Product:"∏",Proportion:"∷",Proportional:"∝",Pscr:"\uD835\uDCAB",Psi:"Ψ",QUOT:'"',Qfr:"\uD835\uDD14",Qopf:"ℚ",Qscr:"\uD835\uDCAC",RBarr:"⤐",REG:"\xae",Racute:"Ŕ",Rang:"⟫",Rarr:"↠",Rarrtl:"⤖",Rcaron:"Ř",Rcedil:"Ŗ",Rcy:"Р",Re:"ℜ",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",Rfr:"ℜ",Rho:"Ρ",RightAngleBracket:"⟩",RightArrow:"→",RightArrowBar:"⇥",RightArrowLeftArrow:"⇄",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVector:"⇂",RightDownVectorBar:"⥕",RightFloor:"⌋",RightTee:"⊢",RightTeeArrow:"↦",RightTeeVector:"⥛",RightTriangle:"⊳",RightTriangleBar:"⧐",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVector:"↾",RightUpVectorBar:"⥔",RightVector:"⇀",RightVectorBar:"⥓",Rightarrow:"⇒",Ropf:"ℝ",RoundImplies:"⥰",Rrightarrow:"⇛",Rscr:"ℛ",Rsh:"↱",RuleDelayed:"⧴",SHCHcy:"Щ",SHcy:"Ш",SOFTcy:"Ь",Sacute:"Ś",Sc:"⪼",Scaron:"Š",Scedil:"Ş",Scirc:"Ŝ",Scy:"С",Sfr:"\uD835\uDD16",ShortDownArrow:"↓",ShortLeftArrow:"←",ShortRightArrow:"→",ShortUpArrow:"↑",Sigma:"Σ",SmallCircle:"∘",Sopf:"\uD835\uDD4A",Sqrt:"√",Square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",Sscr:"\uD835\uDCAE",Star:"⋆",Sub:"⋐",Subset:"⋐",SubsetEqual:"⊆",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",SuchThat:"∋",Sum:"∑",Sup:"⋑",Superset:"⊃",SupersetEqual:"⊇",Supset:"⋑",THORN:"\xde",TRADE:"™",TSHcy:"Ћ",TScy:"Ц",Tab:"	",Tau:"Τ",Tcaron:"Ť",Tcedil:"Ţ",Tcy:"Т",Tfr:"\uD835\uDD17",Therefore:"∴",Theta:"Θ",ThickSpace:"  ",ThinSpace:" ",Tilde:"∼",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",Topf:"\uD835\uDD4B",TripleDot:"⃛",Tscr:"\uD835\uDCAF",Tstrok:"Ŧ",Uacute:"\xda",Uarr:"↟",Uarrocir:"⥉",Ubrcy:"Ў",Ubreve:"Ŭ",Ucirc:"\xdb",Ucy:"У",Udblac:"Ű",Ufr:"\uD835\uDD18",Ugrave:"\xd9",Umacr:"Ū",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",Uopf:"\uD835\uDD4C",UpArrow:"↑",UpArrowBar:"⤒",UpArrowDownArrow:"⇅",UpDownArrow:"↕",UpEquilibrium:"⥮",UpTee:"⊥",UpTeeArrow:"↥",Uparrow:"⇑",Updownarrow:"⇕",UpperLeftArrow:"↖",UpperRightArrow:"↗",Upsi:"ϒ",Upsilon:"Υ",Uring:"Ů",Uscr:"\uD835\uDCB0",Utilde:"Ũ",Uuml:"\xdc",VDash:"⊫",Vbar:"⫫",Vcy:"В",Vdash:"⊩",Vdashl:"⫦",Vee:"⋁",Verbar:"‖",Vert:"‖",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"\uD835\uDD19",Vopf:"\uD835\uDD4D",Vscr:"\uD835\uDCB1",Vvdash:"⊪",Wcirc:"Ŵ",Wedge:"⋀",Wfr:"\uD835\uDD1A",Wopf:"\uD835\uDD4E",Wscr:"\uD835\uDCB2",Xfr:"\uD835\uDD1B",Xi:"Ξ",Xopf:"\uD835\uDD4F",Xscr:"\uD835\uDCB3",YAcy:"Я",YIcy:"Ї",YUcy:"Ю",Yacute:"\xdd",Ycirc:"Ŷ",Ycy:"Ы",Yfr:"\uD835\uDD1C",Yopf:"\uD835\uDD50",Yscr:"\uD835\uDCB4",Yuml:"Ÿ",ZHcy:"Ж",Zacute:"Ź",Zcaron:"Ž",Zcy:"З",Zdot:"Ż",ZeroWidthSpace:"​",Zeta:"Ζ",Zfr:"ℨ",Zopf:"ℤ",Zscr:"\uD835\uDCB5",aacute:"\xe1",abreve:"ă",ac:"∾",acE:"∾̳",acd:"∿",acirc:"\xe2",acute:"\xb4",acy:"а",aelig:"\xe6",af:"⁡",afr:"\uD835\uDD1E",agrave:"\xe0",alefsym:"ℵ",aleph:"ℵ",alpha:"α",amacr:"ā",amalg:"⨿",amp:"&",and:"∧",andand:"⩕",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsd:"∡",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"\xc5",angzarr:"⍼",aogon:"ą",aopf:"\uD835\uDD52",ap:"≈",apE:"⩰",apacir:"⩯",ape:"≊",apid:"≋",apos:"'",approx:"≈",approxeq:"≊",aring:"\xe5",ascr:"\uD835\uDCB6",ast:"*",asymp:"≈",asympeq:"≍",atilde:"\xe3",auml:"\xe4",awconint:"∳",awint:"⨑",bNot:"⫭",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",barvee:"⊽",barwed:"⌅",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",bcy:"б",bdquo:"„",becaus:"∵",because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",beta:"β",beth:"ℶ",between:"≬",bfr:"\uD835\uDD1F",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bnot:"⌐",bopf:"\uD835\uDD53",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxDL:"╗",boxDR:"╔",boxDl:"╖",boxDr:"╓",boxH:"═",boxHD:"╦",boxHU:"╩",boxHd:"╤",boxHu:"╧",boxUL:"╝",boxUR:"╚",boxUl:"╜",boxUr:"╙",boxV:"║",boxVH:"╬",boxVL:"╣",boxVR:"╠",boxVh:"╫",boxVl:"╢",boxVr:"╟",boxbox:"⧉",boxdL:"╕",boxdR:"╒",boxdl:"┐",boxdr:"┌",boxh:"─",boxhD:"╥",boxhU:"╨",boxhd:"┬",boxhu:"┴",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxuL:"╛",boxuR:"╘",boxul:"┘",boxur:"└",boxv:"│",boxvH:"╪",boxvL:"╡",boxvR:"╞",boxvh:"┼",boxvl:"┤",boxvr:"├",bprime:"‵",breve:"˘",brvbar:"\xa6",bscr:"\uD835\uDCB7",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsol:"\\",bsolb:"⧅",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",bumpeq:"≏",cacute:"ć",cap:"∩",capand:"⩄",capbrcup:"⩉",capcap:"⩋",capcup:"⩇",capdot:"⩀",caps:"∩︀",caret:"⁁",caron:"ˇ",ccaps:"⩍",ccaron:"č",ccedil:"\xe7",ccirc:"ĉ",ccups:"⩌",ccupssm:"⩐",cdot:"ċ",cedil:"\xb8",cemptyv:"⦲",cent:"\xa2",centerdot:"\xb7",cfr:"\uD835\uDD20",chcy:"ч",check:"✓",checkmark:"✓",chi:"χ",cir:"○",cirE:"⧃",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledR:"\xae",circledS:"Ⓢ",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",clubs:"♣",clubsuit:"♣",colon:":",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",conint:"∮",copf:"\uD835\uDD54",coprod:"∐",copy:"\xa9",copysr:"℗",crarr:"↵",cross:"✗",cscr:"\uD835\uDCB8",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",cup:"∪",cupbrcap:"⩈",cupcap:"⩆",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curren:"\xa4",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",dArr:"⇓",dHar:"⥥",dagger:"†",daleth:"ℸ",darr:"↓",dash:"‐",dashv:"⊣",dbkarow:"⤏",dblac:"˝",dcaron:"ď",dcy:"д",dd:"ⅆ",ddagger:"‡",ddarr:"⇊",ddotseq:"⩷",deg:"\xb0",delta:"δ",demptyv:"⦱",dfisht:"⥿",dfr:"\uD835\uDD21",dharl:"⇃",dharr:"⇂",diam:"⋄",diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"\xa8",digamma:"ϝ",disin:"⋲",div:"\xf7",divide:"\xf7",divideontimes:"⋇",divonx:"⋇",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",dopf:"\uD835\uDD55",dot:"˙",doteq:"≐",doteqdot:"≑",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",downarrow:"↓",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",dscr:"\uD835\uDCB9",dscy:"ѕ",dsol:"⧶",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",dzcy:"џ",dzigrarr:"⟿",eDDot:"⩷",eDot:"≑",eacute:"\xe9",easter:"⩮",ecaron:"ě",ecir:"≖",ecirc:"\xea",ecolon:"≕",ecy:"э",edot:"ė",ee:"ⅇ",efDot:"≒",efr:"\uD835\uDD22",eg:"⪚",egrave:"\xe8",egs:"⪖",egsdot:"⪘",el:"⪙",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",emacr:"ē",empty:"∅",emptyset:"∅",emptyv:"∅",emsp13:" ",emsp14:" ",emsp:" ",eng:"ŋ",ensp:" ",eogon:"ę",eopf:"\uD835\uDD56",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",equals:"=",equest:"≟",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erDot:"≓",erarr:"⥱",escr:"ℯ",esdot:"≐",esim:"≂",eta:"η",eth:"\xf0",euml:"\xeb",euro:"€",excl:"!",exist:"∃",expectation:"ℰ",exponentiale:"ⅇ",fallingdotseq:"≒",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",ffr:"\uD835\uDD23",filig:"ﬁ",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",fopf:"\uD835\uDD57",forall:"∀",fork:"⋔",forkv:"⫙",fpartint:"⨍",frac12:"\xbd",frac13:"⅓",frac14:"\xbc",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"\xbe",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",fscr:"\uD835\uDCBB",gE:"≧",gEl:"⪌",gacute:"ǵ",gamma:"γ",gammad:"ϝ",gap:"⪆",gbreve:"ğ",gcirc:"ĝ",gcy:"г",gdot:"ġ",ge:"≥",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",ges:"⩾",gescc:"⪩",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",gfr:"\uD835\uDD24",gg:"≫",ggg:"⋙",gimel:"ℷ",gjcy:"ѓ",gl:"≷",glE:"⪒",gla:"⪥",glj:"⪤",gnE:"≩",gnap:"⪊",gnapprox:"⪊",gne:"⪈",gneq:"⪈",gneqq:"≩",gnsim:"⋧",gopf:"\uD835\uDD58",grave:"`",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",gt:">",gtcc:"⪧",gtcir:"⩺",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",hArr:"⇔",hairsp:" ",half:"\xbd",hamilt:"ℋ",hardcy:"ъ",harr:"↔",harrcir:"⥈",harrw:"↭",hbar:"ℏ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",hfr:"\uD835\uDD25",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",hopf:"\uD835\uDD59",horbar:"―",hscr:"\uD835\uDCBD",hslash:"ℏ",hstrok:"ħ",hybull:"⁃",hyphen:"‐",iacute:"\xed",ic:"⁣",icirc:"\xee",icy:"и",iecy:"е",iexcl:"\xa1",iff:"⇔",ifr:"\uD835\uDD26",igrave:"\xec",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",ijlig:"ĳ",imacr:"ī",image:"ℑ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",imof:"⊷",imped:"Ƶ",in:"∈",incare:"℅",infin:"∞",infintie:"⧝",inodot:"ı",int:"∫",intcal:"⊺",integers:"ℤ",intercal:"⊺",intlarhk:"⨗",intprod:"⨼",iocy:"ё",iogon:"į",iopf:"\uD835\uDD5A",iota:"ι",iprod:"⨼",iquest:"\xbf",iscr:"\uD835\uDCBE",isin:"∈",isinE:"⋹",isindot:"⋵",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",itilde:"ĩ",iukcy:"і",iuml:"\xef",jcirc:"ĵ",jcy:"й",jfr:"\uD835\uDD27",jmath:"ȷ",jopf:"\uD835\uDD5B",jscr:"\uD835\uDCBF",jsercy:"ј",jukcy:"є",kappa:"κ",kappav:"ϰ",kcedil:"ķ",kcy:"к",kfr:"\uD835\uDD28",kgreen:"ĸ",khcy:"х",kjcy:"ќ",kopf:"\uD835\uDD5C",kscr:"\uD835\uDCC0",lAarr:"⇚",lArr:"⇐",lAtail:"⤛",lBarr:"⤎",lE:"≦",lEg:"⪋",lHar:"⥢",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",lambda:"λ",lang:"⟨",langd:"⦑",langle:"⟨",lap:"⪅",laquo:"\xab",larr:"←",larrb:"⇤",larrbfs:"⤟",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",lat:"⪫",latail:"⤙",late:"⪭",lates:"⪭︀",lbarr:"⤌",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",lcaron:"ľ",lcedil:"ļ",lceil:"⌈",lcub:"{",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",le:"≤",leftarrow:"←",leftarrowtail:"↢",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",leftrightarrow:"↔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",leftthreetimes:"⋋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",les:"⩽",lescc:"⪨",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",lessgtr:"≶",lesssim:"≲",lfisht:"⥼",lfloor:"⌊",lfr:"\uD835\uDD29",lg:"≶",lgE:"⪑",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",ljcy:"љ",ll:"≪",llarr:"⇇",llcorner:"⌞",llhard:"⥫",lltri:"◺",lmidot:"ŀ",lmoust:"⎰",lmoustache:"⎰",lnE:"≨",lnap:"⪉",lnapprox:"⪉",lne:"⪇",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",longleftarrow:"⟵",longleftrightarrow:"⟷",longmapsto:"⟼",longrightarrow:"⟶",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",lopf:"\uD835\uDD5D",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",lscr:"\uD835\uDCC1",lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",lstrok:"ł",lt:"<",ltcc:"⪦",ltcir:"⩹",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltrPar:"⦖",ltri:"◃",ltrie:"⊴",ltrif:"◂",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",mDDot:"∺",macr:"\xaf",male:"♂",malt:"✠",maltese:"✠",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",mcy:"м",mdash:"—",measuredangle:"∡",mfr:"\uD835\uDD2A",mho:"℧",micro:"\xb5",mid:"∣",midast:"*",midcir:"⫰",middot:"\xb7",minus:"−",minusb:"⊟",minusd:"∸",minusdu:"⨪",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",mopf:"\uD835\uDD5E",mp:"∓",mscr:"\uD835\uDCC2",mstpos:"∾",mu:"μ",multimap:"⊸",mumap:"⊸",nGg:"⋙̸",nGt:"≫⃒",nGtv:"≫̸",nLeftarrow:"⇍",nLeftrightarrow:"⇎",nLl:"⋘̸",nLt:"≪⃒",nLtv:"≪̸",nRightarrow:"⇏",nVDash:"⊯",nVdash:"⊮",nabla:"∇",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natur:"♮",natural:"♮",naturals:"ℕ",nbsp:"\xa0",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",ncaron:"ň",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",ncy:"н",ndash:"–",ne:"≠",neArr:"⇗",nearhk:"⤤",nearr:"↗",nearrow:"↗",nedot:"≐̸",nequiv:"≢",nesear:"⤨",nesim:"≂̸",nexist:"∄",nexists:"∄",nfr:"\uD835\uDD2B",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",ngsim:"≵",ngt:"≯",ngtr:"≯",nhArr:"⇎",nharr:"↮",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",njcy:"њ",nlArr:"⇍",nlE:"≦̸",nlarr:"↚",nldr:"‥",nle:"≰",nleftarrow:"↚",nleftrightarrow:"↮",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nlsim:"≴",nlt:"≮",nltri:"⋪",nltrie:"⋬",nmid:"∤",nopf:"\uD835\uDD5F",not:"\xac",notin:"∉",notinE:"⋹̸",notindot:"⋵̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",npar:"∦",nparallel:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",npre:"⪯̸",nprec:"⊀",npreceq:"⪯̸",nrArr:"⇏",nrarr:"↛",nrarrc:"⤳̸",nrarrw:"↝̸",nrightarrow:"↛",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",nscr:"\uD835\uDCC3",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",ntilde:"\xf1",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",nu:"ν",num:"#",numero:"№",numsp:" ",nvDash:"⊭",nvHarr:"⤄",nvap:"≍⃒",nvdash:"⊬",nvge:"≥⃒",nvgt:">⃒",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwArr:"⇖",nwarhk:"⤣",nwarr:"↖",nwarrow:"↖",nwnear:"⤧",oS:"Ⓢ",oacute:"\xf3",oast:"⊛",ocir:"⊚",ocirc:"\xf4",ocy:"о",odash:"⊝",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",oelig:"œ",ofcir:"⦿",ofr:"\uD835\uDD2C",ogon:"˛",ograve:"\xf2",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",omacr:"ō",omega:"ω",omicron:"ο",omid:"⦶",ominus:"⊖",oopf:"\uD835\uDD60",opar:"⦷",operp:"⦹",oplus:"⊕",or:"∨",orarr:"↻",ord:"⩝",order:"ℴ",orderof:"ℴ",ordf:"\xaa",ordm:"\xba",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oscr:"ℴ",oslash:"\xf8",osol:"⊘",otilde:"\xf5",otimes:"⊗",otimesas:"⨶",ouml:"\xf6",ovbar:"⌽",par:"∥",para:"\xb6",parallel:"∥",parsim:"⫳",parsl:"⫽",part:"∂",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",pfr:"\uD835\uDD2D",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plus:"+",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plusdo:"∔",plusdu:"⨥",pluse:"⩲",plusmn:"\xb1",plussim:"⨦",plustwo:"⨧",pm:"\xb1",pointint:"⨕",popf:"\uD835\uDD61",pound:"\xa3",pr:"≺",prE:"⪳",prap:"⪷",prcue:"≼",pre:"⪯",prec:"≺",precapprox:"⪷",preccurlyeq:"≼",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",precsim:"≾",prime:"′",primes:"ℙ",prnE:"⪵",prnap:"⪹",prnsim:"⋨",prod:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",propto:"∝",prsim:"≾",prurel:"⊰",pscr:"\uD835\uDCC5",psi:"ψ",puncsp:" ",qfr:"\uD835\uDD2E",qint:"⨌",qopf:"\uD835\uDD62",qprime:"⁗",qscr:"\uD835\uDCC6",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",quot:'"',rAarr:"⇛",rArr:"⇒",rAtail:"⤜",rBarr:"⤏",rHar:"⥤",race:"∽̱",racute:"ŕ",radic:"√",raemptyv:"⦳",rang:"⟩",rangd:"⦒",range:"⦥",rangle:"⟩",raquo:"\xbb",rarr:"→",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",rarrtl:"↣",rarrw:"↝",ratail:"⤚",ratio:"∶",rationals:"ℚ",rbarr:"⤍",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",rcaron:"ř",rcedil:"ŗ",rceil:"⌉",rcub:"}",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",rect:"▭",reg:"\xae",rfisht:"⥽",rfloor:"⌋",rfr:"\uD835\uDD2F",rhard:"⇁",rharu:"⇀",rharul:"⥬",rho:"ρ",rhov:"ϱ",rightarrow:"→",rightarrowtail:"↣",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",rightthreetimes:"⋌",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoust:"⎱",rmoustache:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",ropf:"\uD835\uDD63",roplus:"⨮",rotimes:"⨵",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",rsaquo:"›",rscr:"\uD835\uDCC7",rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",ruluhar:"⥨",rx:"℞",sacute:"ś",sbquo:"‚",sc:"≻",scE:"⪴",scap:"⪸",scaron:"š",sccue:"≽",sce:"⪰",scedil:"ş",scirc:"ŝ",scnE:"⪶",scnap:"⪺",scnsim:"⋩",scpolint:"⨓",scsim:"≿",scy:"с",sdot:"⋅",sdotb:"⊡",sdote:"⩦",seArr:"⇘",searhk:"⤥",searr:"↘",searrow:"↘",sect:"\xa7",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",sfr:"\uD835\uDD30",sfrown:"⌢",sharp:"♯",shchcy:"щ",shcy:"ш",shortmid:"∣",shortparallel:"∥",shy:"\xad",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",softcy:"ь",sol:"/",solb:"⧄",solbar:"⌿",sopf:"\uD835\uDD64",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",squ:"□",square:"□",squarf:"▪",squf:"▪",srarr:"→",sscr:"\uD835\uDCC8",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"\xaf",sub:"⊂",subE:"⫅",subdot:"⪽",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",subset:"⊂",subseteq:"⊆",subseteqq:"⫅",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succ:"≻",succapprox:"⪸",succcurlyeq:"≽",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",sum:"∑",sung:"♪",sup1:"\xb9",sup2:"\xb2",sup3:"\xb3",sup:"⊃",supE:"⫆",supdot:"⪾",supdsub:"⫘",supe:"⊇",supedot:"⫄",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",supset:"⊃",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swArr:"⇙",swarhk:"⤦",swarr:"↙",swarrow:"↙",swnwar:"⤪",szlig:"\xdf",target:"⌖",tau:"τ",tbrk:"⎴",tcaron:"ť",tcedil:"ţ",tcy:"т",tdot:"⃛",telrec:"⌕",tfr:"\uD835\uDD31",there4:"∴",therefore:"∴",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",thinsp:" ",thkap:"≈",thksim:"∼",thorn:"\xfe",tilde:"˜",times:"\xd7",timesb:"⊠",timesbar:"⨱",timesd:"⨰",tint:"∭",toea:"⤨",top:"⊤",topbot:"⌶",topcir:"⫱",topf:"\uD835\uDD65",topfork:"⫚",tosa:"⤩",tprime:"‴",trade:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",tscr:"\uD835\uDCC9",tscy:"ц",tshcy:"ћ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",uArr:"⇑",uHar:"⥣",uacute:"\xfa",uarr:"↑",ubrcy:"ў",ubreve:"ŭ",ucirc:"\xfb",ucy:"у",udarr:"⇅",udblac:"ű",udhar:"⥮",ufisht:"⥾",ufr:"\uD835\uDD32",ugrave:"\xf9",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",umacr:"ū",uml:"\xa8",uogon:"ų",uopf:"\uD835\uDD66",uparrow:"↑",updownarrow:"↕",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",upsi:"υ",upsih:"ϒ",upsilon:"υ",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",uring:"ů",urtri:"◹",uscr:"\uD835\uDCCA",utdot:"⋰",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",uuml:"\xfc",uwangle:"⦧",vArr:"⇕",vBar:"⫨",vBarv:"⫩",vDash:"⊨",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",varr:"↕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",vcy:"в",vdash:"⊢",vee:"∨",veebar:"⊻",veeeq:"≚",vellip:"⋮",verbar:"|",vert:"|",vfr:"\uD835\uDD33",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",vopf:"\uD835\uDD67",vprop:"∝",vrtri:"⊳",vscr:"\uD835\uDCCB",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",vzigzag:"⦚",wcirc:"ŵ",wedbar:"⩟",wedge:"∧",wedgeq:"≙",weierp:"℘",wfr:"\uD835\uDD34",wopf:"\uD835\uDD68",wp:"℘",wr:"≀",wreath:"≀",wscr:"\uD835\uDCCC",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",xfr:"\uD835\uDD35",xhArr:"⟺",xharr:"⟷",xi:"ξ",xlArr:"⟸",xlarr:"⟵",xmap:"⟼",xnis:"⋻",xodot:"⨀",xopf:"\uD835\uDD69",xoplus:"⨁",xotime:"⨂",xrArr:"⟹",xrarr:"⟶",xscr:"\uD835\uDCCD",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",yacute:"\xfd",yacy:"я",ycirc:"ŷ",ycy:"ы",yen:"\xa5",yfr:"\uD835\uDD36",yicy:"ї",yopf:"\uD835\uDD6A",yscr:"\uD835\uDCCE",yucy:"ю",yuml:"\xff",zacute:"ź",zcaron:"ž",zcy:"з",zdot:"ż",zeetrf:"ℨ",zeta:"ζ",zfr:"\uD835\uDD37",zhcy:"ж",zigrarr:"⇝",zopf:"\uD835\uDD6B",zscr:"\uD835\uDCCF",zwj:"‍",zwnj:"‌"},aM={}.hasOwnProperty;function aN(a){return!!aM.call(aL,a)&&aL[a]}let aO={name:"characterReference",tokenize:function(a,b,c){let d,e,f=this,g=0;return function(b){return a.enter("characterReference"),a.enter("characterReferenceMarker"),a.consume(b),a.exit("characterReferenceMarker"),h};function h(b){return 35===b?(a.enter("characterReferenceMarkerNumeric"),a.consume(b),a.exit("characterReferenceMarkerNumeric"),i):(a.enter("characterReferenceValue"),d=31,e=T,j(b))}function i(b){return 88===b||120===b?(a.enter("characterReferenceMarkerHexadecimal"),a.consume(b),a.exit("characterReferenceMarkerHexadecimal"),a.enter("characterReferenceValue"),d=6,e=X,j):(a.enter("characterReferenceValue"),d=7,e=W,j(b))}function j(h){if(59===h&&g){let d=a.exit("characterReferenceValue");return e!==T||aN(f.sliceSerialize(d))?(a.enter("characterReferenceMarker"),a.consume(h),a.exit("characterReferenceMarker"),a.exit("characterReference"),b):c(h)}return e(h)&&g++<d?(a.consume(h),j):c(h)}}},aP={name:"characterEscape",tokenize:function(a,b,c){return function(b){return a.enter("characterEscape"),a.enter("escapeMarker"),a.consume(b),a.exit("escapeMarker"),d};function d(d){return Y(d)?(a.enter("characterEscapeValue"),a.consume(d),a.exit("characterEscapeValue"),a.exit("characterEscape"),b):c(d)}}},aQ={name:"lineEnding",tokenize:function(a,b){return function(c){return a.enter("lineEnding"),a.consume(c),a.exit("lineEnding"),ad(a,b,"linePrefix")}}};function aR(a,b,c){let d=[],e=-1;for(;++e<a.length;){let f=a[e].resolveAll;f&&!d.includes(f)&&(b=f(b,c),d.push(f))}return b}let aS={name:"labelEnd",resolveAll:function(a){let b=-1,c=[];for(;++b<a.length;){let d=a[b][1];if(c.push(a[b]),"labelImage"===d.type||"labelLink"===d.type||"labelEnd"===d.type){let a="labelImage"===d.type?4:2;d.type="data",b+=a}}return a.length!==c.length&&L(a,0,a.length,c),a},resolveTo:function(a,b){let c,d,e,f,g=a.length,h=0;for(;g--;)if(c=a[g][1],d){if("link"===c.type||"labelLink"===c.type&&c._inactive)break;"enter"===a[g][0]&&"labelLink"===c.type&&(c._inactive=!0)}else if(e){if("enter"===a[g][0]&&("labelImage"===c.type||"labelLink"===c.type)&&!c._balanced&&(d=g,"labelLink"!==c.type)){h=2;break}}else"labelEnd"===c.type&&(e=g);let i={type:"labelLink"===a[d][1].type?"link":"image",start:{...a[d][1].start},end:{...a[a.length-1][1].end}},j={type:"label",start:{...a[d][1].start},end:{...a[e][1].end}},k={type:"labelText",start:{...a[d+h+2][1].end},end:{...a[e-2][1].start}};return f=M(f=[["enter",i,b],["enter",j,b]],a.slice(d+1,d+h+3)),f=M(f,[["enter",k,b]]),f=M(f,aR(b.parser.constructs.insideSpan.null,a.slice(d+h+4,e-3),b)),f=M(f,[["exit",k,b],a[e-2],a[e-1],["exit",j,b]]),f=M(f,a.slice(e+1)),f=M(f,[["exit",i,b]]),L(a,d,a.length,f),a},tokenize:function(a,b,c){let d,e,f=this,g=f.events.length;for(;g--;)if(("labelImage"===f.events[g][1].type||"labelLink"===f.events[g][1].type)&&!f.events[g][1]._balanced){d=f.events[g][1];break}return function(b){return d?d._inactive?k(b):(e=f.parser.defined.includes(aA(f.sliceSerialize({start:d.end,end:f.now()}))),a.enter("labelEnd"),a.enter("labelMarker"),a.consume(b),a.exit("labelMarker"),a.exit("labelEnd"),h):c(b)};function h(b){return 40===b?a.attempt(aT,j,e?j:k)(b):91===b?a.attempt(aU,j,e?i:k)(b):e?j(b):k(b)}function i(b){return a.attempt(aV,j,k)(b)}function j(a){return b(a)}function k(a){return d._balanced=!0,c(a)}}},aT={tokenize:function(a,b,c){return function(b){return a.enter("resource"),a.enter("resourceMarker"),a.consume(b),a.exit("resourceMarker"),d};function d(b){return $(b)?az(a,e)(b):e(b)}function e(b){return 41===b?j(b):aw(a,f,g,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(b)}function f(b){return $(b)?az(a,h)(b):j(b)}function g(a){return c(a)}function h(b){return 34===b||39===b||40===b?ay(a,i,c,"resourceTitle","resourceTitleMarker","resourceTitleString")(b):j(b)}function i(b){return $(b)?az(a,j)(b):j(b)}function j(d){return 41===d?(a.enter("resourceMarker"),a.consume(d),a.exit("resourceMarker"),a.exit("resource"),b):c(d)}}},aU={tokenize:function(a,b,c){let d=this;return function(b){return ax.call(d,a,e,f,"reference","referenceMarker","referenceString")(b)};function e(a){return d.parser.defined.includes(aA(d.sliceSerialize(d.events[d.events.length-1][1]).slice(1,-1)))?b(a):c(a)}function f(a){return c(a)}}},aV={tokenize:function(a,b,c){return function(b){return a.enter("reference"),a.enter("referenceMarker"),a.consume(b),a.exit("referenceMarker"),d};function d(d){return 93===d?(a.enter("referenceMarker"),a.consume(d),a.exit("referenceMarker"),a.exit("reference"),b):c(d)}}},aW={name:"labelStartImage",resolveAll:aS.resolveAll,tokenize:function(a,b,c){let d=this;return function(b){return a.enter("labelImage"),a.enter("labelImageMarker"),a.consume(b),a.exit("labelImageMarker"),e};function e(b){return 91===b?(a.enter("labelMarker"),a.consume(b),a.exit("labelMarker"),a.exit("labelImage"),f):c(b)}function f(a){return 94===a&&"_hiddenFootnoteSupport"in d.parser.constructs?c(a):b(a)}}};function aX(a){return null===a||$(a)||ab(a)?1:aa(a)?2:void 0}let aY={name:"attention",resolveAll:function(a,b){let c,d,e,f,g,h,i,j,k=-1;for(;++k<a.length;)if("enter"===a[k][0]&&"attentionSequence"===a[k][1].type&&a[k][1]._close){for(c=k;c--;)if("exit"===a[c][0]&&"attentionSequence"===a[c][1].type&&a[c][1]._open&&b.sliceSerialize(a[c][1]).charCodeAt(0)===b.sliceSerialize(a[k][1]).charCodeAt(0)){if((a[c][1]._close||a[k][1]._open)&&(a[k][1].end.offset-a[k][1].start.offset)%3&&!((a[c][1].end.offset-a[c][1].start.offset+a[k][1].end.offset-a[k][1].start.offset)%3))continue;h=a[c][1].end.offset-a[c][1].start.offset>1&&a[k][1].end.offset-a[k][1].start.offset>1?2:1;let l={...a[c][1].end},m={...a[k][1].start};aZ(l,-h),aZ(m,h),f={type:h>1?"strongSequence":"emphasisSequence",start:l,end:{...a[c][1].end}},g={type:h>1?"strongSequence":"emphasisSequence",start:{...a[k][1].start},end:m},e={type:h>1?"strongText":"emphasisText",start:{...a[c][1].end},end:{...a[k][1].start}},d={type:h>1?"strong":"emphasis",start:{...f.start},end:{...g.end}},a[c][1].end={...f.start},a[k][1].start={...g.end},i=[],a[c][1].end.offset-a[c][1].start.offset&&(i=M(i,[["enter",a[c][1],b],["exit",a[c][1],b]])),i=M(i,[["enter",d,b],["enter",f,b],["exit",f,b],["enter",e,b]]),i=M(i,aR(b.parser.constructs.insideSpan.null,a.slice(c+1,k),b)),i=M(i,[["exit",e,b],["enter",g,b],["exit",g,b],["exit",d,b]]),a[k][1].end.offset-a[k][1].start.offset?(j=2,i=M(i,[["enter",a[k][1],b],["exit",a[k][1],b]])):j=0,L(a,c-1,k-c+3,i),k=c+i.length-j-2;break}}for(k=-1;++k<a.length;)"attentionSequence"===a[k][1].type&&(a[k][1].type="data");return a},tokenize:function(a,b){let c,d=this.parser.constructs.attentionMarkers.null,e=this.previous,f=aX(e);return function(g){return c=g,a.enter("attentionSequence"),function g(h){if(h===c)return a.consume(h),g;let i=a.exit("attentionSequence"),j=aX(h),k=!j||2===j&&f||d.includes(h),l=!f||2===f&&j||d.includes(e);return i._open=!!(42===c?k:k&&(f||!l)),i._close=!!(42===c?l:l&&(j||!k)),b(h)}(g)}}};function aZ(a,b){a.column+=b,a.offset+=b,a._bufferIndex+=b}let a$={name:"labelStartLink",resolveAll:aS.resolveAll,tokenize:function(a,b,c){let d=this;return function(b){return a.enter("labelLink"),a.enter("labelMarker"),a.consume(b),a.exit("labelMarker"),a.exit("labelLink"),e};function e(a){return 94===a&&"_hiddenFootnoteSupport"in d.parser.constructs?c(a):b(a)}}},a_={42:as,43:as,45:as,48:as,49:as,50:as,51:as,52:as,53:as,54:as,55:as,56:as,57:as,62:av},a0={91:{name:"definition",tokenize:function(a,b,c){let d,e=this;return function(b){var d;return a.enter("definition"),d=b,ax.call(e,a,f,c,"definitionLabel","definitionLabelMarker","definitionLabelString")(d)};function f(b){return(d=aA(e.sliceSerialize(e.events[e.events.length-1][1]).slice(1,-1)),58===b)?(a.enter("definitionMarker"),a.consume(b),a.exit("definitionMarker"),g):c(b)}function g(b){return $(b)?az(a,h)(b):h(b)}function h(b){return aw(a,i,c,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(b)}function i(b){return a.attempt(aB,j,j)(b)}function j(b){return _(b)?ad(a,k,"whitespace")(b):k(b)}function k(f){return null===f||Z(f)?(a.exit("definition"),e.parser.defined.push(d),b(f)):c(f)}}}},a1={[-2]:aC,[-1]:aC,32:aC},a2={35:{name:"headingAtx",resolve:function(a,b){let c,d,e=a.length-2,f=3;return"whitespace"===a[3][1].type&&(f+=2),e-2>f&&"whitespace"===a[e][1].type&&(e-=2),"atxHeadingSequence"===a[e][1].type&&(f===e-1||e-4>f&&"whitespace"===a[e-2][1].type)&&(e-=f+1===e?2:4),e>f&&(c={type:"atxHeadingText",start:a[f][1].start,end:a[e][1].end},d={type:"chunkText",start:a[f][1].start,end:a[e][1].end,contentType:"text"},L(a,f,e-f+1,[["enter",c,b],["enter",d,b],["exit",d,b],["exit",c,b]])),a},tokenize:function(a,b,c){let d=0;return function(e){var f;return a.enter("atxHeading"),f=e,a.enter("atxHeadingSequence"),function e(f){return 35===f&&d++<6?(a.consume(f),e):null===f||$(f)?(a.exit("atxHeadingSequence"),function c(d){return 35===d?(a.enter("atxHeadingSequence"),function b(d){return 35===d?(a.consume(d),b):(a.exit("atxHeadingSequence"),c(d))}(d)):null===d||Z(d)?(a.exit("atxHeading"),b(d)):_(d)?ad(a,c,"whitespace")(d):(a.enter("atxHeadingText"),function b(d){return null===d||35===d||$(d)?(a.exit("atxHeadingText"),c(d)):(a.consume(d),b)}(d))}(f)):c(f)}(f)}}},42:ar,45:[aE,ar],60:{concrete:!0,name:"htmlFlow",resolveTo:function(a){let b=a.length;for(;b--&&("enter"!==a[b][0]||"htmlFlow"!==a[b][1].type););return b>1&&"linePrefix"===a[b-2][1].type&&(a[b][1].start=a[b-2][1].start,a[b+1][1].start=a[b-2][1].start,a.splice(b-2,2)),a},tokenize:function(a,b,c){let d,e,f,g,h,i=this;return function(b){var c;return c=b,a.enter("htmlFlow"),a.enter("htmlFlowData"),a.consume(c),j};function j(g){return 33===g?(a.consume(g),k):47===g?(a.consume(g),e=!0,n):63===g?(a.consume(g),d=3,i.interrupt?b:G):S(g)?(a.consume(g),f=String.fromCharCode(g),o):c(g)}function k(e){return 45===e?(a.consume(e),d=2,l):91===e?(a.consume(e),d=5,g=0,m):S(e)?(a.consume(e),d=4,i.interrupt?b:G):c(e)}function l(d){return 45===d?(a.consume(d),i.interrupt?b:G):c(d)}function m(d){let e="CDATA[";return d===e.charCodeAt(g++)?(a.consume(d),g===e.length)?i.interrupt?b:y:m:c(d)}function n(b){return S(b)?(a.consume(b),f=String.fromCharCode(b),o):c(b)}function o(g){if(null===g||47===g||62===g||$(g)){let h=47===g,j=f.toLowerCase();return!h&&!e&&aG.includes(j)?(d=1,i.interrupt?b(g):y(g)):aF.includes(f.toLowerCase())?(d=6,h)?(a.consume(g),p):i.interrupt?b(g):y(g):(d=7,i.interrupt&&!i.parser.lazy[i.now().line]?c(g):e?function b(c){return _(c)?(a.consume(c),b):w(c)}(g):q(g))}return 45===g||T(g)?(a.consume(g),f+=String.fromCharCode(g),o):c(g)}function p(d){return 62===d?(a.consume(d),i.interrupt?b:y):c(d)}function q(b){return 47===b?(a.consume(b),w):58===b||95===b||S(b)?(a.consume(b),r):_(b)?(a.consume(b),q):w(b)}function r(b){return 45===b||46===b||58===b||95===b||T(b)?(a.consume(b),r):s(b)}function s(b){return 61===b?(a.consume(b),t):_(b)?(a.consume(b),s):q(b)}function t(b){return null===b||60===b||61===b||62===b||96===b?c(b):34===b||39===b?(a.consume(b),h=b,u):_(b)?(a.consume(b),t):function b(c){return null===c||34===c||39===c||47===c||60===c||61===c||62===c||96===c||$(c)?s(c):(a.consume(c),b)}(b)}function u(b){return b===h?(a.consume(b),h=null,v):null===b||Z(b)?c(b):(a.consume(b),u)}function v(a){return 47===a||62===a||_(a)?q(a):c(a)}function w(b){return 62===b?(a.consume(b),x):c(b)}function x(b){return null===b||Z(b)?y(b):_(b)?(a.consume(b),x):c(b)}function y(b){return 45===b&&2===d?(a.consume(b),C):60===b&&1===d?(a.consume(b),D):62===b&&4===d?(a.consume(b),H):63===b&&3===d?(a.consume(b),G):93===b&&5===d?(a.consume(b),F):Z(b)&&(6===d||7===d)?(a.exit("htmlFlowData"),a.check(aH,I,z)(b)):null===b||Z(b)?(a.exit("htmlFlowData"),z(b)):(a.consume(b),y)}function z(b){return a.check(aI,A,I)(b)}function A(b){return a.enter("lineEnding"),a.consume(b),a.exit("lineEnding"),B}function B(b){return null===b||Z(b)?z(b):(a.enter("htmlFlowData"),y(b))}function C(b){return 45===b?(a.consume(b),G):y(b)}function D(b){return 47===b?(a.consume(b),f="",E):y(b)}function E(b){if(62===b){let c=f.toLowerCase();return aG.includes(c)?(a.consume(b),H):y(b)}return S(b)&&f.length<8?(a.consume(b),f+=String.fromCharCode(b),E):y(b)}function F(b){return 93===b?(a.consume(b),G):y(b)}function G(b){return 62===b?(a.consume(b),H):45===b&&2===d?(a.consume(b),G):y(b)}function H(b){return null===b||Z(b)?(a.exit("htmlFlowData"),I(b)):(a.consume(b),H)}function I(c){return a.exit("htmlFlow"),b(c)}}},61:aE,95:ar,96:aK,126:aK},a3={38:aO,92:aP},a4={[-5]:aQ,[-4]:aQ,[-3]:aQ,33:aW,38:aO,42:aY,60:[{name:"autolink",tokenize:function(a,b,c){let d=0;return function(b){return a.enter("autolink"),a.enter("autolinkMarker"),a.consume(b),a.exit("autolinkMarker"),a.enter("autolinkProtocol"),e};function e(b){return S(b)?(a.consume(b),f):64===b?c(b):h(b)}function f(b){return 43===b||45===b||46===b||T(b)?(d=1,function b(c){return 58===c?(a.consume(c),d=0,g):(43===c||45===c||46===c||T(c))&&d++<32?(a.consume(c),b):(d=0,h(c))}(b)):h(b)}function g(d){return 62===d?(a.exit("autolinkProtocol"),a.enter("autolinkMarker"),a.consume(d),a.exit("autolinkMarker"),a.exit("autolink"),b):null===d||32===d||60===d||V(d)?c(d):(a.consume(d),g)}function h(b){return 64===b?(a.consume(b),i):U(b)?(a.consume(b),h):c(b)}function i(e){return T(e)?function e(f){return 46===f?(a.consume(f),d=0,i):62===f?(a.exit("autolinkProtocol").type="autolinkEmail",a.enter("autolinkMarker"),a.consume(f),a.exit("autolinkMarker"),a.exit("autolink"),b):function b(f){if((45===f||T(f))&&d++<63){let c=45===f?b:e;return a.consume(f),c}return c(f)}(f)}(e):c(e)}}},{name:"htmlText",tokenize:function(a,b,c){let d,e,f,g=this;return function(b){return a.enter("htmlText"),a.enter("htmlTextData"),a.consume(b),h};function h(b){return 33===b?(a.consume(b),i):47===b?(a.consume(b),u):63===b?(a.consume(b),s):S(b)?(a.consume(b),w):c(b)}function i(b){return 45===b?(a.consume(b),j):91===b?(a.consume(b),e=0,n):S(b)?(a.consume(b),r):c(b)}function j(b){return 45===b?(a.consume(b),m):c(b)}function k(b){return null===b?c(b):45===b?(a.consume(b),l):Z(b)?(f=k,E(b)):(a.consume(b),k)}function l(b){return 45===b?(a.consume(b),m):k(b)}function m(a){return 62===a?D(a):45===a?l(a):k(a)}function n(b){let d="CDATA[";return b===d.charCodeAt(e++)?(a.consume(b),e===d.length?o:n):c(b)}function o(b){return null===b?c(b):93===b?(a.consume(b),p):Z(b)?(f=o,E(b)):(a.consume(b),o)}function p(b){return 93===b?(a.consume(b),q):o(b)}function q(b){return 62===b?D(b):93===b?(a.consume(b),q):o(b)}function r(b){return null===b||62===b?D(b):Z(b)?(f=r,E(b)):(a.consume(b),r)}function s(b){return null===b?c(b):63===b?(a.consume(b),t):Z(b)?(f=s,E(b)):(a.consume(b),s)}function t(a){return 62===a?D(a):s(a)}function u(b){return S(b)?(a.consume(b),v):c(b)}function v(b){return 45===b||T(b)?(a.consume(b),v):function b(c){return Z(c)?(f=b,E(c)):_(c)?(a.consume(c),b):D(c)}(b)}function w(b){return 45===b||T(b)?(a.consume(b),w):47===b||62===b||$(b)?x(b):c(b)}function x(b){return 47===b?(a.consume(b),D):58===b||95===b||S(b)?(a.consume(b),y):Z(b)?(f=x,E(b)):_(b)?(a.consume(b),x):D(b)}function y(b){return 45===b||46===b||58===b||95===b||T(b)?(a.consume(b),y):function b(c){return 61===c?(a.consume(c),z):Z(c)?(f=b,E(c)):_(c)?(a.consume(c),b):x(c)}(b)}function z(b){return null===b||60===b||61===b||62===b||96===b?c(b):34===b||39===b?(a.consume(b),d=b,A):Z(b)?(f=z,E(b)):_(b)?(a.consume(b),z):(a.consume(b),B)}function A(b){return b===d?(a.consume(b),d=void 0,C):null===b?c(b):Z(b)?(f=A,E(b)):(a.consume(b),A)}function B(b){return null===b||34===b||39===b||60===b||61===b||96===b?c(b):47===b||62===b||$(b)?x(b):(a.consume(b),B)}function C(a){return 47===a||62===a||$(a)?x(a):c(a)}function D(d){return 62===d?(a.consume(d),a.exit("htmlTextData"),a.exit("htmlText"),b):c(d)}function E(b){return a.exit("htmlTextData"),a.enter("lineEnding"),a.consume(b),a.exit("lineEnding"),F}function F(b){return _(b)?ad(a,G,"linePrefix",g.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(b):G(b)}function G(b){return a.enter("htmlTextData"),f(b)}}}],91:a$,92:[{name:"hardBreakEscape",tokenize:function(a,b,c){return function(b){return a.enter("hardBreakEscape"),a.consume(b),d};function d(d){return Z(d)?(a.exit("hardBreakEscape"),b(d)):c(d)}}},aP],93:aS,95:aY,96:{name:"codeText",previous:function(a){return 96!==a||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(a){let b,c,d=a.length-4,e=3;if(("lineEnding"===a[3][1].type||"space"===a[e][1].type)&&("lineEnding"===a[d][1].type||"space"===a[d][1].type)){for(b=e;++b<d;)if("codeTextData"===a[b][1].type){a[e][1].type="codeTextPadding",a[d][1].type="codeTextPadding",e+=2,d-=2;break}}for(b=e-1,d++;++b<=d;)void 0===c?b!==d&&"lineEnding"!==a[b][1].type&&(c=b):(b===d||"lineEnding"===a[b][1].type)&&(a[c][1].type="codeTextData",b!==c+2&&(a[c][1].end=a[b-1][1].end,a.splice(c+2,b-c-2),d-=b-c-2,b=c+2),c=void 0);return a},tokenize:function(a,b,c){let d,e,f=0;return function(b){return a.enter("codeText"),a.enter("codeTextSequence"),function b(c){return 96===c?(a.consume(c),f++,b):(a.exit("codeTextSequence"),g(c))}(b)};function g(i){return null===i?c(i):32===i?(a.enter("space"),a.consume(i),a.exit("space"),g):96===i?(e=a.enter("codeTextSequence"),d=0,function c(g){return 96===g?(a.consume(g),d++,c):d===f?(a.exit("codeTextSequence"),a.exit("codeText"),b(g)):(e.type="codeTextData",h(g))}(i)):Z(i)?(a.enter("lineEnding"),a.consume(i),a.exit("lineEnding"),g):(a.enter("codeTextData"),h(i))}function h(b){return null===b||32===b||96===b||Z(b)?(a.exit("codeTextData"),g(b)):(a.consume(b),h)}}}},a5={null:[aY,al]},a6={null:[42,95]},a7={null:[]},a8=/[\0\t\n\r]/g;function a9(a,b){let c=Number.parseInt(a,b);return c<9||11===c||c>13&&c<32||c>126&&c<160||c>55295&&c<57344||c>64975&&c<65008||(65535&c)==65535||(65535&c)==65534||c>1114111?"�":String.fromCodePoint(c)}let ba=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function bb(a,b,c){if(b)return b;if(35===c.charCodeAt(0)){let a=c.charCodeAt(1),b=120===a||88===a;return a9(c.slice(b?2:1),b?16:10)}return aN(c)||a}let bc={}.hasOwnProperty;function bd(a){return{line:a.line,column:a.column,offset:a.offset}}function be(a,b){if(a)throw Error("Cannot close `"+a.type+"` ("+j({start:a.start,end:a.end})+"): a different token (`"+b.type+"`, "+j({start:b.start,end:b.end})+") is open");throw Error("Cannot close document, a token (`"+b.type+"`, "+j({start:b.start,end:b.end})+") is still open")}function bf(a){let b=this;b.parser=function(c){var d,f;let g,h,i,k;return"string"!=typeof(d={...b.data("settings"),...a,extensions:b.data("micromarkExtensions")||[],mdastExtensions:b.data("fromMarkdownExtensions")||[]})&&(f=d,d=void 0),(function(a){let b={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:d(s),autolinkProtocol:k,autolinkEmail:k,atxHeading:d(p),blockQuote:d(function(){return{type:"blockquote",children:[]}}),characterEscape:k,characterReference:k,codeFenced:d(o),codeFencedFenceInfo:e,codeFencedFenceMeta:e,codeIndented:d(o,e),codeText:d(function(){return{type:"inlineCode",value:""}},e),codeTextData:k,data:k,codeFlowValue:k,definition:d(function(){return{type:"definition",identifier:"",label:null,title:null,url:""}}),definitionDestinationString:e,definitionLabelString:e,definitionTitleString:e,emphasis:d(function(){return{type:"emphasis",children:[]}}),hardBreakEscape:d(q),hardBreakTrailing:d(q),htmlFlow:d(r,e),htmlFlowData:k,htmlText:d(r,e),htmlTextData:k,image:d(function(){return{type:"image",title:null,url:"",alt:null}}),label:e,link:d(s),listItem:d(function(a){return{type:"listItem",spread:a._spread,checked:null,children:[]}}),listItemValue:function(a){this.data.expectingFirstListItemValue&&(this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(a),10),this.data.expectingFirstListItemValue=void 0)},listOrdered:d(t,function(){this.data.expectingFirstListItemValue=!0}),listUnordered:d(t),paragraph:d(function(){return{type:"paragraph",children:[]}}),reference:function(){this.data.referenceType="collapsed"},referenceString:e,resourceDestinationString:e,resourceTitleString:e,setextHeading:d(p),strong:d(function(){return{type:"strong",children:[]}}),thematicBreak:d(function(){return{type:"thematicBreak"}})},exit:{atxHeading:g(),atxHeadingSequence:function(a){let b=this.stack[this.stack.length-1];b.depth||(b.depth=this.sliceSerialize(a).length)},autolink:g(),autolinkEmail:function(a){l.call(this,a),this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(a)},autolinkProtocol:function(a){l.call(this,a),this.stack[this.stack.length-1].url=this.sliceSerialize(a)},blockQuote:g(),characterEscapeValue:l,characterReferenceMarkerHexadecimal:n,characterReferenceMarkerNumeric:n,characterReferenceValue:function(a){let b,c=this.sliceSerialize(a),d=this.data.characterReferenceType;d?(b=a9(c,"characterReferenceMarkerNumeric"===d?10:16),this.data.characterReferenceType=void 0):b=aN(c);let e=this.stack[this.stack.length-1];e.value+=b},characterReference:function(a){this.stack.pop().position.end=bd(a.end)},codeFenced:g(function(){let a=this.resume();this.stack[this.stack.length-1].value=a.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}),codeFencedFence:function(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)},codeFencedFenceInfo:function(){let a=this.resume();this.stack[this.stack.length-1].lang=a},codeFencedFenceMeta:function(){let a=this.resume();this.stack[this.stack.length-1].meta=a},codeFlowValue:l,codeIndented:g(function(){let a=this.resume();this.stack[this.stack.length-1].value=a.replace(/(\r?\n|\r)$/g,"")}),codeText:g(function(){let a=this.resume();this.stack[this.stack.length-1].value=a}),codeTextData:l,data:l,definition:g(),definitionDestinationString:function(){let a=this.resume();this.stack[this.stack.length-1].url=a},definitionLabelString:function(a){let b=this.resume(),c=this.stack[this.stack.length-1];c.label=b,c.identifier=aA(this.sliceSerialize(a)).toLowerCase()},definitionTitleString:function(){let a=this.resume();this.stack[this.stack.length-1].title=a},emphasis:g(),hardBreakEscape:g(m),hardBreakTrailing:g(m),htmlFlow:g(function(){let a=this.resume();this.stack[this.stack.length-1].value=a}),htmlFlowData:l,htmlText:g(function(){let a=this.resume();this.stack[this.stack.length-1].value=a}),htmlTextData:l,image:g(function(){let a=this.stack[this.stack.length-1];if(this.data.inReference){let b=this.data.referenceType||"shortcut";a.type+="Reference",a.referenceType=b,delete a.url,delete a.title}else delete a.identifier,delete a.label;this.data.referenceType=void 0}),label:function(){let a=this.stack[this.stack.length-1],b=this.resume(),c=this.stack[this.stack.length-1];this.data.inReference=!0,"link"===c.type?c.children=a.children:c.alt=b},labelText:function(a){let b=this.sliceSerialize(a),c=this.stack[this.stack.length-2];c.label=b.replace(ba,bb),c.identifier=aA(b).toLowerCase()},lineEnding:function(a){let c=this.stack[this.stack.length-1];if(this.data.atHardBreak){c.children[c.children.length-1].position.end=bd(a.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&b.canContainEols.includes(c.type)&&(k.call(this,a),l.call(this,a))},link:g(function(){let a=this.stack[this.stack.length-1];if(this.data.inReference){let b=this.data.referenceType||"shortcut";a.type+="Reference",a.referenceType=b,delete a.url,delete a.title}else delete a.identifier,delete a.label;this.data.referenceType=void 0}),listItem:g(),listOrdered:g(),listUnordered:g(),paragraph:g(),referenceString:function(a){let b=this.resume(),c=this.stack[this.stack.length-1];c.label=b,c.identifier=aA(this.sliceSerialize(a)).toLowerCase(),this.data.referenceType="full"},resourceDestinationString:function(){let a=this.resume();this.stack[this.stack.length-1].url=a},resourceTitleString:function(){let a=this.resume();this.stack[this.stack.length-1].title=a},resource:function(){this.data.inReference=void 0},setextHeading:g(function(){this.data.setextHeadingSlurpLineEnding=void 0}),setextHeadingLineSequence:function(a){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(a).codePointAt(0)?1:2},setextHeadingText:function(){this.data.setextHeadingSlurpLineEnding=!0},strong:g(),thematicBreak:g()}};!function a(b,c){let d=-1;for(;++d<c.length;){let e=c[d];Array.isArray(e)?a(b,e):function(a,b){let c;for(c in b)if(bc.call(b,c))switch(c){case"canContainEols":{let d=b[c];d&&a[c].push(...d);break}case"transforms":{let d=b[c];d&&a[c].push(...d);break}case"enter":case"exit":{let d=b[c];d&&Object.assign(a[c],d)}}}(b,e)}}(b,(a||{}).mdastExtensions||[]);let c={};return function(a){let d={type:"root",children:[]},g={stack:[d],tokenStack:[],config:b,enter:f,exit:h,buffer:e,resume:i,data:c},j=[],k=-1;for(;++k<a.length;)("listOrdered"===a[k][1].type||"listUnordered"===a[k][1].type)&&("enter"===a[k][0]?j.push(k):k=function(a,b,c){let d,e,f,g,h=b-1,i=-1,j=!1;for(;++h<=c;){let b=a[h];switch(b[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===b[0]?i++:i--,g=void 0;break;case"lineEndingBlank":"enter"===b[0]&&(!d||g||i||f||(f=h),g=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:g=void 0}if(!i&&"enter"===b[0]&&"listItemPrefix"===b[1].type||-1===i&&"exit"===b[0]&&("listUnordered"===b[1].type||"listOrdered"===b[1].type)){if(d){let g=h;for(e=void 0;g--;){let b=a[g];if("lineEnding"===b[1].type||"lineEndingBlank"===b[1].type){if("exit"===b[0])continue;e&&(a[e][1].type="lineEndingBlank",j=!0),b[1].type="lineEnding",e=g}else if("linePrefix"===b[1].type||"blockQuotePrefix"===b[1].type||"blockQuotePrefixWhitespace"===b[1].type||"blockQuoteMarker"===b[1].type||"listItemIndent"===b[1].type);else break}f&&(!e||f<e)&&(d._spread=!0),d.end=Object.assign({},e?a[e][1].start:b[1].end),a.splice(e||h,0,["exit",d,b[2]]),h++,c++}if("listItemPrefix"===b[1].type){let e={type:"listItem",_spread:!1,start:Object.assign({},b[1].start),end:void 0};d=e,a.splice(h,0,["enter",e,b[2]]),h++,c++,f=void 0,g=!0}}}return a[b][1]._spread=j,c}(a,j.pop(),k));for(k=-1;++k<a.length;){let c=b[a[k][0]];bc.call(c,a[k][1].type)&&c[a[k][1].type].call(Object.assign({sliceSerialize:a[k][2].sliceSerialize},g),a[k][1])}if(g.tokenStack.length>0){let a=g.tokenStack[g.tokenStack.length-1];(a[1]||be).call(g,void 0,a[0])}for(d.position={start:bd(a.length>0?a[0][1].start:{line:1,column:1,offset:0}),end:bd(a.length>0?a[a.length-2][1].end:{line:1,column:1,offset:0})},k=-1;++k<b.transforms.length;)d=b.transforms[k](d)||d;return d};function d(a,b){return function(c){f.call(this,a(c),c),b&&b.call(this,c)}}function e(){this.stack.push({type:"fragment",children:[]})}function f(a,b,c){this.stack[this.stack.length-1].children.push(a),this.stack.push(a),this.tokenStack.push([b,c||void 0]),a.position={start:bd(b.start),end:void 0}}function g(a){return function(b){a&&a.call(this,b),h.call(this,b)}}function h(a,b){let c=this.stack.pop(),d=this.tokenStack.pop();if(d)d[0].type!==a.type&&(b?b.call(this,a,d[0]):(d[1]||be).call(this,a,d[0]));else throw Error("Cannot close `"+a.type+"` ("+j({start:a.start,end:a.end})+"): it’s not open");c.position.end=bd(a.end)}function i(){return I(this.stack.pop())}function k(a){let b=this.stack[this.stack.length-1].children,c=b[b.length-1];c&&"text"===c.type||((c={type:"text",value:""}).position={start:bd(a.start),end:void 0},b.push(c)),this.stack.push(c)}function l(a){let b=this.stack.pop();b.value+=this.sliceSerialize(a),b.position.end=bd(a.end)}function m(){this.data.atHardBreak=!0}function n(a){this.data.characterReferenceType=a.type}function o(){return{type:"code",lang:null,meta:null,value:""}}function p(){return{type:"heading",depth:0,children:[]}}function q(){return{type:"break"}}function r(){return{type:"html",value:""}}function s(){return{type:"link",title:null,url:"",children:[]}}function t(a){return{type:"list",ordered:"listOrdered"===a.type,start:null,spread:a._spread,children:[]}}})(f)(function(a){for(;!P(a););return a}((function(a){let b={constructs:R([e,...(a||{}).extensions||[]]),content:c(ae),defined:[],document:c(af),flow:c(ak),lazy:{},string:c(am),text:c(an)};return b;function c(a){return function(c){return function(a,b,c){let d={_bufferIndex:-1,_index:0,line:c&&c.line||1,column:c&&c.column||1,offset:c&&c.offset||0},e={},f=[],g=[],h=[],i={attempt:o(function(a,b){p(a,b.from)}),check:o(n),consume:function(a){Z(a)?(d.line++,d.column=1,d.offset+=-3===a?2:1,q()):-1!==a&&(d.column++,d.offset++),d._bufferIndex<0?d._index++:(d._bufferIndex++,d._bufferIndex===g[d._index].length&&(d._bufferIndex=-1,d._index++)),j.previous=a},enter:function(a,b){let c=b||{};return c.type=a,c.start=m(),j.events.push(["enter",c,j]),h.push(c),c},exit:function(a){let b=h.pop();return b.end=m(),j.events.push(["exit",b,j]),b},interrupt:o(n,{interrupt:!0})},j={code:null,containerState:{},defineSkip:function(a){e[a.line]=a.column,q()},events:[],now:m,parser:a,previous:null,sliceSerialize:function(a,b){return function(a,b){let c,d=-1,e=[];for(;++d<a.length;){let f,g=a[d];if("string"==typeof g)f=g;else switch(g){case -5:f="\r";break;case -4:f="\n";break;case -3:f="\r\n";break;case -2:f=b?" ":"	";break;case -1:if(!b&&c)continue;f=" ";break;default:f=String.fromCharCode(g)}c=-2===g,e.push(f)}return e.join("")}(l(a),b)},sliceStream:l,write:function(a){return(g=M(g,a),function(){let a;for(;d._index<g.length;){let c=g[d._index];if("string"==typeof c)for(a=d._index,d._bufferIndex<0&&(d._bufferIndex=0);d._index===a&&d._bufferIndex<c.length;){var b;b=c.charCodeAt(d._bufferIndex),k=k(b)}else k=k(c)}}(),null!==g[g.length-1])?[]:(p(b,0),j.events=aR(f,j.events,j),j.events)}},k=b.tokenize.call(j,i);return b.resolveAll&&f.push(b),j;function l(a){return function(a,b){let c,d=b.start._index,e=b.start._bufferIndex,f=b.end._index,g=b.end._bufferIndex;if(d===f)c=[a[d].slice(e,g)];else{if(c=a.slice(d,f),e>-1){let a=c[0];"string"==typeof a?c[0]=a.slice(e):c.shift()}g>0&&c.push(a[f].slice(0,g))}return c}(g,a)}function m(){let{_bufferIndex:a,_index:b,line:c,column:e,offset:f}=d;return{_bufferIndex:a,_index:b,line:c,column:e,offset:f}}function n(a,b){b.restore()}function o(a,b){return function(c,e,f){var g;let k,l,n,o;return Array.isArray(c)?p(c):"tokenize"in c?p([c]):(g=c,function(a){let b=null!==a&&g[a],c=null!==a&&g.null;return p([...Array.isArray(b)?b:b?[b]:[],...Array.isArray(c)?c:c?[c]:[]])(a)});function p(a){return(k=a,l=0,0===a.length)?f:r(a[l])}function r(a){return function(c){return(o=function(){let a=m(),b=j.previous,c=j.currentConstruct,e=j.events.length,f=Array.from(h);return{from:e,restore:function(){d=a,j.previous=b,j.currentConstruct=c,j.events.length=e,h=f,q()}}}(),n=a,a.partial||(j.currentConstruct=a),a.name&&j.parser.constructs.disable.null.includes(a.name))?t(c):a.tokenize.call(b?Object.assign(Object.create(j),b):j,i,s,t)(c)}}function s(b){return a(n,o),e}function t(a){return(o.restore(),++l<k.length)?r(k[l]):f}}}function p(a,b){a.resolveAll&&!f.includes(a)&&f.push(a),a.resolve&&L(j.events,b,j.events.length-b,a.resolve(j.events.slice(b),j)),a.resolveTo&&(j.events=a.resolveTo(j.events,j))}function q(){d.line in e&&d.column<2&&(d.column=e[d.line],d.offset+=e[d.line]-1)}}(b,a,c)}}})(f).document().write((h=1,i="",k=!0,function(a,b,c){let d,e,f,j,l,m=[];for(a=i+("string"==typeof a?a.toString():new TextDecoder(b||void 0).decode(a)),f=0,i="",k&&(65279===a.charCodeAt(0)&&f++,k=void 0);f<a.length;){if(a8.lastIndex=f,j=(d=a8.exec(a))&&void 0!==d.index?d.index:a.length,l=a.charCodeAt(j),!d){i=a.slice(f);break}if(10===l&&f===j&&g)m.push(-3),g=void 0;else switch(g&&(m.push(-5),g=void 0),f<j&&(m.push(a.slice(f,j)),h+=j-f),l){case 0:m.push(65533),h++;break;case 9:for(e=4*Math.ceil(h/4),m.push(-2);h++<e;)m.push(-1);break;case 10:m.push(-4),h=1;break;default:g=!0,h=1}f=j+1}return c&&(g&&m.push(-5),i&&m.push(i),m.push(null)),m})(c,d,!0))))}}function bg(a,b){let c=String(a);if("string"!=typeof b)throw TypeError("Expected character");let d=0,e=c.indexOf(b);for(;-1!==e;)d++,e=c.indexOf(b,e+b.length);return d}let bh=function(a){var b,c;if(null==a)return bj;if("function"==typeof a)return bi(a);if("object"==typeof a){return Array.isArray(a)?function(a){let b=[],c=-1;for(;++c<a.length;)b[c]=bh(a[c]);return bi(function(...a){let c=-1;for(;++c<b.length;)if(b[c].apply(this,a))return!0;return!1})}(a):(b=a,bi(function(a){let c;for(c in b)if(a[c]!==b[c])return!1;return!0}))}if("string"==typeof a){return c=a,bi(function(a){return a&&a.type===c})}throw Error("Expected function, string, or object as test")};function bi(a){return function(b,c,d){return!!(function(a){return null!==a&&"object"==typeof a&&"type"in a}(b)&&a.call(this,b,"number"==typeof c?c:void 0,d||void 0))}}function bj(){return!0}let bk=[],bl="skip";function bm(a,b,c,d){let e;"function"==typeof b&&"function"!=typeof c?(d=c,c=b):e=b;let f=bh(e),g=d?-1:1;(function a(e,h,i){let j=e&&"object"==typeof e?e:{};if("string"==typeof j.type){let a="string"==typeof j.tagName?j.tagName:"string"==typeof j.name?j.name:void 0;Object.defineProperty(k,"name",{value:"node (\x1b[33m"+e.type+(a?"<"+a+">":"")+"\x1b[39m)"})}return k;function k(){var j;let k,l,m,n=bk;if((!b||f(e,h,i[i.length-1]||void 0))&&!1===(n=Array.isArray(j=c(e,i))?j:"number"==typeof j?[!0,j]:null==j?bk:[j])[0])return n;if("children"in e&&e.children&&e.children&&n[0]!==bl)for(l=(d?e.children.length:-1)+g,m=i.concat(e);l>-1&&l<e.children.length;){if(!1===(k=a(e.children[l],l,m)())[0])return k;l="number"==typeof k[1]?k[1]:l+g}return n}})(a,void 0,[])()}let bn="phrasing",bo=["autolink","link","image","label"];function bp(a){this.enter({type:"link",title:null,url:"",children:[]},a)}function bq(a){this.config.enter.autolinkProtocol.call(this,a)}function br(a){this.config.exit.autolinkProtocol.call(this,a)}function bs(a){this.config.exit.data.call(this,a);let b=this.stack[this.stack.length-1];b.type,b.url="http://"+this.sliceSerialize(a)}function bt(a){this.config.exit.autolinkEmail.call(this,a)}function bu(a){this.exit(a)}function bv(a){!function(a,b,c){let d=bh((c||{}).ignore||[]),e=function(a){let b=[];if(!Array.isArray(a))throw TypeError("Expected find and replace tuple or list of tuples");let c=!a[0]||Array.isArray(a[0])?a:[a],d=-1;for(;++d<c.length;){var e;let a=c[d];b.push(["string"==typeof(e=a[0])?RegExp(function(a){if("string"!=typeof a)throw TypeError("Expected a string");return a.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(e),"g"):e,function(a){return"function"==typeof a?a:function(){return a}}(a[1])])}return b}(b),f=-1;for(;++f<e.length;)bm(a,"text",g);function g(a,b){let c,g=-1;for(;++g<b.length;){let a=b[g],e=c?c.children:void 0;if(d(a,e?e.indexOf(a):void 0,c))return;c=a}if(c)return function(a,b){let c=b[b.length-1],d=e[f][0],g=e[f][1],h=0,i=c.children.indexOf(a),j=!1,k=[];d.lastIndex=0;let l=d.exec(a.value);for(;l;){let c=l.index,e={index:l.index,input:l.input,stack:[...b,a]},f=g(...l,e);if("string"==typeof f&&(f=f.length>0?{type:"text",value:f}:void 0),!1===f?d.lastIndex=c+1:(h!==c&&k.push({type:"text",value:a.value.slice(h,c)}),Array.isArray(f)?k.push(...f):f&&k.push(f),h=c+l[0].length,j=!0),!d.global)break;l=d.exec(a.value)}return j?(h<a.value.length&&k.push({type:"text",value:a.value.slice(h)}),c.children.splice(i,1,...k)):k=[a],i+k.length}(a,b)}}(a,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,bw],[/(?<=^|\s|\p{P}|\p{S})([-.\w+]+)@([-\w]+(?:\.[-\w]+)+)/gu,bx]],{ignore:["link","linkReference"]})}function bw(a,b,c,d,e){let f="";if(!by(e)||(/^w/i.test(b)&&(c=b+c,b="",f="http://"),!function(a){let b=a.split(".");return!(b.length<2||b[b.length-1]&&(/_/.test(b[b.length-1])||!/[a-zA-Z\d]/.test(b[b.length-1]))||b[b.length-2]&&(/_/.test(b[b.length-2])||!/[a-zA-Z\d]/.test(b[b.length-2])))}(c)))return!1;let g=function(a){let b=/[!"&'),.:;<>?\]}]+$/.exec(a);if(!b)return[a,void 0];a=a.slice(0,b.index);let c=b[0],d=c.indexOf(")"),e=bg(a,"("),f=bg(a,")");for(;-1!==d&&e>f;)a+=c.slice(0,d+1),d=(c=c.slice(d+1)).indexOf(")"),f++;return[a,c]}(c+d);if(!g[0])return!1;let h={type:"link",title:null,url:f+b+g[0],children:[{type:"text",value:b+g[0]}]};return g[1]?[h,{type:"text",value:g[1]}]:h}function bx(a,b,c,d){return!(!by(d,!0)||/[-\d_]$/.test(c))&&{type:"link",title:null,url:"mailto:"+b+"@"+c,children:[{type:"text",value:b+"@"+c}]}}function by(a,b){let c=a.input.charCodeAt(a.index-1);return(0===a.index||ab(c)||aa(c))&&(!b||47!==c)}function bz(){this.buffer()}function bA(a){this.enter({type:"footnoteReference",identifier:"",label:""},a)}function bB(){this.buffer()}function bC(a){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},a)}function bD(a){let b=this.resume(),c=this.stack[this.stack.length-1];c.type,c.identifier=aA(this.sliceSerialize(a)).toLowerCase(),c.label=b}function bE(a){this.exit(a)}function bF(a){let b=this.resume(),c=this.stack[this.stack.length-1];c.type,c.identifier=aA(this.sliceSerialize(a)).toLowerCase(),c.label=b}function bG(a){this.exit(a)}function bH(a,b,c,d){let e=c.createTracker(d),f=e.move("[^"),g=c.enter("footnoteReference"),h=c.enter("reference");return f+=e.move(c.safe(c.associationId(a),{after:"]",before:f})),h(),g(),f+=e.move("]")}function bI(a,b,c){return 0===b?a:bJ(a,b,c)}function bJ(a,b,c){return(c?"":"    ")+a}bH.peek=function(){return"["};let bK=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];function bL(a){this.enter({type:"delete",children:[]},a)}function bM(a){this.exit(a)}function bN(a,b,c,d){let e=c.createTracker(d),f=c.enter("strikethrough"),g=e.move("~~");return g+=c.containerPhrasing(a,{...e.current(),before:g,after:"~"}),g+=e.move("~~"),f(),g}function bO(a){return a.length}function bP(a){let b="string"==typeof a?a.codePointAt(0):0;return 67===b||99===b?99:76===b||108===b?108:114*(82===b||114===b)}bN.peek=function(){return"~"};function bQ(a,b,c,d){let e,f,g;"function"==typeof b&&"function"!=typeof c?(f=void 0,g=b,e=c):(f=b,g=c,e=d),bm(a,f,function(a,b){let c=b[b.length-1],d=c?c.children.indexOf(a):void 0;return g(a,d,c)},e)}function bR(a,b,c){let d=a.value||"",e="`",f=-1;for(;RegExp("(^|[^`])"+e+"([^`]|$)").test(d);)e+="`";for(/[^ \r\n]/.test(d)&&(/^[ \r\n]/.test(d)&&/[ \r\n]$/.test(d)||/^`|`$/.test(d))&&(d=" "+d+" ");++f<c.unsafe.length;){let a,b=c.unsafe[f],e=c.compilePattern(b);if(b.atBreak)for(;a=e.exec(d);){let b=a.index;10===d.charCodeAt(b)&&13===d.charCodeAt(b-1)&&b--,d=d.slice(0,b)+" "+d.slice(a.index+1)}}return e+d+e}bR.peek=function(){return"`"};bh(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);let bS={inlineCode:bR,listItem:function(a,b,c,d){let e=function(a){let b=a.options.listItemIndent||"one";if("tab"!==b&&"one"!==b&&"mixed"!==b)throw Error("Cannot serialize items with `"+b+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return b}(c),f=c.bulletCurrent||function(a){let b=a.options.bullet||"*";if("*"!==b&&"+"!==b&&"-"!==b)throw Error("Cannot serialize items with `"+b+"` for `options.bullet`, expected `*`, `+`, or `-`");return b}(c);b&&"list"===b.type&&b.ordered&&(f=("number"==typeof b.start&&b.start>-1?b.start:1)+(!1===c.options.incrementListMarker?0:b.children.indexOf(a))+f);let g=f.length+1;("tab"===e||"mixed"===e&&(b&&"list"===b.type&&b.spread||a.spread))&&(g=4*Math.ceil(g/4));let h=c.createTracker(d);h.move(f+" ".repeat(g-f.length)),h.shift(g);let i=c.enter("listItem"),j=c.indentLines(c.containerFlow(a,h.current()),function(a,b,c){return b?(c?"":" ".repeat(g))+a:(c?f:f+" ".repeat(g-f.length))+a});return i(),j}};function bT(a){let b=a._align;this.enter({type:"table",align:b.map(function(a){return"none"===a?null:a}),children:[]},a),this.data.inTable=!0}function bU(a){this.exit(a),this.data.inTable=void 0}function bV(a){this.enter({type:"tableRow",children:[]},a)}function bW(a){this.exit(a)}function bX(a){this.enter({type:"tableCell",children:[]},a)}function bY(a){let b=this.resume();this.data.inTable&&(b=b.replace(/\\([\\|])/g,bZ));let c=this.stack[this.stack.length-1];c.type,c.value=b,this.exit(a)}function bZ(a,b){return"|"===b?b:a}function b$(a){let b=this.stack[this.stack.length-2];b.type,b.checked="taskListCheckValueChecked"===a.type}function b_(a){let b=this.stack[this.stack.length-2];if(b&&"listItem"===b.type&&"boolean"==typeof b.checked){let a=this.stack[this.stack.length-1];a.type;let c=a.children[0];if(c&&"text"===c.type){let d,e=b.children,f=-1;for(;++f<e.length;){let a=e[f];if("paragraph"===a.type){d=a;break}}d===a&&(c.value=c.value.slice(1),0===c.value.length?a.children.shift():a.position&&c.position&&"number"==typeof c.position.start.offset&&(c.position.start.column++,c.position.start.offset++,a.position.start=Object.assign({},c.position.start)))}}this.exit(a)}function b0(a,b,c,d){let e=a.children[0],f="boolean"==typeof a.checked&&e&&"paragraph"===e.type,g="["+(a.checked?"x":" ")+"] ",h=c.createTracker(d);f&&h.move(g);let i=bS.listItem(a,b,c,{...d,...h.current()});return f&&(i=i.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,function(a){return a+g})),i}let b1={tokenize:function(a,b,c){let d=0;return function b(f){return(87===f||119===f)&&d<3?(d++,a.consume(f),b):46===f&&3===d?(a.consume(f),e):c(f)};function e(a){return null===a?c(a):b(a)}},partial:!0},b2={tokenize:function(a,b,c){let d,e,f;return g;function g(b){return 46===b||95===b?a.check(b4,i,h)(b):null===b||$(b)||ab(b)||45!==b&&aa(b)?i(b):(f=!0,a.consume(b),g)}function h(b){return 95===b?d=!0:(e=d,d=void 0),a.consume(b),g}function i(a){return e||d||!f?c(a):b(a)}},partial:!0},b3={tokenize:function(a,b){let c=0,d=0;return e;function e(g){return 40===g?(c++,a.consume(g),e):41===g&&d<c?f(g):33===g||34===g||38===g||39===g||41===g||42===g||44===g||46===g||58===g||59===g||60===g||63===g||93===g||95===g||126===g?a.check(b4,b,f)(g):null===g||$(g)||ab(g)?b(g):(a.consume(g),e)}function f(b){return 41===b&&d++,a.consume(b),e}},partial:!0},b4={tokenize:function(a,b,c){return d;function d(g){return 33===g||34===g||39===g||41===g||42===g||44===g||46===g||58===g||59===g||63===g||95===g||126===g?(a.consume(g),d):38===g?(a.consume(g),f):93===g?(a.consume(g),e):60===g||null===g||$(g)||ab(g)?b(g):c(g)}function e(a){return null===a||40===a||91===a||$(a)||ab(a)?b(a):d(a)}function f(b){return S(b)?function b(e){return 59===e?(a.consume(e),d):S(e)?(a.consume(e),b):c(e)}(b):c(b)}},partial:!0},b5={tokenize:function(a,b,c){return function(b){return a.consume(b),d};function d(a){return T(a)?c(a):b(a)}},partial:!0},b6={name:"wwwAutolink",tokenize:function(a,b,c){let d=this;return function(b){return 87!==b&&119!==b||!cb.call(d,d.previous)||cf(d.events)?c(b):(a.enter("literalAutolink"),a.enter("literalAutolinkWww"),a.check(b1,a.attempt(b2,a.attempt(b3,e),c),c)(b))};function e(c){return a.exit("literalAutolinkWww"),a.exit("literalAutolink"),b(c)}},previous:cb},b7={name:"protocolAutolink",tokenize:function(a,b,c){let d=this,e="",f=!1;return function(b){return(72===b||104===b)&&cc.call(d,d.previous)&&!cf(d.events)?(a.enter("literalAutolink"),a.enter("literalAutolinkHttp"),e+=String.fromCodePoint(b),a.consume(b),g):c(b)};function g(b){if(S(b)&&e.length<5)return e+=String.fromCodePoint(b),a.consume(b),g;if(58===b){let c=e.toLowerCase();if("http"===c||"https"===c)return a.consume(b),h}return c(b)}function h(b){return 47===b?(a.consume(b),f)?i:(f=!0,h):c(b)}function i(b){return null===b||V(b)||$(b)||ab(b)||aa(b)?c(b):a.attempt(b2,a.attempt(b3,j),c)(b)}function j(c){return a.exit("literalAutolinkHttp"),a.exit("literalAutolink"),b(c)}},previous:cc},b8={name:"emailAutolink",tokenize:function(a,b,c){let d,e,f=this;return function(b){return!ce(b)||!cd.call(f,f.previous)||cf(f.events)?c(b):(a.enter("literalAutolink"),a.enter("literalAutolinkEmail"),function b(d){return ce(d)?(a.consume(d),b):64===d?(a.consume(d),g):c(d)}(b))};function g(b){return 46===b?a.check(b5,i,h)(b):45===b||95===b||T(b)?(e=!0,a.consume(b),g):i(b)}function h(b){return a.consume(b),d=!0,g}function i(g){return e&&d&&S(f.previous)?(a.exit("literalAutolinkEmail"),a.exit("literalAutolink"),b(g)):c(g)}},previous:cd},b9={},ca=48;for(;ca<123;)b9[ca]=b8,58==++ca?ca=65:91===ca&&(ca=97);function cb(a){return null===a||40===a||42===a||95===a||91===a||93===a||126===a||$(a)}function cc(a){return!S(a)}function cd(a){return!(47===a||ce(a))}function ce(a){return 43===a||45===a||46===a||95===a||T(a)}function cf(a){let b=a.length,c=!1;for(;b--;){let d=a[b][1];if(("labelLink"===d.type||"labelImage"===d.type)&&!d._balanced){c=!0;break}if(d._gfmAutolinkLiteralWalkedInto){c=!1;break}}return a.length>0&&!c&&(a[a.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),c}b9[43]=b8,b9[45]=b8,b9[46]=b8,b9[95]=b8,b9[72]=[b8,b7],b9[104]=[b8,b7],b9[87]=[b8,b6],b9[119]=[b8,b6];let cg={tokenize:function(a,b,c){let d=this;return ad(a,function(a){let e=d.events[d.events.length-1];return e&&"gfmFootnoteDefinitionIndent"===e[1].type&&4===e[2].sliceSerialize(e[1],!0).length?b(a):c(a)},"gfmFootnoteDefinitionIndent",5)},partial:!0};function ch(a,b,c){let d,e=this,f=e.events.length,g=e.parser.gfmFootnotes||(e.parser.gfmFootnotes=[]);for(;f--;){let a=e.events[f][1];if("labelImage"===a.type){d=a;break}if("gfmFootnoteCall"===a.type||"labelLink"===a.type||"label"===a.type||"image"===a.type||"link"===a.type)break}return function(f){if(!d||!d._balanced)return c(f);let h=aA(e.sliceSerialize({start:d.end,end:e.now()}));return 94===h.codePointAt(0)&&g.includes(h.slice(1))?(a.enter("gfmFootnoteCallLabelMarker"),a.consume(f),a.exit("gfmFootnoteCallLabelMarker"),b(f)):c(f)}}function ci(a,b){let c=a.length;for(;c--;)if("labelImage"===a[c][1].type&&"enter"===a[c][0]){a[c][1];break}a[c+1][1].type="data",a[c+3][1].type="gfmFootnoteCallLabelMarker";let d={type:"gfmFootnoteCall",start:Object.assign({},a[c+3][1].start),end:Object.assign({},a[a.length-1][1].end)},e={type:"gfmFootnoteCallMarker",start:Object.assign({},a[c+3][1].end),end:Object.assign({},a[c+3][1].end)};e.end.column++,e.end.offset++,e.end._bufferIndex++;let f={type:"gfmFootnoteCallString",start:Object.assign({},e.end),end:Object.assign({},a[a.length-1][1].start)},g={type:"chunkString",contentType:"string",start:Object.assign({},f.start),end:Object.assign({},f.end)},h=[a[c+1],a[c+2],["enter",d,b],a[c+3],a[c+4],["enter",e,b],["exit",e,b],["enter",f,b],["enter",g,b],["exit",g,b],["exit",f,b],a[a.length-2],a[a.length-1],["exit",d,b]];return a.splice(c,a.length-c+1,...h),a}function cj(a,b,c){let d,e=this,f=e.parser.gfmFootnotes||(e.parser.gfmFootnotes=[]),g=0;return function(b){return a.enter("gfmFootnoteCall"),a.enter("gfmFootnoteCallLabelMarker"),a.consume(b),a.exit("gfmFootnoteCallLabelMarker"),h};function h(b){return 94!==b?c(b):(a.enter("gfmFootnoteCallMarker"),a.consume(b),a.exit("gfmFootnoteCallMarker"),a.enter("gfmFootnoteCallString"),a.enter("chunkString").contentType="string",i)}function i(h){if(g>999||93===h&&!d||null===h||91===h||$(h))return c(h);if(93===h){a.exit("chunkString");let d=a.exit("gfmFootnoteCallString");return f.includes(aA(e.sliceSerialize(d)))?(a.enter("gfmFootnoteCallLabelMarker"),a.consume(h),a.exit("gfmFootnoteCallLabelMarker"),a.exit("gfmFootnoteCall"),b):c(h)}return $(h)||(d=!0),g++,a.consume(h),92===h?j:i}function j(b){return 91===b||92===b||93===b?(a.consume(b),g++,i):i(b)}}function ck(a,b,c){let d,e,f=this,g=f.parser.gfmFootnotes||(f.parser.gfmFootnotes=[]),h=0;return function(b){return a.enter("gfmFootnoteDefinition")._container=!0,a.enter("gfmFootnoteDefinitionLabel"),a.enter("gfmFootnoteDefinitionLabelMarker"),a.consume(b),a.exit("gfmFootnoteDefinitionLabelMarker"),i};function i(b){return 94===b?(a.enter("gfmFootnoteDefinitionMarker"),a.consume(b),a.exit("gfmFootnoteDefinitionMarker"),a.enter("gfmFootnoteDefinitionLabelString"),a.enter("chunkString").contentType="string",j):c(b)}function j(b){if(h>999||93===b&&!e||null===b||91===b||$(b))return c(b);if(93===b){a.exit("chunkString");let c=a.exit("gfmFootnoteDefinitionLabelString");return d=aA(f.sliceSerialize(c)),a.enter("gfmFootnoteDefinitionLabelMarker"),a.consume(b),a.exit("gfmFootnoteDefinitionLabelMarker"),a.exit("gfmFootnoteDefinitionLabel"),l}return $(b)||(e=!0),h++,a.consume(b),92===b?k:j}function k(b){return 91===b||92===b||93===b?(a.consume(b),h++,j):j(b)}function l(b){return 58===b?(a.enter("definitionMarker"),a.consume(b),a.exit("definitionMarker"),g.includes(d)||g.push(d),ad(a,m,"gfmFootnoteDefinitionWhitespace")):c(b)}function m(a){return b(a)}}function cl(a,b,c){return a.check(ah,b,a.attempt(cg,b,c))}function cm(a){a.exit("gfmFootnoteDefinition")}class cn{constructor(){this.map=[]}add(a,b,c){!function(a,b,c,d){let e=0;if(0!==c||0!==d.length){for(;e<a.map.length;){if(a.map[e][0]===b){a.map[e][1]+=c,a.map[e][2].push(...d);return}e+=1}a.map.push([b,c,d])}}(this,a,b,c)}consume(a){if(this.map.sort(function(a,b){return a[0]-b[0]}),0===this.map.length)return;let b=this.map.length,c=[];for(;b>0;)b-=1,c.push(a.slice(this.map[b][0]+this.map[b][1]),this.map[b][2]),a.length=this.map[b][0];c.push(a.slice()),a.length=0;let d=c.pop();for(;d;){for(let b of d)a.push(b);d=c.pop()}this.map.length=0}}function co(a,b,c){let d,e=this,f=0,g=0;return function(a){let b=e.events.length-1;for(;b>-1;){let a=e.events[b][1].type;if("lineEnding"===a||"linePrefix"===a)b--;else break}let d=b>-1?e.events[b][1].type:null,f="tableHead"===d||"tableRow"===d?s:h;return f===s&&e.parser.lazy[e.now().line]?c(a):f(a)};function h(b){var c;return a.enter("tableHead"),a.enter("tableRow"),124===(c=b)||(d=!0,g+=1),i(c)}function i(b){return null===b?c(b):Z(b)?g>1?(g=0,e.interrupt=!0,a.exit("tableRow"),a.enter("lineEnding"),a.consume(b),a.exit("lineEnding"),l):c(b):_(b)?ad(a,i,"whitespace")(b):(g+=1,d&&(d=!1,f+=1),124===b)?(a.enter("tableCellDivider"),a.consume(b),a.exit("tableCellDivider"),d=!0,i):(a.enter("data"),j(b))}function j(b){return null===b||124===b||$(b)?(a.exit("data"),i(b)):(a.consume(b),92===b?k:j)}function k(b){return 92===b||124===b?(a.consume(b),j):j(b)}function l(b){return(e.interrupt=!1,e.parser.lazy[e.now().line])?c(b):(a.enter("tableDelimiterRow"),d=!1,_(b))?ad(a,m,"linePrefix",e.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(b):m(b)}function m(b){return 45===b||58===b?o(b):124===b?(d=!0,a.enter("tableCellDivider"),a.consume(b),a.exit("tableCellDivider"),n):c(b)}function n(b){return _(b)?ad(a,o,"whitespace")(b):o(b)}function o(b){return 58===b?(g+=1,d=!0,a.enter("tableDelimiterMarker"),a.consume(b),a.exit("tableDelimiterMarker"),p):45===b?(g+=1,p(b)):null===b||Z(b)?r(b):c(b)}function p(b){return 45===b?(a.enter("tableDelimiterFiller"),function b(c){return 45===c?(a.consume(c),b):58===c?(d=!0,a.exit("tableDelimiterFiller"),a.enter("tableDelimiterMarker"),a.consume(c),a.exit("tableDelimiterMarker"),q):(a.exit("tableDelimiterFiller"),q(c))}(b)):c(b)}function q(b){return _(b)?ad(a,r,"whitespace")(b):r(b)}function r(e){if(124===e)return m(e);if(null===e||Z(e))return d&&f===g?(a.exit("tableDelimiterRow"),a.exit("tableHead"),b(e)):c(e);return c(e)}function s(b){return a.enter("tableRow"),t(b)}function t(c){return 124===c?(a.enter("tableCellDivider"),a.consume(c),a.exit("tableCellDivider"),t):null===c||Z(c)?(a.exit("tableRow"),b(c)):_(c)?ad(a,t,"whitespace")(c):(a.enter("data"),u(c))}function u(b){return null===b||124===b||$(b)?(a.exit("data"),t(b)):(a.consume(b),92===b?v:u)}function v(b){return 92===b||124===b?(a.consume(b),u):u(b)}}function cp(a,b){let c,d,e,f=-1,g=!0,h=0,i=[0,0,0,0],j=[0,0,0,0],k=!1,l=0,m=new cn;for(;++f<a.length;){let n=a[f],o=n[1];"enter"===n[0]?"tableHead"===o.type?(k=!1,0!==l&&(cr(m,b,l,c,d),d=void 0,l=0),c={type:"table",start:Object.assign({},o.start),end:Object.assign({},o.end)},m.add(f,0,[["enter",c,b]])):"tableRow"===o.type||"tableDelimiterRow"===o.type?(g=!0,e=void 0,i=[0,0,0,0],j=[0,f+1,0,0],k&&(k=!1,d={type:"tableBody",start:Object.assign({},o.start),end:Object.assign({},o.end)},m.add(f,0,[["enter",d,b]])),h="tableDelimiterRow"===o.type?2:d?3:1):h&&("data"===o.type||"tableDelimiterMarker"===o.type||"tableDelimiterFiller"===o.type)?(g=!1,0===j[2]&&(0!==i[1]&&(j[0]=j[1],e=cq(m,b,i,h,void 0,e),i=[0,0,0,0]),j[2]=f)):"tableCellDivider"===o.type&&(g?g=!1:(0!==i[1]&&(j[0]=j[1],e=cq(m,b,i,h,void 0,e)),j=[(i=j)[1],f,0,0])):"tableHead"===o.type?(k=!0,l=f):"tableRow"===o.type||"tableDelimiterRow"===o.type?(l=f,0!==i[1]?(j[0]=j[1],e=cq(m,b,i,h,f,e)):0!==j[1]&&(e=cq(m,b,j,h,f,e)),h=0):h&&("data"===o.type||"tableDelimiterMarker"===o.type||"tableDelimiterFiller"===o.type)&&(j[3]=f)}for(0!==l&&cr(m,b,l,c,d),m.consume(b.events),f=-1;++f<b.events.length;){let a=b.events[f];"enter"===a[0]&&"table"===a[1].type&&(a[1]._align=function(a,b){let c=!1,d=[];for(;b<a.length;){let e=a[b];if(c){if("enter"===e[0])"tableContent"===e[1].type&&d.push("tableDelimiterMarker"===a[b+1][1].type?"left":"none");else if("tableContent"===e[1].type){if("tableDelimiterMarker"===a[b-1][1].type){let a=d.length-1;d[a]="left"===d[a]?"center":"right"}}else if("tableDelimiterRow"===e[1].type)break}else"enter"===e[0]&&"tableDelimiterRow"===e[1].type&&(c=!0);b+=1}return d}(b.events,f))}return a}function cq(a,b,c,d,e,f){0!==c[0]&&(f.end=Object.assign({},cs(b.events,c[0])),a.add(c[0],0,[["exit",f,b]]));let g=cs(b.events,c[1]);if(f={type:1===d?"tableHeader":2===d?"tableDelimiter":"tableData",start:Object.assign({},g),end:Object.assign({},g)},a.add(c[1],0,[["enter",f,b]]),0!==c[2]){let e=cs(b.events,c[2]),f=cs(b.events,c[3]),g={type:"tableContent",start:Object.assign({},e),end:Object.assign({},f)};if(a.add(c[2],0,[["enter",g,b]]),2!==d){let d=b.events[c[2]],e=b.events[c[3]];if(d[1].end=Object.assign({},e[1].end),d[1].type="chunkText",d[1].contentType="text",c[3]>c[2]+1){let b=c[2]+1,d=c[3]-c[2]-1;a.add(b,d,[])}}a.add(c[3]+1,0,[["exit",g,b]])}return void 0!==e&&(f.end=Object.assign({},cs(b.events,e)),a.add(e,0,[["exit",f,b]]),f=void 0),f}function cr(a,b,c,d,e){let f=[],g=cs(b.events,c);e&&(e.end=Object.assign({},g),f.push(["exit",e,b])),d.end=Object.assign({},g),f.push(["exit",d,b]),a.add(c+1,0,f)}function cs(a,b){let c=a[b],d="enter"===c[0]?"start":"end";return c[1][d]}let ct={name:"tasklistCheck",tokenize:function(a,b,c){let d=this;return function(b){return null===d.previous&&d._gfmTasklistFirstContentOfListItem?(a.enter("taskListCheck"),a.enter("taskListCheckMarker"),a.consume(b),a.exit("taskListCheckMarker"),e):c(b)};function e(b){return $(b)?(a.enter("taskListCheckValueUnchecked"),a.consume(b),a.exit("taskListCheckValueUnchecked"),f):88===b||120===b?(a.enter("taskListCheckValueChecked"),a.consume(b),a.exit("taskListCheckValueChecked"),f):c(b)}function f(b){return 93===b?(a.enter("taskListCheckMarker"),a.consume(b),a.exit("taskListCheckMarker"),a.exit("taskListCheck"),g):c(b)}function g(d){return Z(d)?b(d):_(d)?a.check({tokenize:cu},b,c)(d):c(d)}}};function cu(a,b,c){return ad(a,function(a){return null===a?c(a):b(a)},"whitespace")}let cv={};function cw(a){let b,c=a||cv,d=this.data(),e=d.micromarkExtensions||(d.micromarkExtensions=[]),f=d.fromMarkdownExtensions||(d.fromMarkdownExtensions=[]),g=d.toMarkdownExtensions||(d.toMarkdownExtensions=[]);e.push(R([{text:b9},{document:{91:{name:"gfmFootnoteDefinition",tokenize:ck,continuation:{tokenize:cl},exit:cm}},text:{91:{name:"gfmFootnoteCall",tokenize:cj},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:ch,resolveTo:ci}}},function(a){let b=(a||{}).singleTilde,c={name:"strikethrough",tokenize:function(a,c,d){let e=this.previous,f=this.events,g=0;return function(h){return 126===e&&"characterEscape"!==f[f.length-1][1].type?d(h):(a.enter("strikethroughSequenceTemporary"),function f(h){let i=aX(e);if(126===h)return g>1?d(h):(a.consume(h),g++,f);if(g<2&&!b)return d(h);let j=a.exit("strikethroughSequenceTemporary"),k=aX(h);return j._open=!k||2===k&&!!i,j._close=!i||2===i&&!!k,c(h)}(h))}},resolveAll:function(a,b){let c=-1;for(;++c<a.length;)if("enter"===a[c][0]&&"strikethroughSequenceTemporary"===a[c][1].type&&a[c][1]._close){let d=c;for(;d--;)if("exit"===a[d][0]&&"strikethroughSequenceTemporary"===a[d][1].type&&a[d][1]._open&&a[c][1].end.offset-a[c][1].start.offset==a[d][1].end.offset-a[d][1].start.offset){a[c][1].type="strikethroughSequence",a[d][1].type="strikethroughSequence";let e={type:"strikethrough",start:Object.assign({},a[d][1].start),end:Object.assign({},a[c][1].end)},f={type:"strikethroughText",start:Object.assign({},a[d][1].end),end:Object.assign({},a[c][1].start)},g=[["enter",e,b],["enter",a[d][1],b],["exit",a[d][1],b],["enter",f,b]],h=b.parser.constructs.insideSpan.null;h&&L(g,g.length,0,aR(h,a.slice(d+1,c),b)),L(g,g.length,0,[["exit",f,b],["enter",a[c][1],b],["exit",a[c][1],b],["exit",e,b]]),L(a,d-1,c-d+3,g),c=d+g.length-2;break}}for(c=-1;++c<a.length;)"strikethroughSequenceTemporary"===a[c][1].type&&(a[c][1].type="data");return a}};return null==b&&(b=!0),{text:{126:c},insideSpan:{null:[c]},attentionMarkers:{null:[126]}}}(c),{flow:{null:{name:"table",tokenize:co,resolveAll:cp}}},{text:{91:ct}}])),f.push([{transforms:[bv],enter:{literalAutolink:bp,literalAutolinkEmail:bq,literalAutolinkHttp:bq,literalAutolinkWww:bq},exit:{literalAutolink:bu,literalAutolinkEmail:bt,literalAutolinkHttp:br,literalAutolinkWww:bs}},{enter:{gfmFootnoteCallString:bz,gfmFootnoteCall:bA,gfmFootnoteDefinitionLabelString:bB,gfmFootnoteDefinition:bC},exit:{gfmFootnoteCallString:bD,gfmFootnoteCall:bE,gfmFootnoteDefinitionLabelString:bF,gfmFootnoteDefinition:bG}},{canContainEols:["delete"],enter:{strikethrough:bL},exit:{strikethrough:bM}},{enter:{table:bT,tableData:bX,tableHeader:bX,tableRow:bV},exit:{codeText:bY,table:bU,tableData:bW,tableHeader:bW,tableRow:bW}},{exit:{taskListCheckValueChecked:b$,taskListCheckValueUnchecked:b$,paragraph:b_}}]),g.push({extensions:[{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:bn,notInConstruct:bo},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:bn,notInConstruct:bo},{character:":",before:"[ps]",after:"\\/",inConstruct:bn,notInConstruct:bo}]},(b=!1,c&&c.firstLineBlank&&(b=!0),{handlers:{footnoteDefinition:function(a,c,d,e){let f=d.createTracker(e),g=f.move("[^"),h=d.enter("footnoteDefinition"),i=d.enter("label");return g+=f.move(d.safe(d.associationId(a),{before:g,after:"]"})),i(),g+=f.move("]:"),a.children&&a.children.length>0&&(f.shift(4),g+=f.move((b?"\n":" ")+d.indentLines(d.containerFlow(a,f.current()),b?bJ:bI))),h(),g},footnoteReference:bH},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]}),{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:bK}],handlers:{delete:bN}},function(a){let b=a||{},c=b.tableCellPadding,d=b.tablePipeAlign,e=b.stringLength,f=c?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:"\n",inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:function(a,b,c){let d=bS.inlineCode(a,b,c);return c.stack.includes("tableCell")&&(d=d.replace(/\|/g,"\\$&")),d},table:function(a,b,c,d){return h(function(a,b,c){let d=a.children,e=-1,f=[],g=b.enter("table");for(;++e<d.length;)f[e]=i(d[e],b,c);return g(),f}(a,c,d),a.align)},tableCell:g,tableRow:function(a,b,c,d){let e=h([i(a,c,d)]);return e.slice(0,e.indexOf("\n"))}}};function g(a,b,c,d){let e=c.enter("tableCell"),g=c.enter("phrasing"),h=c.containerPhrasing(a,{...d,before:f,after:f});return g(),e(),h}function h(a,b){return function(a,b){let c=b||{},d=(c.align||[]).concat(),e=c.stringLength||bO,f=[],g=[],h=[],i=[],j=0,k=-1;for(;++k<a.length;){let b=[],d=[],f=-1;for(a[k].length>j&&(j=a[k].length);++f<a[k].length;){var l;let g=null==(l=a[k][f])?"":String(l);if(!1!==c.alignDelimiters){let a=e(g);d[f]=a,(void 0===i[f]||a>i[f])&&(i[f]=a)}b.push(g)}g[k]=b,h[k]=d}let m=-1;if("object"==typeof d&&"length"in d)for(;++m<j;)f[m]=bP(d[m]);else{let a=bP(d);for(;++m<j;)f[m]=a}m=-1;let n=[],o=[];for(;++m<j;){let a=f[m],b="",d="";99===a?(b=":",d=":"):108===a?b=":":114===a&&(d=":");let e=!1===c.alignDelimiters?1:Math.max(1,i[m]-b.length-d.length),g=b+"-".repeat(e)+d;!1!==c.alignDelimiters&&((e=b.length+e+d.length)>i[m]&&(i[m]=e),o[m]=e),n[m]=g}g.splice(1,0,n),h.splice(1,0,o),k=-1;let p=[];for(;++k<g.length;){let a=g[k],b=h[k];m=-1;let d=[];for(;++m<j;){let e=a[m]||"",g="",h="";if(!1!==c.alignDelimiters){let a=i[m]-(b[m]||0),c=f[m];114===c?g=" ".repeat(a):99===c?a%2?(g=" ".repeat(a/2+.5),h=" ".repeat(a/2-.5)):h=g=" ".repeat(a/2):h=" ".repeat(a)}!1===c.delimiterStart||m||d.push("|"),!1!==c.padding&&(!1!==c.alignDelimiters||""!==e)&&(!1!==c.delimiterStart||m)&&d.push(" "),!1!==c.alignDelimiters&&d.push(g),d.push(e),!1!==c.alignDelimiters&&d.push(h),!1!==c.padding&&d.push(" "),(!1!==c.delimiterEnd||m!==j-1)&&d.push("|")}p.push(!1===c.delimiterEnd?d.join("").replace(/ +$/,""):d.join(""))}return p.join("\n")}(a,{align:b,alignDelimiters:d,padding:c,stringLength:e})}function i(a,b,c){let d=a.children,e=-1,f=[],h=b.enter("tableRow");for(;++e<d.length;)f[e]=g(d[e],a,b,c);return h(),f}}(c),{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:b0}}]})}let cx="object"==typeof self?self:globalThis,cy=a=>((a,b)=>{let c=(b,c)=>(a.set(c,b),b),d=e=>{if(a.has(e))return a.get(e);let[f,g]=b[e];switch(f){case 0:case -1:return c(g,e);case 1:{let a=c([],e);for(let b of g)a.push(d(b));return a}case 2:{let a=c({},e);for(let[b,c]of g)a[d(b)]=d(c);return a}case 3:return c(new Date(g),e);case 4:{let{source:a,flags:b}=g;return c(new RegExp(a,b),e)}case 5:{let a=c(new Map,e);for(let[b,c]of g)a.set(d(b),d(c));return a}case 6:{let a=c(new Set,e);for(let b of g)a.add(d(b));return a}case 7:{let{name:a,message:b}=g;return c(new cx[a](b),e)}case 8:return c(BigInt(g),e);case"BigInt":return c(Object(BigInt(g)),e);case"ArrayBuffer":return c(new Uint8Array(g).buffer,g);case"DataView":{let{buffer:a}=new Uint8Array(g);return c(new DataView(a),g)}}return c(new cx[f](g),e)};return d})(new Map,a)(0),{toString:cz}={},{keys:cA}=Object,cB=a=>{let b=typeof a;if("object"!==b||!a)return[0,b];let c=cz.call(a).slice(8,-1);switch(c){case"Array":return[1,""];case"Object":return[2,""];case"Date":return[3,""];case"RegExp":return[4,""];case"Map":return[5,""];case"Set":return[6,""];case"DataView":return[1,c]}return c.includes("Array")?[1,c]:c.includes("Error")?[7,c]:[2,c]},cC=([a,b])=>0===a&&("function"===b||"symbol"===b),cD=(a,{json:b,lossy:c}={})=>{let d=[];return((a,b,c,d)=>{let e=(a,b)=>{let e=d.push(a)-1;return c.set(b,e),e},f=d=>{if(c.has(d))return c.get(d);let[g,h]=cB(d);switch(g){case 0:{let b=d;switch(h){case"bigint":g=8,b=d.toString();break;case"function":case"symbol":if(a)throw TypeError("unable to serialize "+h);b=null;break;case"undefined":return e([-1],d)}return e([g,b],d)}case 1:{if(h){let a=d;return"DataView"===h?a=new Uint8Array(d.buffer):"ArrayBuffer"===h&&(a=new Uint8Array(d)),e([h,[...a]],d)}let a=[],b=e([g,a],d);for(let b of d)a.push(f(b));return b}case 2:{if(h)switch(h){case"BigInt":return e([h,d.toString()],d);case"Boolean":case"Number":case"String":return e([h,d.valueOf()],d)}if(b&&"toJSON"in d)return f(d.toJSON());let c=[],i=e([g,c],d);for(let b of cA(d))(a||!cC(cB(d[b])))&&c.push([f(b),f(d[b])]);return i}case 3:return e([g,d.toISOString()],d);case 4:{let{source:a,flags:b}=d;return e([g,{source:a,flags:b}],d)}case 5:{let b=[],c=e([g,b],d);for(let[c,e]of d)(a||!(cC(cB(c))||cC(cB(e))))&&b.push([f(c),f(e)]);return c}case 6:{let b=[],c=e([g,b],d);for(let c of d)(a||!cC(cB(c)))&&b.push(f(c));return c}}let{message:i}=d;return e([g,{name:h,message:i}],d)};return f})(!(b||c),!!b,new Map,d)(a),d},cE="function"==typeof structuredClone?(a,b)=>b&&("json"in b||"lossy"in b)?cy(cD(a,b)):structuredClone(a):(a,b)=>cy(cD(a,b));function cF(a){let b=[],c=-1,d=0,e=0;for(;++c<a.length;){let f=a.charCodeAt(c),g="";if(37===f&&T(a.charCodeAt(c+1))&&T(a.charCodeAt(c+2)))e=2;else if(f<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(f))||(g=String.fromCharCode(f));else if(f>55295&&f<57344){let b=a.charCodeAt(c+1);f<56320&&b>56319&&b<57344?(g=String.fromCharCode(f,b),e=1):g="�"}else g=String.fromCharCode(f);g&&(b.push(a.slice(d,c),encodeURIComponent(g)),d=c+e+1,g=""),e&&(c+=e,e=0)}return b.join("")+a.slice(d)}function cG(a,b){let c=[{type:"text",value:"↩"}];return b>1&&c.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(b)}]}),c}function cH(a,b){return"Back to reference "+(a+1)+(b>1?"-"+b:"")}let cI=cK("end"),cJ=cK("start");function cK(a){return function(b){let c=b&&b.position&&b.position[a]||{};if("number"==typeof c.line&&c.line>0&&"number"==typeof c.column&&c.column>0)return{line:c.line,column:c.column,offset:"number"==typeof c.offset&&c.offset>-1?c.offset:void 0}}}function cL(a,b){let c=b.referenceType,d="]";if("collapsed"===c?d+="[]":"full"===c&&(d+="["+(b.label||b.identifier)+"]"),"imageReference"===b.type)return[{type:"text",value:"!["+b.alt+d}];let e=a.all(b),f=e[0];f&&"text"===f.type?f.value="["+f.value:e.unshift({type:"text",value:"["});let g=e[e.length-1];return g&&"text"===g.type?g.value+=d:e.push({type:"text",value:d}),e}function cM(a){let b=a.spread;return null==b?a.children.length>1:b}function cN(a,b,c){let d=0,e=a.length;if(b){let b=a.codePointAt(d);for(;9===b||32===b;)d++,b=a.codePointAt(d)}if(c){let b=a.codePointAt(e-1);for(;9===b||32===b;)e--,b=a.codePointAt(e-1)}return e>d?a.slice(d,e):""}let cO={blockquote:function(a,b){let c={type:"element",tagName:"blockquote",properties:{},children:a.wrap(a.all(b),!0)};return a.patch(b,c),a.applyData(b,c)},break:function(a,b){let c={type:"element",tagName:"br",properties:{},children:[]};return a.patch(b,c),[a.applyData(b,c),{type:"text",value:"\n"}]},code:function(a,b){let c=b.value?b.value+"\n":"",d={};b.lang&&(d.className=["language-"+b.lang]);let e={type:"element",tagName:"code",properties:d,children:[{type:"text",value:c}]};return b.meta&&(e.data={meta:b.meta}),a.patch(b,e),e={type:"element",tagName:"pre",properties:{},children:[e=a.applyData(b,e)]},a.patch(b,e),e},delete:function(a,b){let c={type:"element",tagName:"del",properties:{},children:a.all(b)};return a.patch(b,c),a.applyData(b,c)},emphasis:function(a,b){let c={type:"element",tagName:"em",properties:{},children:a.all(b)};return a.patch(b,c),a.applyData(b,c)},footnoteReference:function(a,b){let c,d="string"==typeof a.options.clobberPrefix?a.options.clobberPrefix:"user-content-",e=String(b.identifier).toUpperCase(),f=cF(e.toLowerCase()),g=a.footnoteOrder.indexOf(e),h=a.footnoteCounts.get(e);void 0===h?(h=0,a.footnoteOrder.push(e),c=a.footnoteOrder.length):c=g+1,h+=1,a.footnoteCounts.set(e,h);let i={type:"element",tagName:"a",properties:{href:"#"+d+"fn-"+f,id:d+"fnref-"+f+(h>1?"-"+h:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(c)}]};a.patch(b,i);let j={type:"element",tagName:"sup",properties:{},children:[i]};return a.patch(b,j),a.applyData(b,j)},heading:function(a,b){let c={type:"element",tagName:"h"+b.depth,properties:{},children:a.all(b)};return a.patch(b,c),a.applyData(b,c)},html:function(a,b){if(a.options.allowDangerousHtml){let c={type:"raw",value:b.value};return a.patch(b,c),a.applyData(b,c)}},imageReference:function(a,b){let c=String(b.identifier).toUpperCase(),d=a.definitionById.get(c);if(!d)return cL(a,b);let e={src:cF(d.url||""),alt:b.alt};null!==d.title&&void 0!==d.title&&(e.title=d.title);let f={type:"element",tagName:"img",properties:e,children:[]};return a.patch(b,f),a.applyData(b,f)},image:function(a,b){let c={src:cF(b.url)};null!==b.alt&&void 0!==b.alt&&(c.alt=b.alt),null!==b.title&&void 0!==b.title&&(c.title=b.title);let d={type:"element",tagName:"img",properties:c,children:[]};return a.patch(b,d),a.applyData(b,d)},inlineCode:function(a,b){let c={type:"text",value:b.value.replace(/\r?\n|\r/g," ")};a.patch(b,c);let d={type:"element",tagName:"code",properties:{},children:[c]};return a.patch(b,d),a.applyData(b,d)},linkReference:function(a,b){let c=String(b.identifier).toUpperCase(),d=a.definitionById.get(c);if(!d)return cL(a,b);let e={href:cF(d.url||"")};null!==d.title&&void 0!==d.title&&(e.title=d.title);let f={type:"element",tagName:"a",properties:e,children:a.all(b)};return a.patch(b,f),a.applyData(b,f)},link:function(a,b){let c={href:cF(b.url)};null!==b.title&&void 0!==b.title&&(c.title=b.title);let d={type:"element",tagName:"a",properties:c,children:a.all(b)};return a.patch(b,d),a.applyData(b,d)},listItem:function(a,b,c){let d=a.all(b),e=c?function(a){let b=!1;if("list"===a.type){b=a.spread||!1;let c=a.children,d=-1;for(;!b&&++d<c.length;)b=cM(c[d])}return b}(c):cM(b),f={},g=[];if("boolean"==typeof b.checked){let a,c=d[0];c&&"element"===c.type&&"p"===c.tagName?a=c:(a={type:"element",tagName:"p",properties:{},children:[]},d.unshift(a)),a.children.length>0&&a.children.unshift({type:"text",value:" "}),a.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:b.checked,disabled:!0},children:[]}),f.className=["task-list-item"]}let h=-1;for(;++h<d.length;){let a=d[h];(e||0!==h||"element"!==a.type||"p"!==a.tagName)&&g.push({type:"text",value:"\n"}),"element"!==a.type||"p"!==a.tagName||e?g.push(a):g.push(...a.children)}let i=d[d.length-1];i&&(e||"element"!==i.type||"p"!==i.tagName)&&g.push({type:"text",value:"\n"});let j={type:"element",tagName:"li",properties:f,children:g};return a.patch(b,j),a.applyData(b,j)},list:function(a,b){let c={},d=a.all(b),e=-1;for("number"==typeof b.start&&1!==b.start&&(c.start=b.start);++e<d.length;){let a=d[e];if("element"===a.type&&"li"===a.tagName&&a.properties&&Array.isArray(a.properties.className)&&a.properties.className.includes("task-list-item")){c.className=["contains-task-list"];break}}let f={type:"element",tagName:b.ordered?"ol":"ul",properties:c,children:a.wrap(d,!0)};return a.patch(b,f),a.applyData(b,f)},paragraph:function(a,b){let c={type:"element",tagName:"p",properties:{},children:a.all(b)};return a.patch(b,c),a.applyData(b,c)},root:function(a,b){let c={type:"root",children:a.wrap(a.all(b))};return a.patch(b,c),a.applyData(b,c)},strong:function(a,b){let c={type:"element",tagName:"strong",properties:{},children:a.all(b)};return a.patch(b,c),a.applyData(b,c)},table:function(a,b){let c=a.all(b),d=c.shift(),e=[];if(d){let c={type:"element",tagName:"thead",properties:{},children:a.wrap([d],!0)};a.patch(b.children[0],c),e.push(c)}if(c.length>0){let d={type:"element",tagName:"tbody",properties:{},children:a.wrap(c,!0)},f=cJ(b.children[1]),g=cI(b.children[b.children.length-1]);f&&g&&(d.position={start:f,end:g}),e.push(d)}let f={type:"element",tagName:"table",properties:{},children:a.wrap(e,!0)};return a.patch(b,f),a.applyData(b,f)},tableCell:function(a,b){let c={type:"element",tagName:"td",properties:{},children:a.all(b)};return a.patch(b,c),a.applyData(b,c)},tableRow:function(a,b,c){let d=c?c.children:void 0,e=0===(d?d.indexOf(b):1)?"th":"td",f=c&&"table"===c.type?c.align:void 0,g=f?f.length:b.children.length,h=-1,i=[];for(;++h<g;){let c=b.children[h],d={},g=f?f[h]:void 0;g&&(d.align=g);let j={type:"element",tagName:e,properties:d,children:[]};c&&(j.children=a.all(c),a.patch(c,j),j=a.applyData(c,j)),i.push(j)}let j={type:"element",tagName:"tr",properties:{},children:a.wrap(i,!0)};return a.patch(b,j),a.applyData(b,j)},text:function(a,b){let c={type:"text",value:function(a){let b=String(a),c=/\r?\n|\r/g,d=c.exec(b),e=0,f=[];for(;d;)f.push(cN(b.slice(e,d.index),e>0,!0),d[0]),e=d.index+d[0].length,d=c.exec(b);return f.push(cN(b.slice(e),e>0,!1)),f.join("")}(String(b.value))};return a.patch(b,c),a.applyData(b,c)},thematicBreak:function(a,b){let c={type:"element",tagName:"hr",properties:{},children:[]};return a.patch(b,c),a.applyData(b,c)},toml:cP,yaml:cP,definition:cP,footnoteDefinition:cP};function cP(){}let cQ={}.hasOwnProperty,cR={};function cS(a,b){a.position&&(b.position=function(a){let b=cJ(a),c=cI(a);if(b&&c)return{start:b,end:c}}(a))}function cT(a,b){let c=b;if(a&&a.data){let b=a.data.hName,d=a.data.hChildren,e=a.data.hProperties;"string"==typeof b&&("element"===c.type?c.tagName=b:c={type:"element",tagName:b,properties:{},children:"children"in c?c.children:[c]}),"element"===c.type&&e&&Object.assign(c.properties,cE(e)),"children"in c&&c.children&&null!=d&&(c.children=d)}return c}function cU(a,b){let c=[],d=-1;for(b&&c.push({type:"text",value:"\n"});++d<a.length;)d&&c.push({type:"text",value:"\n"}),c.push(a[d]);return b&&a.length>0&&c.push({type:"text",value:"\n"}),c}function cV(a){let b=0,c=a.charCodeAt(b);for(;9===c||32===c;)b++,c=a.charCodeAt(b);return a.slice(b)}function cW(a,b){let c=function(a,b){let c=b||cR,d=new Map,e=new Map,f={all:function(a){let b=[];if("children"in a){let c=a.children,d=-1;for(;++d<c.length;){let e=f.one(c[d],a);if(e){if(d&&"break"===c[d-1].type&&(Array.isArray(e)||"text"!==e.type||(e.value=cV(e.value)),!Array.isArray(e)&&"element"===e.type)){let a=e.children[0];a&&"text"===a.type&&(a.value=cV(a.value))}Array.isArray(e)?b.push(...e):b.push(e)}}}return b},applyData:cT,definitionById:d,footnoteById:e,footnoteCounts:new Map,footnoteOrder:[],handlers:{...cO,...c.handlers},one:function(a,b){let c=a.type,d=f.handlers[c];if(cQ.call(f.handlers,c)&&d)return d(f,a,b);if(f.options.passThrough&&f.options.passThrough.includes(c)){if("children"in a){let{children:b,...c}=a,d=cE(c);return d.children=f.all(a),d}return cE(a)}return(f.options.unknownHandler||function(a,b){let c=b.data||{},d="value"in b&&!(cQ.call(c,"hProperties")||cQ.call(c,"hChildren"))?{type:"text",value:b.value}:{type:"element",tagName:"div",properties:{},children:a.all(b)};return a.patch(b,d),a.applyData(b,d)})(f,a,b)},options:c,patch:cS,wrap:cU};return bQ(a,function(a){if("definition"===a.type||"footnoteDefinition"===a.type){let b="definition"===a.type?d:e,c=String(a.identifier).toUpperCase();b.has(c)||b.set(c,a)}}),f}(a,b),d=c.one(a,void 0),e=function(a){let b="string"==typeof a.options.clobberPrefix?a.options.clobberPrefix:"user-content-",c=a.options.footnoteBackContent||cG,d=a.options.footnoteBackLabel||cH,e=a.options.footnoteLabel||"Footnotes",f=a.options.footnoteLabelTagName||"h2",g=a.options.footnoteLabelProperties||{className:["sr-only"]},h=[],i=-1;for(;++i<a.footnoteOrder.length;){let e=a.footnoteById.get(a.footnoteOrder[i]);if(!e)continue;let f=a.all(e),g=String(e.identifier).toUpperCase(),j=cF(g.toLowerCase()),k=0,l=[],m=a.footnoteCounts.get(g);for(;void 0!==m&&++k<=m;){l.length>0&&l.push({type:"text",value:" "});let a="string"==typeof c?c:c(i,k);"string"==typeof a&&(a={type:"text",value:a}),l.push({type:"element",tagName:"a",properties:{href:"#"+b+"fnref-"+j+(k>1?"-"+k:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof d?d:d(i,k),className:["data-footnote-backref"]},children:Array.isArray(a)?a:[a]})}let n=f[f.length-1];if(n&&"element"===n.type&&"p"===n.tagName){let a=n.children[n.children.length-1];a&&"text"===a.type?a.value+=" ":n.children.push({type:"text",value:" "}),n.children.push(...l)}else f.push(...l);let o={type:"element",tagName:"li",properties:{id:b+"fn-"+j},children:a.wrap(f,!0)};a.patch(e,o),h.push(o)}if(0!==h.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:f,properties:{...cE(g),id:"footnote-label"},children:[{type:"text",value:e}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:a.wrap(h,!0)},{type:"text",value:"\n"}]}}(c),f=Array.isArray(d)?{type:"root",children:d}:d||{type:"root",children:[]};return e&&f.children.push({type:"text",value:"\n"},e),f}function cX(a,b){return a&&"run"in a?async function(c,d){let e=cW(c,{file:d,...b});await a.run(e,d)}:function(c,d){return cW(c,{file:d,...a||b})}}let cY=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g,cZ=Object.hasOwnProperty;class c${constructor(){this.occurrences,this.reset()}slug(a,b){var c,d;let e=(c=a,d=!0===b,"string"!=typeof c?"":(d||(c=c.toLowerCase()),c.replace(cY,"").replace(/ /g,"-"))),f=e;for(;cZ.call(this.occurrences,e);)this.occurrences[f]++,e=f+"-"+this.occurrences[f];return this.occurrences[e]=0,e}reset(){this.occurrences=Object.create(null)}}function c_(a){let b="element"===a.type?a.tagName.toLowerCase():"",c=2===b.length&&104===b.charCodeAt(0)?b.charCodeAt(1):0;return c>48&&c<55?c-48:void 0}let c0={},c1=new c$;function c2(a){let b=(a||c0).prefix||"";return function(a){c1.reset(),bQ(a,"element",function(a){c_(a)&&!a.properties.id&&(a.properties.id=b+c1.slug("children"in a?function a(b){let c=-1,d=[];for(;++c<b.children.length;){var e;d[c]="text"===(e=b.children[c]).type?e.value:"children"in e?a(e):""}return d.join("")}(a):"value"in a?a.value:""))})}}let c3=function(a){if(null==a)return c5;if("string"==typeof a){var b;return b=a,c4(function(a){return a.tagName===b})}if("object"==typeof a){var c=a;let b=[],d=-1;for(;++d<c.length;)b[d]=c3(c[d]);return c4(function(...a){let c=-1;for(;++c<b.length;)if(b[c].apply(this,a))return!0;return!1})}if("function"==typeof a)return c4(a);throw Error("Expected function, string, or array as `test`")};function c4(a){return function(b,c,d){var e;return!!(null!==(e=b)&&"object"==typeof e&&"type"in e&&"tagName"in e&&a.call(this,b,"number"==typeof c?c:void 0,d||void 0))}}function c5(a){return!!(a&&"object"==typeof a&&"type"in a&&"element"===a.type&&"tagName"in a&&"string"==typeof a.tagName)}let c6={type:"element",tagName:"span",properties:{className:["icon","icon-link"]},children:[]},c7={};function c8(a){let b,c=a||c7,d=c.properties,e=c.headingProperties,f=c.behavior||"prepend",g=c.content,h=c.group,i=c3(c.test);return"after"===f||"before"===f?b=function(a,b,c){if("number"!=typeof b||!c)return;let e=da(g||c6,a),i=c9(a,dc(d,a),e),j="before"===f?[i,a]:[a,i];if(h){let b=db(h,a);b&&!Array.isArray(b)&&"element"===b.type&&(b.children=j,j=[b])}return c.children.splice(b,1,...j),[bl,b+j.length]}:"wrap"===f?b=function(a){let b=a.children,c=[];return"function"==typeof g?(b=[],c=g(a)):g&&(c=cE(g)),a.children=[c9(a,dc(d,a),Array.isArray(c)?[...b,...c]:[...b,c])],[bl]}:(b=function(a){let b=da(g||c6,a);return a.children["prepend"===f?"unshift":"push"](c9(a,dc(d,a),b)),[bl]},d||(d={ariaHidden:"true",tabIndex:-1})),function(a){bQ(a,"element",function(a,c,d){if(c_(a)&&a.properties.id&&i(a,c,d))return Object.assign(a.properties,dc(e,a)),b(a,c,d)})}}function c9(a,b,c){return{type:"element",tagName:"a",properties:{...b,href:"#"+a.properties.id},children:c}}function da(a,b){let c=db(a,b);return Array.isArray(c)?c:[c]}function db(a,b){return"function"==typeof a?a(b):cE(a)}function dc(a,b){return"function"==typeof a?a(b):a?cE(a):{}}let dd=function(a,b,c){let d=bh(c);if(!a||!a.type||!a.children)throw Error("Expected parent node");if("number"==typeof b){if(b<0||b===1/0)throw Error("Expected positive finite number as index")}else if((b=a.children.indexOf(b))<0)throw Error("Expected child node or index");for(;++b<a.children.length;)if(d(a.children[b],b,a))return a.children[b]},de=/\n/g,df=/[\t ]+/g,dg=c3("br"),dh=c3(function(a){return"td"===a.tagName||"th"===a.tagName}),di=c3("p"),dj=c3("tr"),dk=c3(["datalist","head","noembed","noframes","noscript","rp","script","style","template","title",function(a){return!!(a.properties||{}).hidden},function(a){return"dialog"===a.tagName&&!(a.properties||{}).open}]),dl=c3(["address","article","aside","blockquote","body","caption","center","dd","dialog","dir","dl","dt","div","figure","figcaption","footer","form,","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","legend","li","listing","main","menu","nav","ol","p","plaintext","pre","section","ul","xmp"]);function dm(a,b){let c,d=String(a.value),e=[],f=[],g=0;for(;g<=d.length;){de.lastIndex=g;let a=de.exec(d),c=a&&"index"in a?a.index:d.length;e.push(function(a,b,c){let d,e=[],f=0;for(;f<a.length;){df.lastIndex=f;let c=df.exec(a);d=c?c.index:a.length,f||d||!c||b||e.push(""),f!==d&&e.push(a.slice(f,d)),f=c?d+c[0].length:d}return f===d||c||e.push(""),e.join(" ")}(d.slice(g,c).replace(/[\u061C\u200E\u200F\u202A-\u202E\u2066-\u2069]/g,""),0!==g||b.breakBefore,c!==d.length||b.breakAfter)),g=c+1}let h=-1;for(;++h<e.length;)8203===e[h].charCodeAt(e[h].length-1)||h<e.length-1&&8203===e[h+1].charCodeAt(0)?(f.push(e[h]),c=void 0):e[h]?("number"==typeof c&&f.push(c),f.push(e[h]),c=0):(0===h||h===e.length-1)&&f.push(0);return f}function dn(a,b){if("element"===a.type){let c=a.properties||{};switch(a.tagName){case"listing":case"plaintext":case"xmp":return"pre";case"nobr":return"nowrap";case"pre":return c.wrap?"pre-wrap":"pre";case"td":case"th":return c.noWrap?"nowrap":b.whitespace;case"textarea":return"pre-wrap"}}return b.whitespace}let dp=["a","abbr","address","article","aside","audio","b","blockquote","body","button","canvas","caption","cite","code","dd","del","details","dfn","div","dl","dt","em","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","html","i","iframe","img","input","ins","kbd","label","legend","li","main","mark","menu","nav","object","ol","optgroup","option","p","picture","q","quote","samp","section","select","source","span","strong","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","ul","var","video","defs","g","marker","mask","pattern","svg","switch","symbol","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feFlood","feGaussianBlur","feImage","feMerge","feMorphology","feOffset","feSpecularLighting","feTile","feTurbulence","linearGradient","radialGradient","stop","circle","ellipse","image","line","path","polygon","polyline","rect","text","use","textPath","tspan","foreignObject","clipPath"],dq=["any-hover","any-pointer","aspect-ratio","color","color-gamut","color-index","device-aspect-ratio","device-height","device-width","display-mode","forced-colors","grid","height","hover","inverted-colors","monochrome","orientation","overflow-block","overflow-inline","pointer","prefers-color-scheme","prefers-contrast","prefers-reduced-motion","prefers-reduced-transparency","resolution","scan","scripting","update","width","min-width","max-width","min-height","max-height"].sort().reverse(),dr=["active","any-link","blank","checked","current","default","defined","dir","disabled","drop","empty","enabled","first","first-child","first-of-type","fullscreen","future","focus","focus-visible","focus-within","has","host","host-context","hover","indeterminate","in-range","invalid","is","lang","last-child","last-of-type","left","link","local-link","not","nth-child","nth-col","nth-last-child","nth-last-col","nth-last-of-type","nth-of-type","only-child","only-of-type","optional","out-of-range","past","placeholder-shown","read-only","read-write","required","right","root","scope","target","target-within","user-invalid","valid","visited","where"].sort().reverse(),ds=["after","backdrop","before","cue","cue-region","first-letter","first-line","grammar-error","marker","part","placeholder","selection","slotted","spelling-error"].sort().reverse(),dt=["accent-color","align-content","align-items","align-self","alignment-baseline","all","anchor-name","animation","animation-composition","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-range","animation-range-end","animation-range-start","animation-timeline","animation-timing-function","appearance","aspect-ratio","backdrop-filter","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-position-x","background-position-y","background-repeat","background-size","baseline-shift","block-size","border","border-block","border-block-color","border-block-end","border-block-end-color","border-block-end-style","border-block-end-width","border-block-start","border-block-start-color","border-block-start-style","border-block-start-width","border-block-style","border-block-width","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-end-end-radius","border-end-start-radius","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-inline","border-inline-color","border-inline-end","border-inline-end-color","border-inline-end-style","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-style","border-inline-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-start-end-radius","border-start-start-radius","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-align","box-decoration-break","box-direction","box-flex","box-flex-group","box-lines","box-ordinal-group","box-orient","box-pack","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","color-scheme","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","contain","contain-intrinsic-block-size","contain-intrinsic-height","contain-intrinsic-inline-size","contain-intrinsic-size","contain-intrinsic-width","container","container-name","container-type","content","content-visibility","counter-increment","counter-reset","counter-set","cue","cue-after","cue-before","cursor","cx","cy","direction","display","dominant-baseline","empty-cells","enable-background","field-sizing","fill","fill-opacity","fill-rule","filter","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","flood-color","flood-opacity","flow","font","font-display","font-family","font-feature-settings","font-kerning","font-language-override","font-optical-sizing","font-palette","font-size","font-size-adjust","font-smooth","font-smoothing","font-stretch","font-style","font-synthesis","font-synthesis-position","font-synthesis-small-caps","font-synthesis-style","font-synthesis-weight","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-emoji","font-variant-ligatures","font-variant-numeric","font-variant-position","font-variation-settings","font-weight","forced-color-adjust","gap","glyph-orientation-horizontal","glyph-orientation-vertical","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphenate-character","hyphenate-limit-chars","hyphens","icon","image-orientation","image-rendering","image-resolution","ime-mode","initial-letter","initial-letter-align","inline-size","inset","inset-area","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","isolation","justify-content","justify-items","justify-self","kerning","left","letter-spacing","lighting-color","line-break","line-height","line-height-step","list-style","list-style-image","list-style-position","list-style-type","margin","margin-block","margin-block-end","margin-block-start","margin-bottom","margin-inline","margin-inline-end","margin-inline-start","margin-left","margin-right","margin-top","margin-trim","marker","marker-end","marker-mid","marker-start","marks","mask","mask-border","mask-border-mode","mask-border-outset","mask-border-repeat","mask-border-slice","mask-border-source","mask-border-width","mask-clip","mask-composite","mask-image","mask-mode","mask-origin","mask-position","mask-repeat","mask-size","mask-type","masonry-auto-flow","math-depth","math-shift","math-style","max-block-size","max-height","max-inline-size","max-width","min-block-size","min-height","min-inline-size","min-width","mix-blend-mode","nav-down","nav-index","nav-left","nav-right","nav-up","none","normal","object-fit","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-anchor","overflow-block","overflow-clip-margin","overflow-inline","overflow-wrap","overflow-x","overflow-y","overlay","overscroll-behavior","overscroll-behavior-block","overscroll-behavior-inline","overscroll-behavior-x","overscroll-behavior-y","padding","padding-block","padding-block-end","padding-block-start","padding-bottom","padding-inline","padding-inline-end","padding-inline-start","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","paint-order","pause","pause-after","pause-before","perspective","perspective-origin","place-content","place-items","place-self","pointer-events","position","position-anchor","position-visibility","print-color-adjust","quotes","r","resize","rest","rest-after","rest-before","right","rotate","row-gap","ruby-align","ruby-position","scale","scroll-behavior","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-align","scroll-snap-stop","scroll-snap-type","scroll-timeline","scroll-timeline-axis","scroll-timeline-name","scrollbar-color","scrollbar-gutter","scrollbar-width","shape-image-threshold","shape-margin","shape-outside","shape-rendering","speak","speak-as","src","stop-color","stop-opacity","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","table-layout","text-align","text-align-all","text-align-last","text-anchor","text-combine-upright","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-skip-ink","text-decoration-style","text-decoration-thickness","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-indent","text-justify","text-orientation","text-overflow","text-rendering","text-shadow","text-size-adjust","text-transform","text-underline-offset","text-underline-position","text-wrap","text-wrap-mode","text-wrap-style","timeline-scope","top","touch-action","transform","transform-box","transform-origin","transform-style","transition","transition-behavior","transition-delay","transition-duration","transition-property","transition-timing-function","translate","unicode-bidi","user-modify","user-select","vector-effect","vertical-align","view-timeline","view-timeline-axis","view-timeline-inset","view-timeline-name","view-transition-name","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","white-space","white-space-collapse","widows","width","will-change","word-break","word-spacing","word-wrap","writing-mode","x","y","z-index","zoom"].sort().reverse();var du="[0-9](_*[0-9])*",dv=`\\.(${du})`,dw="[0-9a-fA-F](_*[0-9a-fA-F])*",dx={className:"number",variants:[{begin:`(\\b(${du})((${dv})|\\.)?|(${dv}))[eE][+-]?(${du})[fFdD]?\\b`},{begin:`\\b(${du})((${dv})[fFdD]?\\b|\\.([fFdD]\\b)?)`},{begin:`(${dv})[fFdD]?\\b`},{begin:`\\b(${du})[fFdD]\\b`},{begin:`\\b0[xX]((${dw})\\.?|(${dw})?\\.(${dw}))[pP][+-]?(${du})[fFdD]?\\b`},{begin:"\\b(0|[1-9](_*[0-9])*)[lL]?\\b"},{begin:`\\b0[xX](${dw})[lL]?\\b`},{begin:"\\b0(_*[0-7])*[lL]?\\b"},{begin:"\\b0[bB][01](_*[01])*[lL]?\\b"}],relevance:0};let dy="[A-Za-z$_][0-9A-Za-z$_]*",dz=["as","in","of","if","for","while","finally","var","new","function","do","return","void","else","break","catch","instanceof","with","throw","case","default","try","switch","continue","typeof","delete","let","yield","const","class","debugger","async","await","static","import","from","export","extends","using"],dA=["true","false","null","undefined","NaN","Infinity"],dB=["Object","Function","Boolean","Symbol","Math","Date","Number","BigInt","String","RegExp","Array","Float32Array","Float64Array","Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Int32Array","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array","Set","Map","WeakSet","WeakMap","ArrayBuffer","SharedArrayBuffer","Atomics","DataView","JSON","Promise","Generator","GeneratorFunction","AsyncFunction","Reflect","Proxy","Intl","WebAssembly"],dC=["Error","EvalError","InternalError","RangeError","ReferenceError","SyntaxError","TypeError","URIError"],dD=["setInterval","setTimeout","clearInterval","clearTimeout","require","exports","eval","isFinite","isNaN","parseFloat","parseInt","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","escape","unescape"],dE=["arguments","this","super","console","window","document","localStorage","sessionStorage","module","global"],dF=[].concat(dD,dB,dC);var dG="[0-9](_*[0-9])*",dH=`\\.(${dG})`,dI="[0-9a-fA-F](_*[0-9a-fA-F])*",dJ={className:"number",variants:[{begin:`(\\b(${dG})((${dH})|\\.)?|(${dH}))[eE][+-]?(${dG})[fFdD]?\\b`},{begin:`\\b(${dG})((${dH})[fFdD]?\\b|\\.([fFdD]\\b)?)`},{begin:`(${dH})[fFdD]?\\b`},{begin:`\\b(${dG})[fFdD]\\b`},{begin:`\\b0[xX]((${dI})\\.?|(${dI})?\\.(${dI}))[pP][+-]?(${dG})[fFdD]?\\b`},{begin:"\\b(0|[1-9](_*[0-9])*)[lL]?\\b"},{begin:`\\b0[xX](${dI})[lL]?\\b`},{begin:"\\b0(_*[0-7])*[lL]?\\b"},{begin:"\\b0[bB][01](_*[01])*[lL]?\\b"}],relevance:0};let dK=["a","abbr","address","article","aside","audio","b","blockquote","body","button","canvas","caption","cite","code","dd","del","details","dfn","div","dl","dt","em","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","html","i","iframe","img","input","ins","kbd","label","legend","li","main","mark","menu","nav","object","ol","optgroup","option","p","picture","q","quote","samp","section","select","source","span","strong","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","ul","var","video","defs","g","marker","mask","pattern","svg","switch","symbol","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feFlood","feGaussianBlur","feImage","feMerge","feMorphology","feOffset","feSpecularLighting","feTile","feTurbulence","linearGradient","radialGradient","stop","circle","ellipse","image","line","path","polygon","polyline","rect","text","use","textPath","tspan","foreignObject","clipPath"],dL=["any-hover","any-pointer","aspect-ratio","color","color-gamut","color-index","device-aspect-ratio","device-height","device-width","display-mode","forced-colors","grid","height","hover","inverted-colors","monochrome","orientation","overflow-block","overflow-inline","pointer","prefers-color-scheme","prefers-contrast","prefers-reduced-motion","prefers-reduced-transparency","resolution","scan","scripting","update","width","min-width","max-width","min-height","max-height"].sort().reverse(),dM=["active","any-link","blank","checked","current","default","defined","dir","disabled","drop","empty","enabled","first","first-child","first-of-type","fullscreen","future","focus","focus-visible","focus-within","has","host","host-context","hover","indeterminate","in-range","invalid","is","lang","last-child","last-of-type","left","link","local-link","not","nth-child","nth-col","nth-last-child","nth-last-col","nth-last-of-type","nth-of-type","only-child","only-of-type","optional","out-of-range","past","placeholder-shown","read-only","read-write","required","right","root","scope","target","target-within","user-invalid","valid","visited","where"].sort().reverse(),dN=["after","backdrop","before","cue","cue-region","first-letter","first-line","grammar-error","marker","part","placeholder","selection","slotted","spelling-error"].sort().reverse(),dO=["accent-color","align-content","align-items","align-self","alignment-baseline","all","anchor-name","animation","animation-composition","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-range","animation-range-end","animation-range-start","animation-timeline","animation-timing-function","appearance","aspect-ratio","backdrop-filter","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-position-x","background-position-y","background-repeat","background-size","baseline-shift","block-size","border","border-block","border-block-color","border-block-end","border-block-end-color","border-block-end-style","border-block-end-width","border-block-start","border-block-start-color","border-block-start-style","border-block-start-width","border-block-style","border-block-width","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-end-end-radius","border-end-start-radius","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-inline","border-inline-color","border-inline-end","border-inline-end-color","border-inline-end-style","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-style","border-inline-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-start-end-radius","border-start-start-radius","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-align","box-decoration-break","box-direction","box-flex","box-flex-group","box-lines","box-ordinal-group","box-orient","box-pack","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","color-scheme","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","contain","contain-intrinsic-block-size","contain-intrinsic-height","contain-intrinsic-inline-size","contain-intrinsic-size","contain-intrinsic-width","container","container-name","container-type","content","content-visibility","counter-increment","counter-reset","counter-set","cue","cue-after","cue-before","cursor","cx","cy","direction","display","dominant-baseline","empty-cells","enable-background","field-sizing","fill","fill-opacity","fill-rule","filter","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","flood-color","flood-opacity","flow","font","font-display","font-family","font-feature-settings","font-kerning","font-language-override","font-optical-sizing","font-palette","font-size","font-size-adjust","font-smooth","font-smoothing","font-stretch","font-style","font-synthesis","font-synthesis-position","font-synthesis-small-caps","font-synthesis-style","font-synthesis-weight","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-emoji","font-variant-ligatures","font-variant-numeric","font-variant-position","font-variation-settings","font-weight","forced-color-adjust","gap","glyph-orientation-horizontal","glyph-orientation-vertical","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphenate-character","hyphenate-limit-chars","hyphens","icon","image-orientation","image-rendering","image-resolution","ime-mode","initial-letter","initial-letter-align","inline-size","inset","inset-area","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","isolation","justify-content","justify-items","justify-self","kerning","left","letter-spacing","lighting-color","line-break","line-height","line-height-step","list-style","list-style-image","list-style-position","list-style-type","margin","margin-block","margin-block-end","margin-block-start","margin-bottom","margin-inline","margin-inline-end","margin-inline-start","margin-left","margin-right","margin-top","margin-trim","marker","marker-end","marker-mid","marker-start","marks","mask","mask-border","mask-border-mode","mask-border-outset","mask-border-repeat","mask-border-slice","mask-border-source","mask-border-width","mask-clip","mask-composite","mask-image","mask-mode","mask-origin","mask-position","mask-repeat","mask-size","mask-type","masonry-auto-flow","math-depth","math-shift","math-style","max-block-size","max-height","max-inline-size","max-width","min-block-size","min-height","min-inline-size","min-width","mix-blend-mode","nav-down","nav-index","nav-left","nav-right","nav-up","none","normal","object-fit","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-anchor","overflow-block","overflow-clip-margin","overflow-inline","overflow-wrap","overflow-x","overflow-y","overlay","overscroll-behavior","overscroll-behavior-block","overscroll-behavior-inline","overscroll-behavior-x","overscroll-behavior-y","padding","padding-block","padding-block-end","padding-block-start","padding-bottom","padding-inline","padding-inline-end","padding-inline-start","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","paint-order","pause","pause-after","pause-before","perspective","perspective-origin","place-content","place-items","place-self","pointer-events","position","position-anchor","position-visibility","print-color-adjust","quotes","r","resize","rest","rest-after","rest-before","right","rotate","row-gap","ruby-align","ruby-position","scale","scroll-behavior","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-align","scroll-snap-stop","scroll-snap-type","scroll-timeline","scroll-timeline-axis","scroll-timeline-name","scrollbar-color","scrollbar-gutter","scrollbar-width","shape-image-threshold","shape-margin","shape-outside","shape-rendering","speak","speak-as","src","stop-color","stop-opacity","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","table-layout","text-align","text-align-all","text-align-last","text-anchor","text-combine-upright","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-skip-ink","text-decoration-style","text-decoration-thickness","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-indent","text-justify","text-orientation","text-overflow","text-rendering","text-shadow","text-size-adjust","text-transform","text-underline-offset","text-underline-position","text-wrap","text-wrap-mode","text-wrap-style","timeline-scope","top","touch-action","transform","transform-box","transform-origin","transform-style","transition","transition-behavior","transition-delay","transition-duration","transition-property","transition-timing-function","translate","unicode-bidi","user-modify","user-select","vector-effect","vertical-align","view-timeline","view-timeline-axis","view-timeline-inset","view-timeline-name","view-transition-name","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","white-space","white-space-collapse","widows","width","will-change","word-break","word-spacing","word-wrap","writing-mode","x","y","z-index","zoom"].sort().reverse(),dP=dM.concat(dN).sort().reverse(),dQ=["a","abbr","address","article","aside","audio","b","blockquote","body","button","canvas","caption","cite","code","dd","del","details","dfn","div","dl","dt","em","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","html","i","iframe","img","input","ins","kbd","label","legend","li","main","mark","menu","nav","object","ol","optgroup","option","p","picture","q","quote","samp","section","select","source","span","strong","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","ul","var","video","defs","g","marker","mask","pattern","svg","switch","symbol","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feFlood","feGaussianBlur","feImage","feMerge","feMorphology","feOffset","feSpecularLighting","feTile","feTurbulence","linearGradient","radialGradient","stop","circle","ellipse","image","line","path","polygon","polyline","rect","text","use","textPath","tspan","foreignObject","clipPath"],dR=["any-hover","any-pointer","aspect-ratio","color","color-gamut","color-index","device-aspect-ratio","device-height","device-width","display-mode","forced-colors","grid","height","hover","inverted-colors","monochrome","orientation","overflow-block","overflow-inline","pointer","prefers-color-scheme","prefers-contrast","prefers-reduced-motion","prefers-reduced-transparency","resolution","scan","scripting","update","width","min-width","max-width","min-height","max-height"].sort().reverse(),dS=["active","any-link","blank","checked","current","default","defined","dir","disabled","drop","empty","enabled","first","first-child","first-of-type","fullscreen","future","focus","focus-visible","focus-within","has","host","host-context","hover","indeterminate","in-range","invalid","is","lang","last-child","last-of-type","left","link","local-link","not","nth-child","nth-col","nth-last-child","nth-last-col","nth-last-of-type","nth-of-type","only-child","only-of-type","optional","out-of-range","past","placeholder-shown","read-only","read-write","required","right","root","scope","target","target-within","user-invalid","valid","visited","where"].sort().reverse(),dT=["after","backdrop","before","cue","cue-region","first-letter","first-line","grammar-error","marker","part","placeholder","selection","slotted","spelling-error"].sort().reverse(),dU=["accent-color","align-content","align-items","align-self","alignment-baseline","all","anchor-name","animation","animation-composition","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-range","animation-range-end","animation-range-start","animation-timeline","animation-timing-function","appearance","aspect-ratio","backdrop-filter","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-position-x","background-position-y","background-repeat","background-size","baseline-shift","block-size","border","border-block","border-block-color","border-block-end","border-block-end-color","border-block-end-style","border-block-end-width","border-block-start","border-block-start-color","border-block-start-style","border-block-start-width","border-block-style","border-block-width","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-end-end-radius","border-end-start-radius","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-inline","border-inline-color","border-inline-end","border-inline-end-color","border-inline-end-style","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-style","border-inline-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-start-end-radius","border-start-start-radius","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-align","box-decoration-break","box-direction","box-flex","box-flex-group","box-lines","box-ordinal-group","box-orient","box-pack","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","color-scheme","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","contain","contain-intrinsic-block-size","contain-intrinsic-height","contain-intrinsic-inline-size","contain-intrinsic-size","contain-intrinsic-width","container","container-name","container-type","content","content-visibility","counter-increment","counter-reset","counter-set","cue","cue-after","cue-before","cursor","cx","cy","direction","display","dominant-baseline","empty-cells","enable-background","field-sizing","fill","fill-opacity","fill-rule","filter","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","flood-color","flood-opacity","flow","font","font-display","font-family","font-feature-settings","font-kerning","font-language-override","font-optical-sizing","font-palette","font-size","font-size-adjust","font-smooth","font-smoothing","font-stretch","font-style","font-synthesis","font-synthesis-position","font-synthesis-small-caps","font-synthesis-style","font-synthesis-weight","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-emoji","font-variant-ligatures","font-variant-numeric","font-variant-position","font-variation-settings","font-weight","forced-color-adjust","gap","glyph-orientation-horizontal","glyph-orientation-vertical","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphenate-character","hyphenate-limit-chars","hyphens","icon","image-orientation","image-rendering","image-resolution","ime-mode","initial-letter","initial-letter-align","inline-size","inset","inset-area","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","isolation","justify-content","justify-items","justify-self","kerning","left","letter-spacing","lighting-color","line-break","line-height","line-height-step","list-style","list-style-image","list-style-position","list-style-type","margin","margin-block","margin-block-end","margin-block-start","margin-bottom","margin-inline","margin-inline-end","margin-inline-start","margin-left","margin-right","margin-top","margin-trim","marker","marker-end","marker-mid","marker-start","marks","mask","mask-border","mask-border-mode","mask-border-outset","mask-border-repeat","mask-border-slice","mask-border-source","mask-border-width","mask-clip","mask-composite","mask-image","mask-mode","mask-origin","mask-position","mask-repeat","mask-size","mask-type","masonry-auto-flow","math-depth","math-shift","math-style","max-block-size","max-height","max-inline-size","max-width","min-block-size","min-height","min-inline-size","min-width","mix-blend-mode","nav-down","nav-index","nav-left","nav-right","nav-up","none","normal","object-fit","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-anchor","overflow-block","overflow-clip-margin","overflow-inline","overflow-wrap","overflow-x","overflow-y","overlay","overscroll-behavior","overscroll-behavior-block","overscroll-behavior-inline","overscroll-behavior-x","overscroll-behavior-y","padding","padding-block","padding-block-end","padding-block-start","padding-bottom","padding-inline","padding-inline-end","padding-inline-start","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","paint-order","pause","pause-after","pause-before","perspective","perspective-origin","place-content","place-items","place-self","pointer-events","position","position-anchor","position-visibility","print-color-adjust","quotes","r","resize","rest","rest-after","rest-before","right","rotate","row-gap","ruby-align","ruby-position","scale","scroll-behavior","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-align","scroll-snap-stop","scroll-snap-type","scroll-timeline","scroll-timeline-axis","scroll-timeline-name","scrollbar-color","scrollbar-gutter","scrollbar-width","shape-image-threshold","shape-margin","shape-outside","shape-rendering","speak","speak-as","src","stop-color","stop-opacity","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","table-layout","text-align","text-align-all","text-align-last","text-anchor","text-combine-upright","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-skip-ink","text-decoration-style","text-decoration-thickness","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-indent","text-justify","text-orientation","text-overflow","text-rendering","text-shadow","text-size-adjust","text-transform","text-underline-offset","text-underline-position","text-wrap","text-wrap-mode","text-wrap-style","timeline-scope","top","touch-action","transform","transform-box","transform-origin","transform-style","transition","transition-behavior","transition-delay","transition-duration","transition-property","transition-timing-function","translate","unicode-bidi","user-modify","user-select","vector-effect","vertical-align","view-timeline","view-timeline-axis","view-timeline-inset","view-timeline-name","view-transition-name","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","white-space","white-space-collapse","widows","width","will-change","word-break","word-spacing","word-wrap","writing-mode","x","y","z-index","zoom"].sort().reverse();function dV(a){return a?"string"==typeof a?a:a.source:null}function dW(a){return dX("(?=",a,")")}function dX(...a){return a.map(a=>dV(a)).join("")}function dY(...a){return"("+(function(a){let b=a[a.length-1];return"object"==typeof b&&b.constructor===Object?(a.splice(a.length-1,1),b):{}}(a).capture?"":"?:")+a.map(a=>dV(a)).join("|")+")"}let dZ=a=>dX(/\b/,a,/\w$/.test(a)?/\b/:/\B/),d$=["Protocol","Type"].map(dZ),d_=["init","self"].map(dZ),d0=["Any","Self"],d1=["actor","any","associatedtype","async","await",/as\?/,/as!/,"as","borrowing","break","case","catch","class","consume","consuming","continue","convenience","copy","default","defer","deinit","didSet","distributed","do","dynamic","each","else","enum","extension","fallthrough",/fileprivate\(set\)/,"fileprivate","final","for","func","get","guard","if","import","indirect","infix",/init\?/,/init!/,"inout",/internal\(set\)/,"internal","in","is","isolated","nonisolated","lazy","let","macro","mutating","nonmutating",/open\(set\)/,"open","operator","optional","override","package","postfix","precedencegroup","prefix",/private\(set\)/,"private","protocol",/public\(set\)/,"public","repeat","required","rethrows","return","set","some","static","struct","subscript","super","switch","throws","throw",/try\?/,/try!/,"try","typealias",/unowned\(safe\)/,/unowned\(unsafe\)/,"unowned","var","weak","where","while","willSet"],d2=["false","nil","true"],d3=["assignment","associativity","higherThan","left","lowerThan","none","right"],d4=["#colorLiteral","#column","#dsohandle","#else","#elseif","#endif","#error","#file","#fileID","#fileLiteral","#filePath","#function","#if","#imageLiteral","#keyPath","#line","#selector","#sourceLocation","#warning"],d5=["abs","all","any","assert","assertionFailure","debugPrint","dump","fatalError","getVaList","isKnownUniquelyReferenced","max","min","numericCast","pointwiseMax","pointwiseMin","precondition","preconditionFailure","print","readLine","repeatElement","sequence","stride","swap","swift_unboxFromSwiftValueWithType","transcode","type","unsafeBitCast","unsafeDowncast","withExtendedLifetime","withUnsafeMutablePointer","withUnsafePointer","withVaList","withoutActuallyEscaping","zip"],d6=dY(/[/=\-+!*%<>&|^~?]/,/[\u00A1-\u00A7]/,/[\u00A9\u00AB]/,/[\u00AC\u00AE]/,/[\u00B0\u00B1]/,/[\u00B6\u00BB\u00BF\u00D7\u00F7]/,/[\u2016-\u2017]/,/[\u2020-\u2027]/,/[\u2030-\u203E]/,/[\u2041-\u2053]/,/[\u2055-\u205E]/,/[\u2190-\u23FF]/,/[\u2500-\u2775]/,/[\u2794-\u2BFF]/,/[\u2E00-\u2E7F]/,/[\u3001-\u3003]/,/[\u3008-\u3020]/,/[\u3030]/),d7=dY(d6,/[\u0300-\u036F]/,/[\u1DC0-\u1DFF]/,/[\u20D0-\u20FF]/,/[\uFE00-\uFE0F]/,/[\uFE20-\uFE2F]/),d8=dX(d6,d7,"*"),d9=dY(/[a-zA-Z_]/,/[\u00A8\u00AA\u00AD\u00AF\u00B2-\u00B5\u00B7-\u00BA]/,/[\u00BC-\u00BE\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u00FF]/,/[\u0100-\u02FF\u0370-\u167F\u1681-\u180D\u180F-\u1DBF]/,/[\u1E00-\u1FFF]/,/[\u200B-\u200D\u202A-\u202E\u203F-\u2040\u2054\u2060-\u206F]/,/[\u2070-\u20CF\u2100-\u218F\u2460-\u24FF\u2776-\u2793]/,/[\u2C00-\u2DFF\u2E80-\u2FFF]/,/[\u3004-\u3007\u3021-\u302F\u3031-\u303F\u3040-\uD7FF]/,/[\uF900-\uFD3D\uFD40-\uFDCF\uFDF0-\uFE1F\uFE30-\uFE44]/,/[\uFE47-\uFEFE\uFF00-\uFFFD]/),ea=dY(d9,/\d/,/[\u0300-\u036F\u1DC0-\u1DFF\u20D0-\u20FF\uFE20-\uFE2F]/),eb=dX(d9,ea,"*"),ec=dX(/[A-Z]/,ea,"*"),ed=["attached","autoclosure",dX(/convention\(/,dY("swift","block","c"),/\)/),"discardableResult","dynamicCallable","dynamicMemberLookup","escaping","freestanding","frozen","GKInspectable","IBAction","IBDesignable","IBInspectable","IBOutlet","IBSegueAction","inlinable","main","nonobjc","NSApplicationMain","NSCopying","NSManaged",dX(/objc\(/,eb,/\)/),"objc","objcMembers","propertyWrapper","requires_stored_property_inits","resultBuilder","Sendable","testable","UIApplicationMain","unchecked","unknown","usableFromInline","warn_unqualified_access"],ee=["iOS","iOSApplicationExtension","macOS","macOSApplicationExtension","macCatalyst","macCatalystApplicationExtension","watchOS","watchOSApplicationExtension","tvOS","tvOSApplicationExtension","swift"],ef="[A-Za-z$_][0-9A-Za-z$_]*",eg=["as","in","of","if","for","while","finally","var","new","function","do","return","void","else","break","catch","instanceof","with","throw","case","default","try","switch","continue","typeof","delete","let","yield","const","class","debugger","async","await","static","import","from","export","extends","using"],eh=["true","false","null","undefined","NaN","Infinity"],ei=["Object","Function","Boolean","Symbol","Math","Date","Number","BigInt","String","RegExp","Array","Float32Array","Float64Array","Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Int32Array","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array","Set","Map","WeakSet","WeakMap","ArrayBuffer","SharedArrayBuffer","Atomics","DataView","JSON","Promise","Generator","GeneratorFunction","AsyncFunction","Reflect","Proxy","Intl","WebAssembly"],ej=["Error","EvalError","InternalError","RangeError","ReferenceError","SyntaxError","TypeError","URIError"],ek=["setInterval","setTimeout","clearInterval","clearTimeout","require","exports","eval","isFinite","isNaN","parseFloat","parseInt","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","escape","unescape"],el=["arguments","this","super","console","window","document","localStorage","sessionStorage","module","global"],em=[].concat(ek,ei,ej),en={arduino:function(a){let b=function(a){let b=a.regex,c=a.COMMENT("//","$",{contains:[{begin:/\\\n/}]}),d="decltype\\(auto\\)",e="[a-zA-Z_]\\w*::",f="(?!struct)("+d+"|"+b.optional(e)+"[a-zA-Z_]\\w*"+b.optional("<[^<>]+>")+")",g={className:"type",begin:"\\b[a-z\\d_]*_t\\b"},h={className:"string",variants:[{begin:'(u8?|U|L)?"',end:'"',illegal:"\\n",contains:[a.BACKSLASH_ESCAPE]},{begin:"(u8?|U|L)?'(\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\S)|.)",end:"'",illegal:"."},a.END_SAME_AS_BEGIN({begin:/(?:u8?|U|L)?R"([^()\\ ]{0,16})\(/,end:/\)([^()\\ ]{0,16})"/})]},i={className:"number",variants:[{begin:"[+-]?(?:(?:[0-9](?:'?[0-9])*\\.(?:[0-9](?:'?[0-9])*)?|\\.[0-9](?:'?[0-9])*)(?:[Ee][+-]?[0-9](?:'?[0-9])*)?|[0-9](?:'?[0-9])*[Ee][+-]?[0-9](?:'?[0-9])*|0[Xx](?:[0-9A-Fa-f](?:'?[0-9A-Fa-f])*(?:\\.(?:[0-9A-Fa-f](?:'?[0-9A-Fa-f])*)?)?|\\.[0-9A-Fa-f](?:'?[0-9A-Fa-f])*)[Pp][+-]?[0-9](?:'?[0-9])*)(?:[Ff](?:16|32|64|128)?|(BF|bf)16|[Ll]|)"},{begin:"[+-]?\\b(?:0[Bb][01](?:'?[01])*|0[Xx][0-9A-Fa-f](?:'?[0-9A-Fa-f])*|0(?:'?[0-7])*|[1-9](?:'?[0-9])*)(?:[Uu](?:LL?|ll?)|[Uu][Zz]?|(?:LL?|ll?)[Uu]?|[Zz][Uu]|)"}],relevance:0},j={className:"meta",begin:/#\s*[a-z]+\b/,end:/$/,keywords:{keyword:"if else elif endif define undef warning error line pragma _Pragma ifdef ifndef include"},contains:[{begin:/\\\n/,relevance:0},a.inherit(h,{className:"string"}),{className:"string",begin:/<.*?>/},c,a.C_BLOCK_COMMENT_MODE]},k={className:"title",begin:b.optional(e)+a.IDENT_RE,relevance:0},l=b.optional(e)+a.IDENT_RE+"\\s*\\(",m={type:["bool","char","char16_t","char32_t","char8_t","double","float","int","long","short","void","wchar_t","unsigned","signed","const","static"],keyword:["alignas","alignof","and","and_eq","asm","atomic_cancel","atomic_commit","atomic_noexcept","auto","bitand","bitor","break","case","catch","class","co_await","co_return","co_yield","compl","concept","const_cast|10","consteval","constexpr","constinit","continue","decltype","default","delete","do","dynamic_cast|10","else","enum","explicit","export","extern","false","final","for","friend","goto","if","import","inline","module","mutable","namespace","new","noexcept","not","not_eq","nullptr","operator","or","or_eq","override","private","protected","public","reflexpr","register","reinterpret_cast|10","requires","return","sizeof","static_assert","static_cast|10","struct","switch","synchronized","template","this","thread_local","throw","transaction_safe","transaction_safe_dynamic","true","try","typedef","typeid","typename","union","using","virtual","volatile","while","xor","xor_eq"],literal:["NULL","false","nullopt","nullptr","true"],built_in:["_Pragma"],_type_hints:["any","auto_ptr","barrier","binary_semaphore","bitset","complex","condition_variable","condition_variable_any","counting_semaphore","deque","false_type","flat_map","flat_set","future","imaginary","initializer_list","istringstream","jthread","latch","lock_guard","multimap","multiset","mutex","optional","ostringstream","packaged_task","pair","promise","priority_queue","queue","recursive_mutex","recursive_timed_mutex","scoped_lock","set","shared_future","shared_lock","shared_mutex","shared_timed_mutex","shared_ptr","stack","string_view","stringstream","timed_mutex","thread","true_type","tuple","unique_lock","unique_ptr","unordered_map","unordered_multimap","unordered_multiset","unordered_set","variant","vector","weak_ptr","wstring","wstring_view"]},n={className:"function.dispatch",relevance:0,keywords:{_hint:["abort","abs","acos","apply","as_const","asin","atan","atan2","calloc","ceil","cerr","cin","clog","cos","cosh","cout","declval","endl","exchange","exit","exp","fabs","floor","fmod","forward","fprintf","fputs","free","frexp","fscanf","future","invoke","isalnum","isalpha","iscntrl","isdigit","isgraph","islower","isprint","ispunct","isspace","isupper","isxdigit","labs","launder","ldexp","log","log10","make_pair","make_shared","make_shared_for_overwrite","make_tuple","make_unique","malloc","memchr","memcmp","memcpy","memset","modf","move","pow","printf","putchar","puts","realloc","scanf","sin","sinh","snprintf","sprintf","sqrt","sscanf","std","stderr","stdin","stdout","strcat","strchr","strcmp","strcpy","strcspn","strlen","strncat","strncmp","strncpy","strpbrk","strrchr","strspn","strstr","swap","tan","tanh","terminate","to_underlying","tolower","toupper","vfprintf","visit","vprintf","vsprintf"]},begin:b.concat(/\b/,/(?!decltype)/,/(?!if)/,/(?!for)/,/(?!switch)/,/(?!while)/,a.IDENT_RE,b.lookahead(/(<[^<>]+>|)\s*\(/))},o=[n,j,g,c,a.C_BLOCK_COMMENT_MODE,i,h],p={variants:[{begin:/=/,end:/;/},{begin:/\(/,end:/\)/},{beginKeywords:"new throw return else",end:/;/}],keywords:m,contains:o.concat([{begin:/\(/,end:/\)/,keywords:m,contains:o.concat(["self"]),relevance:0}]),relevance:0},q={className:"function",begin:"("+f+"[\\*&\\s]+)+"+l,returnBegin:!0,end:/[{;=]/,excludeEnd:!0,keywords:m,illegal:/[^\w\s\*&:<>.]/,contains:[{begin:d,keywords:m,relevance:0},{begin:l,returnBegin:!0,contains:[k],relevance:0},{begin:/::/,relevance:0},{begin:/:/,endsWithParent:!0,contains:[h,i]},{relevance:0,match:/,/},{className:"params",begin:/\(/,end:/\)/,keywords:m,relevance:0,contains:[c,a.C_BLOCK_COMMENT_MODE,h,i,g,{begin:/\(/,end:/\)/,keywords:m,relevance:0,contains:["self",c,a.C_BLOCK_COMMENT_MODE,h,i,g]}]},g,c,a.C_BLOCK_COMMENT_MODE,j]};return{name:"C++",aliases:["cc","c++","h++","hpp","hh","hxx","cxx"],keywords:m,illegal:"</",classNameAliases:{"function.dispatch":"built_in"},contains:[].concat(p,q,n,o,[j,{begin:"\\b(deque|list|queue|priority_queue|pair|stack|vector|map|set|bitset|multiset|multimap|unordered_map|unordered_set|unordered_multiset|unordered_multimap|array|tuple|optional|variant|function|flat_map|flat_set)\\s*<(?!<)",end:">",keywords:m,contains:["self",g]},{begin:a.IDENT_RE+"::",keywords:m},{match:[/\b(?:enum(?:\s+(?:class|struct))?|class|struct|union)/,/\s+/,/\w+/],className:{1:"keyword",3:"title.class"}}])}}(a),c=b.keywords;return c.type=[...c.type,"boolean","byte","word","String"],c.literal=[...c.literal,"DIGITAL_MESSAGE","FIRMATA_STRING","ANALOG_MESSAGE","REPORT_DIGITAL","REPORT_ANALOG","INPUT_PULLUP","SET_PIN_MODE","INTERNAL2V56","SYSTEM_RESET","LED_BUILTIN","INTERNAL1V1","SYSEX_START","INTERNAL","EXTERNAL","DEFAULT","OUTPUT","INPUT","HIGH","LOW"],c.built_in=[...c.built_in,"KeyboardController","MouseController","SoftwareSerial","EthernetServer","EthernetClient","LiquidCrystal","RobotControl","GSMVoiceCall","EthernetUDP","EsploraTFT","HttpClient","RobotMotor","WiFiClient","GSMScanner","FileSystem","Scheduler","GSMServer","YunClient","YunServer","IPAddress","GSMClient","GSMModem","Keyboard","Ethernet","Console","GSMBand","Esplora","Stepper","Process","WiFiUDP","GSM_SMS","Mailbox","USBHost","Firmata","PImage","Client","Server","GSMPIN","FileIO","Bridge","Serial","EEPROM","Stream","Mouse","Audio","Servo","File","Task","GPRS","WiFi","Wire","TFT","GSM","SPI","SD"],c._hints=["setup","loop","runShellCommandAsynchronously","analogWriteResolution","retrieveCallingNumber","printFirmwareVersion","analogReadResolution","sendDigitalPortPair","noListenOnLocalhost","readJoystickButton","setFirmwareVersion","readJoystickSwitch","scrollDisplayRight","getVoiceCallStatus","scrollDisplayLeft","writeMicroseconds","delayMicroseconds","beginTransmission","getSignalStrength","runAsynchronously","getAsynchronously","listenOnLocalhost","getCurrentCarrier","readAccelerometer","messageAvailable","sendDigitalPorts","lineFollowConfig","countryNameWrite","runShellCommand","readStringUntil","rewindDirectory","readTemperature","setClockDivider","readLightSensor","endTransmission","analogReference","detachInterrupt","countryNameRead","attachInterrupt","encryptionType","readBytesUntil","robotNameWrite","readMicrophone","robotNameRead","cityNameWrite","userNameWrite","readJoystickY","readJoystickX","mouseReleased","openNextFile","scanNetworks","noInterrupts","digitalWrite","beginSpeaker","mousePressed","isActionDone","mouseDragged","displayLogos","noAutoscroll","addParameter","remoteNumber","getModifiers","keyboardRead","userNameRead","waitContinue","processInput","parseCommand","printVersion","readNetworks","writeMessage","blinkVersion","cityNameRead","readMessage","setDataMode","parsePacket","isListening","setBitOrder","beginPacket","isDirectory","motorsWrite","drawCompass","digitalRead","clearScreen","serialEvent","rightToLeft","setTextSize","leftToRight","requestFrom","keyReleased","compassRead","analogWrite","interrupts","WiFiServer","disconnect","playMelody","parseFloat","autoscroll","getPINUsed","setPINUsed","setTimeout","sendAnalog","readSlider","analogRead","beginWrite","createChar","motorsStop","keyPressed","tempoWrite","readButton","subnetMask","debugPrint","macAddress","writeGreen","randomSeed","attachGPRS","readString","sendString","remotePort","releaseAll","mouseMoved","background","getXChange","getYChange","answerCall","getResult","voiceCall","endPacket","constrain","getSocket","writeJSON","getButton","available","connected","findUntil","readBytes","exitValue","readGreen","writeBlue","startLoop","IPAddress","isPressed","sendSysex","pauseMode","gatewayIP","setCursor","getOemKey","tuneWrite","noDisplay","loadImage","switchPIN","onRequest","onReceive","changePIN","playFile","noBuffer","parseInt","overflow","checkPIN","knobRead","beginTFT","bitClear","updateIR","bitWrite","position","writeRGB","highByte","writeRed","setSpeed","readBlue","noStroke","remoteIP","transfer","shutdown","hangCall","beginSMS","endWrite","attached","maintain","noCursor","checkReg","checkPUK","shiftOut","isValid","shiftIn","pulseIn","connect","println","localIP","pinMode","getIMEI","display","noBlink","process","getBand","running","beginSD","drawBMP","lowByte","setBand","release","bitRead","prepare","pointTo","readRed","setMode","noFill","remove","listen","stroke","detach","attach","noTone","exists","buffer","height","bitSet","circle","config","cursor","random","IRread","setDNS","endSMS","getKey","micros","millis","begin","print","write","ready","flush","width","isPIN","blink","clear","press","mkdir","rmdir","close","point","yield","image","BSSID","click","delay","read","text","move","peek","beep","rect","line","open","seek","fill","size","turn","stop","home","find","step","tone","sqrt","RSSI","SSID","end","bit","tan","cos","sin","pow","map","abs","max","min","get","run","put"],b.name="Arduino",b.aliases=["ino"],b.supersetOf="cpp",b},bash:function(a){let b=a.regex,c={};Object.assign(c,{className:"variable",variants:[{begin:b.concat(/\$[\w\d#@][\w\d_]*/,"(?![\\w\\d])(?![$])")},{begin:/\$\{/,end:/\}/,contains:["self",{begin:/:-/,contains:[c]}]}]});let d={className:"subst",begin:/\$\(/,end:/\)/,contains:[a.BACKSLASH_ESCAPE]},e=a.inherit(a.COMMENT(),{match:[/(^|\s)/,/#.*$/],scope:{2:"comment"}}),f={begin:/<<-?\s*(?=\w+)/,starts:{contains:[a.END_SAME_AS_BEGIN({begin:/(\w+)/,end:/(\w+)/,className:"string"})]}},g={className:"string",begin:/"/,end:/"/,contains:[a.BACKSLASH_ESCAPE,c,d]};d.contains.push(g);let h={begin:/\$?\(\(/,end:/\)\)/,contains:[{begin:/\d+#[0-9a-f]+/,className:"number"},a.NUMBER_MODE,c]},i=a.SHEBANG({binary:"(fish|bash|zsh|sh|csh|ksh|tcsh|dash|scsh)",relevance:10}),j={className:"function",begin:/\w[\w\d_]*\s*\(\s*\)\s*\{/,returnBegin:!0,contains:[a.inherit(a.TITLE_MODE,{begin:/\w[\w\d_]*/})],relevance:0};return{name:"Bash",aliases:["sh","zsh"],keywords:{$pattern:/\b[a-z][a-z0-9._-]+\b/,keyword:["if","then","else","elif","fi","time","for","while","until","in","do","done","case","esac","coproc","function","select"],literal:["true","false"],built_in:["break","cd","continue","eval","exec","exit","export","getopts","hash","pwd","readonly","return","shift","test","times","trap","umask","unset","alias","bind","builtin","caller","command","declare","echo","enable","help","let","local","logout","mapfile","printf","read","readarray","source","sudo","type","typeset","ulimit","unalias","set","shopt","autoload","bg","bindkey","bye","cap","chdir","clone","comparguments","compcall","compctl","compdescribe","compfiles","compgroups","compquote","comptags","comptry","compvalues","dirs","disable","disown","echotc","echoti","emulate","fc","fg","float","functions","getcap","getln","history","integer","jobs","kill","limit","log","noglob","popd","print","pushd","pushln","rehash","sched","setcap","setopt","stat","suspend","ttyctl","unfunction","unhash","unlimit","unsetopt","vared","wait","whence","where","which","zcompile","zformat","zftp","zle","zmodload","zparseopts","zprof","zpty","zregexparse","zsocket","zstyle","ztcp","chcon","chgrp","chown","chmod","cp","dd","df","dir","dircolors","ln","ls","mkdir","mkfifo","mknod","mktemp","mv","realpath","rm","rmdir","shred","sync","touch","truncate","vdir","b2sum","base32","base64","cat","cksum","comm","csplit","cut","expand","fmt","fold","head","join","md5sum","nl","numfmt","od","paste","ptx","pr","sha1sum","sha224sum","sha256sum","sha384sum","sha512sum","shuf","sort","split","sum","tac","tail","tr","tsort","unexpand","uniq","wc","arch","basename","chroot","date","dirname","du","echo","env","expr","factor","groups","hostid","id","link","logname","nice","nohup","nproc","pathchk","pinky","printenv","printf","pwd","readlink","runcon","seq","sleep","stat","stdbuf","stty","tee","test","timeout","tty","uname","unlink","uptime","users","who","whoami","yes"]},contains:[i,a.SHEBANG(),j,h,e,f,{match:/(\/[a-z._-]+)+/},g,{match:/\\"/},{className:"string",begin:/'/,end:/'/},{match:/\\'/},c]}},c:function(a){let b=a.regex,c=a.COMMENT("//","$",{contains:[{begin:/\\\n/}]}),d="decltype\\(auto\\)",e="[a-zA-Z_]\\w*::",f="("+d+"|"+b.optional(e)+"[a-zA-Z_]\\w*"+b.optional("<[^<>]+>")+")",g={className:"type",variants:[{begin:"\\b[a-z\\d_]*_t\\b"},{match:/\batomic_[a-z]{3,6}\b/}]},h={className:"string",variants:[{begin:'(u8?|U|L)?"',end:'"',illegal:"\\n",contains:[a.BACKSLASH_ESCAPE]},{begin:"(u8?|U|L)?'(\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\S)|.)",end:"'",illegal:"."},a.END_SAME_AS_BEGIN({begin:/(?:u8?|U|L)?R"([^()\\ ]{0,16})\(/,end:/\)([^()\\ ]{0,16})"/})]},i={className:"number",variants:[{match:/\b(0b[01']+)/},{match:/(-?)\b([\d']+(\.[\d']*)?|\.[\d']+)((ll|LL|l|L)(u|U)?|(u|U)(ll|LL|l|L)?|f|F|b|B)/},{match:/(-?)\b(0[xX][a-fA-F0-9]+(?:'[a-fA-F0-9]+)*(?:\.[a-fA-F0-9]*(?:'[a-fA-F0-9]*)*)?(?:[pP][-+]?[0-9]+)?(l|L)?(u|U)?)/},{match:/(-?)\b\d+(?:'\d+)*(?:\.\d*(?:'\d*)*)?(?:[eE][-+]?\d+)?/}],relevance:0},j={className:"meta",begin:/#\s*[a-z]+\b/,end:/$/,keywords:{keyword:"if else elif endif define undef warning error line pragma _Pragma ifdef ifndef elifdef elifndef include"},contains:[{begin:/\\\n/,relevance:0},a.inherit(h,{className:"string"}),{className:"string",begin:/<.*?>/},c,a.C_BLOCK_COMMENT_MODE]},k={className:"title",begin:b.optional(e)+a.IDENT_RE,relevance:0},l=b.optional(e)+a.IDENT_RE+"\\s*\\(",m={keyword:["asm","auto","break","case","continue","default","do","else","enum","extern","for","fortran","goto","if","inline","register","restrict","return","sizeof","typeof","typeof_unqual","struct","switch","typedef","union","volatile","while","_Alignas","_Alignof","_Atomic","_Generic","_Noreturn","_Static_assert","_Thread_local","alignas","alignof","noreturn","static_assert","thread_local","_Pragma"],type:["float","double","signed","unsigned","int","short","long","char","void","_Bool","_BitInt","_Complex","_Imaginary","_Decimal32","_Decimal64","_Decimal96","_Decimal128","_Decimal64x","_Decimal128x","_Float16","_Float32","_Float64","_Float128","_Float32x","_Float64x","_Float128x","const","static","constexpr","complex","bool","imaginary"],literal:"true false NULL",built_in:"std string wstring cin cout cerr clog stdin stdout stderr stringstream istringstream ostringstream auto_ptr deque list queue stack vector map set pair bitset multiset multimap unordered_set unordered_map unordered_multiset unordered_multimap priority_queue make_pair array shared_ptr abort terminate abs acos asin atan2 atan calloc ceil cosh cos exit exp fabs floor fmod fprintf fputs free frexp fscanf future isalnum isalpha iscntrl isdigit isgraph islower isprint ispunct isspace isupper isxdigit tolower toupper labs ldexp log10 log malloc realloc memchr memcmp memcpy memset modf pow printf putchar puts scanf sinh sin snprintf sprintf sqrt sscanf strcat strchr strcmp strcpy strcspn strlen strncat strncmp strncpy strpbrk strrchr strspn strstr tanh tan vfprintf vprintf vsprintf endl initializer_list unique_ptr"},n=[j,g,c,a.C_BLOCK_COMMENT_MODE,i,h],o={variants:[{begin:/=/,end:/;/},{begin:/\(/,end:/\)/},{beginKeywords:"new throw return else",end:/;/}],keywords:m,contains:n.concat([{begin:/\(/,end:/\)/,keywords:m,contains:n.concat(["self"]),relevance:0}]),relevance:0},p={begin:"("+f+"[\\*&\\s]+)+"+l,returnBegin:!0,end:/[{;=]/,excludeEnd:!0,keywords:m,illegal:/[^\w\s\*&:<>.]/,contains:[{begin:d,keywords:m,relevance:0},{begin:l,returnBegin:!0,contains:[a.inherit(k,{className:"title.function"})],relevance:0},{relevance:0,match:/,/},{className:"params",begin:/\(/,end:/\)/,keywords:m,relevance:0,contains:[c,a.C_BLOCK_COMMENT_MODE,h,i,g,{begin:/\(/,end:/\)/,keywords:m,relevance:0,contains:["self",c,a.C_BLOCK_COMMENT_MODE,h,i,g]}]},g,c,a.C_BLOCK_COMMENT_MODE,j]};return{name:"C",aliases:["h"],keywords:m,disableAutodetect:!0,illegal:"</",contains:[].concat(o,p,n,[j,{begin:a.IDENT_RE+"::",keywords:m},{className:"class",beginKeywords:"enum class struct union",end:/[{;:<>=]/,contains:[{beginKeywords:"final class struct"},a.TITLE_MODE]}]),exports:{preprocessor:j,strings:h,keywords:m}}},cpp:function(a){let b=a.regex,c=a.COMMENT("//","$",{contains:[{begin:/\\\n/}]}),d="decltype\\(auto\\)",e="[a-zA-Z_]\\w*::",f="(?!struct)("+d+"|"+b.optional(e)+"[a-zA-Z_]\\w*"+b.optional("<[^<>]+>")+")",g={className:"type",begin:"\\b[a-z\\d_]*_t\\b"},h={className:"string",variants:[{begin:'(u8?|U|L)?"',end:'"',illegal:"\\n",contains:[a.BACKSLASH_ESCAPE]},{begin:"(u8?|U|L)?'(\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\S)|.)",end:"'",illegal:"."},a.END_SAME_AS_BEGIN({begin:/(?:u8?|U|L)?R"([^()\\ ]{0,16})\(/,end:/\)([^()\\ ]{0,16})"/})]},i={className:"number",variants:[{begin:"[+-]?(?:(?:[0-9](?:'?[0-9])*\\.(?:[0-9](?:'?[0-9])*)?|\\.[0-9](?:'?[0-9])*)(?:[Ee][+-]?[0-9](?:'?[0-9])*)?|[0-9](?:'?[0-9])*[Ee][+-]?[0-9](?:'?[0-9])*|0[Xx](?:[0-9A-Fa-f](?:'?[0-9A-Fa-f])*(?:\\.(?:[0-9A-Fa-f](?:'?[0-9A-Fa-f])*)?)?|\\.[0-9A-Fa-f](?:'?[0-9A-Fa-f])*)[Pp][+-]?[0-9](?:'?[0-9])*)(?:[Ff](?:16|32|64|128)?|(BF|bf)16|[Ll]|)"},{begin:"[+-]?\\b(?:0[Bb][01](?:'?[01])*|0[Xx][0-9A-Fa-f](?:'?[0-9A-Fa-f])*|0(?:'?[0-7])*|[1-9](?:'?[0-9])*)(?:[Uu](?:LL?|ll?)|[Uu][Zz]?|(?:LL?|ll?)[Uu]?|[Zz][Uu]|)"}],relevance:0},j={className:"meta",begin:/#\s*[a-z]+\b/,end:/$/,keywords:{keyword:"if else elif endif define undef warning error line pragma _Pragma ifdef ifndef include"},contains:[{begin:/\\\n/,relevance:0},a.inherit(h,{className:"string"}),{className:"string",begin:/<.*?>/},c,a.C_BLOCK_COMMENT_MODE]},k={className:"title",begin:b.optional(e)+a.IDENT_RE,relevance:0},l=b.optional(e)+a.IDENT_RE+"\\s*\\(",m={type:["bool","char","char16_t","char32_t","char8_t","double","float","int","long","short","void","wchar_t","unsigned","signed","const","static"],keyword:["alignas","alignof","and","and_eq","asm","atomic_cancel","atomic_commit","atomic_noexcept","auto","bitand","bitor","break","case","catch","class","co_await","co_return","co_yield","compl","concept","const_cast|10","consteval","constexpr","constinit","continue","decltype","default","delete","do","dynamic_cast|10","else","enum","explicit","export","extern","false","final","for","friend","goto","if","import","inline","module","mutable","namespace","new","noexcept","not","not_eq","nullptr","operator","or","or_eq","override","private","protected","public","reflexpr","register","reinterpret_cast|10","requires","return","sizeof","static_assert","static_cast|10","struct","switch","synchronized","template","this","thread_local","throw","transaction_safe","transaction_safe_dynamic","true","try","typedef","typeid","typename","union","using","virtual","volatile","while","xor","xor_eq"],literal:["NULL","false","nullopt","nullptr","true"],built_in:["_Pragma"],_type_hints:["any","auto_ptr","barrier","binary_semaphore","bitset","complex","condition_variable","condition_variable_any","counting_semaphore","deque","false_type","flat_map","flat_set","future","imaginary","initializer_list","istringstream","jthread","latch","lock_guard","multimap","multiset","mutex","optional","ostringstream","packaged_task","pair","promise","priority_queue","queue","recursive_mutex","recursive_timed_mutex","scoped_lock","set","shared_future","shared_lock","shared_mutex","shared_timed_mutex","shared_ptr","stack","string_view","stringstream","timed_mutex","thread","true_type","tuple","unique_lock","unique_ptr","unordered_map","unordered_multimap","unordered_multiset","unordered_set","variant","vector","weak_ptr","wstring","wstring_view"]},n={className:"function.dispatch",relevance:0,keywords:{_hint:["abort","abs","acos","apply","as_const","asin","atan","atan2","calloc","ceil","cerr","cin","clog","cos","cosh","cout","declval","endl","exchange","exit","exp","fabs","floor","fmod","forward","fprintf","fputs","free","frexp","fscanf","future","invoke","isalnum","isalpha","iscntrl","isdigit","isgraph","islower","isprint","ispunct","isspace","isupper","isxdigit","labs","launder","ldexp","log","log10","make_pair","make_shared","make_shared_for_overwrite","make_tuple","make_unique","malloc","memchr","memcmp","memcpy","memset","modf","move","pow","printf","putchar","puts","realloc","scanf","sin","sinh","snprintf","sprintf","sqrt","sscanf","std","stderr","stdin","stdout","strcat","strchr","strcmp","strcpy","strcspn","strlen","strncat","strncmp","strncpy","strpbrk","strrchr","strspn","strstr","swap","tan","tanh","terminate","to_underlying","tolower","toupper","vfprintf","visit","vprintf","vsprintf"]},begin:b.concat(/\b/,/(?!decltype)/,/(?!if)/,/(?!for)/,/(?!switch)/,/(?!while)/,a.IDENT_RE,b.lookahead(/(<[^<>]+>|)\s*\(/))},o=[n,j,g,c,a.C_BLOCK_COMMENT_MODE,i,h],p={variants:[{begin:/=/,end:/;/},{begin:/\(/,end:/\)/},{beginKeywords:"new throw return else",end:/;/}],keywords:m,contains:o.concat([{begin:/\(/,end:/\)/,keywords:m,contains:o.concat(["self"]),relevance:0}]),relevance:0},q={className:"function",begin:"("+f+"[\\*&\\s]+)+"+l,returnBegin:!0,end:/[{;=]/,excludeEnd:!0,keywords:m,illegal:/[^\w\s\*&:<>.]/,contains:[{begin:d,keywords:m,relevance:0},{begin:l,returnBegin:!0,contains:[k],relevance:0},{begin:/::/,relevance:0},{begin:/:/,endsWithParent:!0,contains:[h,i]},{relevance:0,match:/,/},{className:"params",begin:/\(/,end:/\)/,keywords:m,relevance:0,contains:[c,a.C_BLOCK_COMMENT_MODE,h,i,g,{begin:/\(/,end:/\)/,keywords:m,relevance:0,contains:["self",c,a.C_BLOCK_COMMENT_MODE,h,i,g]}]},g,c,a.C_BLOCK_COMMENT_MODE,j]};return{name:"C++",aliases:["cc","c++","h++","hpp","hh","hxx","cxx"],keywords:m,illegal:"</",classNameAliases:{"function.dispatch":"built_in"},contains:[].concat(p,q,n,o,[j,{begin:"\\b(deque|list|queue|priority_queue|pair|stack|vector|map|set|bitset|multiset|multimap|unordered_map|unordered_set|unordered_multiset|unordered_multimap|array|tuple|optional|variant|function|flat_map|flat_set)\\s*<(?!<)",end:">",keywords:m,contains:["self",g]},{begin:a.IDENT_RE+"::",keywords:m},{match:[/\b(?:enum(?:\s+(?:class|struct))?|class|struct|union)/,/\s+/,/\w+/],className:{1:"keyword",3:"title.class"}}])}},csharp:function(a){let b={keyword:["abstract","as","base","break","case","catch","class","const","continue","do","else","event","explicit","extern","finally","fixed","for","foreach","goto","if","implicit","in","interface","internal","is","lock","namespace","new","operator","out","override","params","private","protected","public","readonly","record","ref","return","scoped","sealed","sizeof","stackalloc","static","struct","switch","this","throw","try","typeof","unchecked","unsafe","using","virtual","void","volatile","while"].concat(["add","alias","and","ascending","args","async","await","by","descending","dynamic","equals","file","from","get","global","group","init","into","join","let","nameof","not","notnull","on","or","orderby","partial","record","remove","required","scoped","select","set","unmanaged","value|0","var","when","where","with","yield"]),built_in:["bool","byte","char","decimal","delegate","double","dynamic","enum","float","int","long","nint","nuint","object","sbyte","short","string","ulong","uint","ushort"],literal:["default","false","null","true"]},c=a.inherit(a.TITLE_MODE,{begin:"[a-zA-Z](\\.?\\w)*"}),d={className:"number",variants:[{begin:"\\b(0b[01']+)"},{begin:"(-?)\\b([\\d']+(\\.[\\d']*)?|\\.[\\d']+)(u|U|l|L|ul|UL|f|F|b|B)"},{begin:"(-?)(\\b0[xX][a-fA-F0-9']+|(\\b[\\d']+(\\.[\\d']*)?|\\.[\\d']+)([eE][-+]?[\\d']+)?)"}],relevance:0},e={className:"string",begin:'@"',end:'"',contains:[{begin:'""'}]},f=a.inherit(e,{illegal:/\n/}),g={className:"subst",begin:/\{/,end:/\}/,keywords:b},h=a.inherit(g,{illegal:/\n/}),i={className:"string",begin:/\$"/,end:'"',illegal:/\n/,contains:[{begin:/\{\{/},{begin:/\}\}/},a.BACKSLASH_ESCAPE,h]},j={className:"string",begin:/\$@"/,end:'"',contains:[{begin:/\{\{/},{begin:/\}\}/},{begin:'""'},g]},k=a.inherit(j,{illegal:/\n/,contains:[{begin:/\{\{/},{begin:/\}\}/},{begin:'""'},h]});g.contains=[j,i,e,a.APOS_STRING_MODE,a.QUOTE_STRING_MODE,d,a.C_BLOCK_COMMENT_MODE],h.contains=[k,i,f,a.APOS_STRING_MODE,a.QUOTE_STRING_MODE,d,a.inherit(a.C_BLOCK_COMMENT_MODE,{illegal:/\n/})];let l={variants:[{className:"string",begin:/"""("*)(?!")(.|\n)*?"""\1/,relevance:1},j,i,e,a.APOS_STRING_MODE,a.QUOTE_STRING_MODE]},m={begin:"<",end:">",contains:[{beginKeywords:"in out"},c]},n=a.IDENT_RE+"(<"+a.IDENT_RE+"(\\s*,\\s*"+a.IDENT_RE+")*>)?(\\[\\])?",o={begin:"@"+a.IDENT_RE,relevance:0};return{name:"C#",aliases:["cs","c#"],keywords:b,illegal:/::/,contains:[a.COMMENT("///","$",{returnBegin:!0,contains:[{className:"doctag",variants:[{begin:"///",relevance:0},{begin:"\x3c!--|--\x3e"},{begin:"</?",end:">"}]}]}),a.C_LINE_COMMENT_MODE,a.C_BLOCK_COMMENT_MODE,{className:"meta",begin:"#",end:"$",keywords:{keyword:"if else elif endif define undef warning error line region endregion pragma checksum"}},l,d,{beginKeywords:"class interface",relevance:0,end:/[{;=]/,illegal:/[^\s:,]/,contains:[{beginKeywords:"where class"},c,m,a.C_LINE_COMMENT_MODE,a.C_BLOCK_COMMENT_MODE]},{beginKeywords:"namespace",relevance:0,end:/[{;=]/,illegal:/[^\s:]/,contains:[c,a.C_LINE_COMMENT_MODE,a.C_BLOCK_COMMENT_MODE]},{beginKeywords:"record",relevance:0,end:/[{;=]/,illegal:/[^\s:]/,contains:[c,m,a.C_LINE_COMMENT_MODE,a.C_BLOCK_COMMENT_MODE]},{className:"meta",begin:"^\\s*\\[(?=[\\w])",excludeBegin:!0,end:"\\]",excludeEnd:!0,contains:[{className:"string",begin:/"/,end:/"/}]},{beginKeywords:"new return throw await else",relevance:0},{className:"function",begin:"("+n+"\\s+)+"+a.IDENT_RE+"\\s*(<[^=]+>\\s*)?\\(",returnBegin:!0,end:/\s*[{;=]/,excludeEnd:!0,keywords:b,contains:[{beginKeywords:"public private protected static internal protected abstract async extern override unsafe virtual new sealed partial",relevance:0},{begin:a.IDENT_RE+"\\s*(<[^=]+>\\s*)?\\(",returnBegin:!0,contains:[a.TITLE_MODE,m],relevance:0},{match:/\(\)/},{className:"params",begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:b,relevance:0,contains:[l,d,a.C_BLOCK_COMMENT_MODE]},a.C_LINE_COMMENT_MODE,a.C_BLOCK_COMMENT_MODE]},o]}},css:function(a){let b=a.regex,c={IMPORTANT:{scope:"meta",begin:"!important"},BLOCK_COMMENT:a.C_BLOCK_COMMENT_MODE,HEXCOLOR:{scope:"number",begin:/#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\b/},FUNCTION_DISPATCH:{className:"built_in",begin:/[\w-]+(?=\()/},ATTRIBUTE_SELECTOR_MODE:{scope:"selector-attr",begin:/\[/,end:/\]/,illegal:"$",contains:[a.APOS_STRING_MODE,a.QUOTE_STRING_MODE]},CSS_NUMBER_MODE:{scope:"number",begin:a.NUMBER_RE+"(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?",relevance:0},CSS_VARIABLE:{className:"attr",begin:/--[A-Za-z_][A-Za-z0-9_-]*/}},d=[a.APOS_STRING_MODE,a.QUOTE_STRING_MODE];return{name:"CSS",case_insensitive:!0,illegal:/[=|'\$]/,keywords:{keyframePosition:"from to"},classNameAliases:{keyframePosition:"selector-tag"},contains:[c.BLOCK_COMMENT,{begin:/-(webkit|moz|ms|o)-(?=[a-z])/},c.CSS_NUMBER_MODE,{className:"selector-id",begin:/#[A-Za-z0-9_-]+/,relevance:0},{className:"selector-class",begin:"\\.[a-zA-Z-][a-zA-Z0-9_-]*",relevance:0},c.ATTRIBUTE_SELECTOR_MODE,{className:"selector-pseudo",variants:[{begin:":("+dr.join("|")+")"},{begin:":(:)?("+ds.join("|")+")"}]},c.CSS_VARIABLE,{className:"attribute",begin:"\\b("+dt.join("|")+")\\b"},{begin:/:/,end:/[;}{]/,contains:[c.BLOCK_COMMENT,c.HEXCOLOR,c.IMPORTANT,c.CSS_NUMBER_MODE,...d,{begin:/(url|data-uri)\(/,end:/\)/,relevance:0,keywords:{built_in:"url data-uri"},contains:[...d,{className:"string",begin:/[^)]/,endsWithParent:!0,excludeEnd:!0}]},c.FUNCTION_DISPATCH]},{begin:b.lookahead(/@/),end:"[{;]",relevance:0,illegal:/:/,contains:[{className:"keyword",begin:/@-?\w[\w]*(-\w+)*/},{begin:/\s/,endsWithParent:!0,excludeEnd:!0,relevance:0,keywords:{$pattern:/[a-z-]+/,keyword:"and or not only",attribute:dq.join(" ")},contains:[{begin:/[a-z-]+(?=:)/,className:"attribute"},...d,c.CSS_NUMBER_MODE]}]},{className:"selector-tag",begin:"\\b("+dp.join("|")+")\\b"}]}},diff:function(a){let b=a.regex;return{name:"Diff",aliases:["patch"],contains:[{className:"meta",relevance:10,match:b.either(/^@@ +-\d+,\d+ +\+\d+,\d+ +@@/,/^\*\*\* +\d+,\d+ +\*\*\*\*$/,/^--- +\d+,\d+ +----$/)},{className:"comment",variants:[{begin:b.either(/Index: /,/^index/,/={3,}/,/^-{3}/,/^\*{3} /,/^\+{3}/,/^diff --git/),end:/$/},{match:/^\*{15}$/}]},{className:"addition",begin:/^\+/,end:/$/},{className:"deletion",begin:/^-/,end:/$/},{className:"addition",begin:/^!/,end:/$/}]}},go:function(a){let b={keyword:["break","case","chan","const","continue","default","defer","else","fallthrough","for","func","go","goto","if","import","interface","map","package","range","return","select","struct","switch","type","var"],type:["bool","byte","complex64","complex128","error","float32","float64","int8","int16","int32","int64","string","uint8","uint16","uint32","uint64","int","uint","uintptr","rune"],literal:["true","false","iota","nil"],built_in:["append","cap","close","complex","copy","imag","len","make","new","panic","print","println","real","recover","delete"]};return{name:"Go",aliases:["golang"],keywords:b,illegal:"</",contains:[a.C_LINE_COMMENT_MODE,a.C_BLOCK_COMMENT_MODE,{className:"string",variants:[a.QUOTE_STRING_MODE,a.APOS_STRING_MODE,{begin:"`",end:"`"}]},{className:"number",variants:[{match:/-?\b0[xX]\.[a-fA-F0-9](_?[a-fA-F0-9])*[pP][+-]?\d(_?\d)*i?/,relevance:0},{match:/-?\b0[xX](_?[a-fA-F0-9])+((\.([a-fA-F0-9](_?[a-fA-F0-9])*)?)?[pP][+-]?\d(_?\d)*)?i?/,relevance:0},{match:/-?\b0[oO](_?[0-7])*i?/,relevance:0},{match:/-?\.\d(_?\d)*([eE][+-]?\d(_?\d)*)?i?/,relevance:0},{match:/-?\b\d(_?\d)*(\.(\d(_?\d)*)?)?([eE][+-]?\d(_?\d)*)?i?/,relevance:0}]},{begin:/:=/},{className:"function",beginKeywords:"func",end:"\\s*(\\{|$)",excludeEnd:!0,contains:[a.TITLE_MODE,{className:"params",begin:/\(/,end:/\)/,endsParent:!0,keywords:b,illegal:/["']/}]}]}},graphql:function(a){let b=a.regex;return{name:"GraphQL",aliases:["gql"],case_insensitive:!0,disableAutodetect:!1,keywords:{keyword:["query","mutation","subscription","type","input","schema","directive","interface","union","scalar","fragment","enum","on"],literal:["true","false","null"]},contains:[a.HASH_COMMENT_MODE,a.QUOTE_STRING_MODE,a.NUMBER_MODE,{scope:"punctuation",match:/[.]{3}/,relevance:0},{scope:"punctuation",begin:/[\!\(\)\:\=\[\]\{\|\}]{1}/,relevance:0},{scope:"variable",begin:/\$/,end:/\W/,excludeEnd:!0,relevance:0},{scope:"meta",match:/@\w+/,excludeEnd:!0},{scope:"symbol",begin:b.concat(/[_A-Za-z][_0-9A-Za-z]*/,b.lookahead(/\s*:/)),relevance:0}],illegal:[/[;<']/,/BEGIN/]}},ini:function(a){let b=a.regex,c={className:"number",relevance:0,variants:[{begin:/([+-]+)?[\d]+_[\d_]+/},{begin:a.NUMBER_RE}]},d=a.COMMENT();d.variants=[{begin:/;/,end:/$/},{begin:/#/,end:/$/}];let e={className:"variable",variants:[{begin:/\$[\w\d"][\w\d_]*/},{begin:/\$\{(.*?)\}/}]},f={className:"literal",begin:/\bon|off|true|false|yes|no\b/},g={className:"string",contains:[a.BACKSLASH_ESCAPE],variants:[{begin:"'''",end:"'''",relevance:10},{begin:'"""',end:'"""',relevance:10},{begin:'"',end:'"'},{begin:"'",end:"'"}]},h=b.either(/[A-Za-z0-9_-]+/,/"(\\"|[^"])*"/,/'[^']*'/);return{name:"TOML, also INI",aliases:["toml"],case_insensitive:!0,illegal:/\S/,contains:[d,{className:"section",begin:/\[+/,end:/\]+/},{begin:b.concat(h,"(\\s*\\.\\s*",h,")*",b.lookahead(/\s*=\s*[^#\s]/)),className:"attr",starts:{end:/$/,contains:[d,{begin:/\[/,end:/\]/,contains:[d,f,e,g,c,"self"],relevance:0},f,e,g,c]}}]}},java:function(a){let b=a.regex,c="[\xc0-ʸa-zA-Z_$][\xc0-ʸa-zA-Z_$0-9]*",d=c+function a(b,c,d){return -1===d?"":b.replace(c,e=>a(b,c,d-1))}("(?:<"+c+"~~~(?:\\s*,\\s*"+c+"~~~)*>)?",/~~~/g,2),e={keyword:["synchronized","abstract","private","var","static","if","const ","for","while","strictfp","finally","protected","import","native","final","void","enum","else","break","transient","catch","instanceof","volatile","case","assert","package","default","public","try","switch","continue","throws","protected","public","private","module","requires","exports","do","sealed","yield","permits","goto","when"],literal:["false","true","null"],type:["char","boolean","long","float","int","byte","short","double"],built_in:["super","this"]},f={className:"meta",begin:"@"+c,contains:[{begin:/\(/,end:/\)/,contains:["self"]}]},g={className:"params",begin:/\(/,end:/\)/,keywords:e,relevance:0,contains:[a.C_BLOCK_COMMENT_MODE],endsParent:!0};return{name:"Java",aliases:["jsp"],keywords:e,illegal:/<\/|#/,contains:[a.COMMENT("/\\*\\*","\\*/",{relevance:0,contains:[{begin:/\w+@/,relevance:0},{className:"doctag",begin:"@[A-Za-z]+"}]}),{begin:/import java\.[a-z]+\./,keywords:"import",relevance:2},a.C_LINE_COMMENT_MODE,a.C_BLOCK_COMMENT_MODE,{begin:/"""/,end:/"""/,className:"string",contains:[a.BACKSLASH_ESCAPE]},a.APOS_STRING_MODE,a.QUOTE_STRING_MODE,{match:[/\b(?:class|interface|enum|extends|implements|new)/,/\s+/,c],className:{1:"keyword",3:"title.class"}},{match:/non-sealed/,scope:"keyword"},{begin:[b.concat(/(?!else)/,c),/\s+/,c,/\s+/,/=(?!=)/],className:{1:"type",3:"variable",5:"operator"}},{begin:[/record/,/\s+/,c],className:{1:"keyword",3:"title.class"},contains:[g,a.C_LINE_COMMENT_MODE,a.C_BLOCK_COMMENT_MODE]},{beginKeywords:"new throw return else",relevance:0},{begin:["(?:"+d+"\\s+)",a.UNDERSCORE_IDENT_RE,/\s*(?=\()/],className:{2:"title.function"},keywords:e,contains:[{className:"params",begin:/\(/,end:/\)/,keywords:e,relevance:0,contains:[f,a.APOS_STRING_MODE,a.QUOTE_STRING_MODE,dx,a.C_BLOCK_COMMENT_MODE]},a.C_LINE_COMMENT_MODE,a.C_BLOCK_COMMENT_MODE]},dx,f]}},javascript:function(a){var b;let c=a.regex,d=/<[A-Za-z0-9\\._:-]+/,e=/\/[A-Za-z0-9\\._:-]+>|\/>/,f={$pattern:dy,keyword:dz,literal:dA,built_in:dF,"variable.language":dE},g="[0-9](_?[0-9])*",h=`\\.(${g})`,i="0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*",j={className:"number",variants:[{begin:`(\\b(${i})((${h})|\\.)?|(${h}))[eE][+-]?(${g})\\b`},{begin:`\\b(${i})\\b((${h})\\b|\\.)?|(${h})\\b`},{begin:"\\b(0|[1-9](_?[0-9])*)n\\b"},{begin:"\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\b"},{begin:"\\b0[bB][0-1](_?[0-1])*n?\\b"},{begin:"\\b0[oO][0-7](_?[0-7])*n?\\b"},{begin:"\\b0[0-7]+n?\\b"}],relevance:0},k={className:"subst",begin:"\\$\\{",end:"\\}",keywords:f,contains:[]},l={begin:".?html`",end:"",starts:{end:"`",returnEnd:!1,contains:[a.BACKSLASH_ESCAPE,k],subLanguage:"xml"}},m={begin:".?css`",end:"",starts:{end:"`",returnEnd:!1,contains:[a.BACKSLASH_ESCAPE,k],subLanguage:"css"}},n={begin:".?gql`",end:"",starts:{end:"`",returnEnd:!1,contains:[a.BACKSLASH_ESCAPE,k],subLanguage:"graphql"}},o={className:"string",begin:"`",end:"`",contains:[a.BACKSLASH_ESCAPE,k]},p={className:"comment",variants:[a.COMMENT(/\/\*\*(?!\/)/,"\\*/",{relevance:0,contains:[{begin:"(?=@[A-Za-z]+)",relevance:0,contains:[{className:"doctag",begin:"@[A-Za-z]+"},{className:"type",begin:"\\{",end:"\\}",excludeEnd:!0,excludeBegin:!0,relevance:0},{className:"variable",begin:dy+"(?=\\s*(-)|$)",endsParent:!0,relevance:0},{begin:/(?=[^\n])\s/,relevance:0}]}]}),a.C_BLOCK_COMMENT_MODE,a.C_LINE_COMMENT_MODE]},q=[a.APOS_STRING_MODE,a.QUOTE_STRING_MODE,l,m,n,o,{match:/\$\d+/},j];k.contains=q.concat({begin:/\{/,end:/\}/,keywords:f,contains:["self"].concat(q)});let r=[].concat(p,k.contains),s=r.concat([{begin:/(\s*)\(/,end:/\)/,keywords:f,contains:["self"].concat(r)}]),t={className:"params",begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:f,contains:s},u={variants:[{match:[/class/,/\s+/,dy,/\s+/,/extends/,/\s+/,c.concat(dy,"(",c.concat(/\./,dy),")*")],scope:{1:"keyword",3:"title.class",5:"keyword",7:"title.class.inherited"}},{match:[/class/,/\s+/,dy],scope:{1:"keyword",3:"title.class"}}]},v={relevance:0,match:c.either(/\bJSON/,/\b[A-Z][a-z]+([A-Z][a-z]*|\d)*/,/\b[A-Z]{2,}([A-Z][a-z]+|\d)+([A-Z][a-z]*)*/,/\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\d)*([A-Z][a-z]*)*/),className:"title.class",keywords:{_:[...dB,...dC]}},w={match:c.concat(/\b/,(b=[...dD,"super","import"].map(a=>`${a}\\s*\\(`),c.concat("(?!",b.join("|"),")")),dy,c.lookahead(/\s*\(/)),className:"title.function",relevance:0},x={begin:c.concat(/\./,c.lookahead(c.concat(dy,/(?![0-9A-Za-z$_(])/))),end:dy,excludeBegin:!0,keywords:"prototype",className:"property",relevance:0},y="(\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)|"+a.UNDERSCORE_IDENT_RE+")\\s*=>",z={match:[/const|var|let/,/\s+/,dy,/\s*/,/=\s*/,/(async\s*)?/,c.lookahead(y)],keywords:"async",className:{1:"keyword",3:"title.function"},contains:[t]};return{name:"JavaScript",aliases:["js","jsx","mjs","cjs"],keywords:f,exports:{PARAMS_CONTAINS:s,CLASS_REFERENCE:v},illegal:/#(?![$_A-z])/,contains:[a.SHEBANG({label:"shebang",binary:"node",relevance:5}),{label:"use_strict",className:"meta",relevance:10,begin:/^\s*['"]use (strict|asm)['"]/},a.APOS_STRING_MODE,a.QUOTE_STRING_MODE,l,m,n,o,p,{match:/\$\d+/},j,v,{scope:"attr",match:dy+c.lookahead(":"),relevance:0},z,{begin:"("+a.RE_STARTERS_RE+"|\\b(case|return|throw)\\b)\\s*",keywords:"return throw case",relevance:0,contains:[p,a.REGEXP_MODE,{className:"function",begin:y,returnBegin:!0,end:"\\s*=>",contains:[{className:"params",variants:[{begin:a.UNDERSCORE_IDENT_RE,relevance:0},{className:null,begin:/\(\s*\)/,skip:!0},{begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:f,contains:s}]}]},{begin:/,/,relevance:0},{match:/\s+/,relevance:0},{variants:[{begin:"<>",end:"</>"},{match:/<[A-Za-z0-9\\._:-]+\s*\/>/},{begin:d,"on:begin":(a,b)=>{let c,d=a[0].length+a.index,e=a.input[d];if("<"===e||","===e)return void b.ignoreMatch();">"!==e||((a,{after:b})=>{let c="</"+a[0].slice(1);return -1!==a.input.indexOf(c,b)})(a,{after:d})||b.ignoreMatch();let f=a.input.substring(d);if((c=f.match(/^\s*=/))||(c=f.match(/^\s+extends\s+/))&&0===c.index)return void b.ignoreMatch()},end:e}],subLanguage:"xml",contains:[{begin:d,end:e,skip:!0,contains:["self"]}]}]},{variants:[{match:[/function/,/\s+/,dy,/(?=\s*\()/]},{match:[/function/,/\s*(?=\()/]}],className:{1:"keyword",3:"title.function"},label:"func.def",contains:[t],illegal:/%/},{beginKeywords:"while if switch catch for"},{begin:"\\b(?!function)"+a.UNDERSCORE_IDENT_RE+"\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)\\s*\\{",returnBegin:!0,label:"func.def",contains:[t,a.inherit(a.TITLE_MODE,{begin:dy,className:"title.function"})]},{match:/\.\.\./,relevance:0},x,{match:"\\$"+dy,relevance:0},{match:[/\bconstructor(?=\s*\()/],className:{1:"title.function"},contains:[t]},w,{relevance:0,match:/\b[A-Z][A-Z_0-9]+\b/,className:"variable.constant"},u,{match:[/get|set/,/\s+/,dy,/(?=\()/],className:{1:"keyword",3:"title.function"},contains:[{begin:/\(\)/},t]},{match:/\$[(.]/}]}},json:function(a){let b=["true","false","null"],c={scope:"literal",beginKeywords:b.join(" ")};return{name:"JSON",aliases:["jsonc"],keywords:{literal:b},contains:[{className:"attr",begin:/"(\\.|[^\\"\r\n])*"(?=\s*:)/,relevance:1.01},{match:/[{}[\],:]/,className:"punctuation",relevance:0},a.QUOTE_STRING_MODE,c,a.C_NUMBER_MODE,a.C_LINE_COMMENT_MODE,a.C_BLOCK_COMMENT_MODE],illegal:"\\S"}},kotlin:function(a){let b={keyword:"abstract as val var vararg get set class object open private protected public noinline crossinline dynamic final enum if else do while for when throw try catch finally import package is in fun override companion reified inline lateinit init interface annotation data sealed internal infix operator out by constructor super tailrec where const inner suspend typealias external expect actual",built_in:"Byte Short Char Int Long Boolean Float Double Void Unit Nothing",literal:"true false null"},c={className:"symbol",begin:a.UNDERSCORE_IDENT_RE+"@"},d={className:"subst",begin:/\$\{/,end:/\}/,contains:[a.C_NUMBER_MODE]},e={className:"variable",begin:"\\$"+a.UNDERSCORE_IDENT_RE},f={className:"string",variants:[{begin:'"""',end:'"""(?=[^"])',contains:[e,d]},{begin:"'",end:"'",illegal:/\n/,contains:[a.BACKSLASH_ESCAPE]},{begin:'"',end:'"',illegal:/\n/,contains:[a.BACKSLASH_ESCAPE,e,d]}]};d.contains.push(f);let g={className:"meta",begin:"@(?:file|property|field|get|set|receiver|param|setparam|delegate)\\s*:(?:\\s*"+a.UNDERSCORE_IDENT_RE+")?"},h={className:"meta",begin:"@"+a.UNDERSCORE_IDENT_RE,contains:[{begin:/\(/,end:/\)/,contains:[a.inherit(f,{className:"string"}),"self"]}]},i=a.COMMENT("/\\*","\\*/",{contains:[a.C_BLOCK_COMMENT_MODE]}),j={variants:[{className:"type",begin:a.UNDERSCORE_IDENT_RE},{begin:/\(/,end:/\)/,contains:[]}]};return j.variants[1].contains=[j],j.variants[1].contains=[j],{name:"Kotlin",aliases:["kt","kts"],keywords:b,contains:[a.COMMENT("/\\*\\*","\\*/",{relevance:0,contains:[{className:"doctag",begin:"@[A-Za-z]+"}]}),a.C_LINE_COMMENT_MODE,i,{className:"keyword",begin:/\b(break|continue|return|this)\b/,starts:{contains:[{className:"symbol",begin:/@\w+/}]}},c,g,h,{className:"function",beginKeywords:"fun",end:"[(]|$",returnBegin:!0,excludeEnd:!0,keywords:b,relevance:5,contains:[{begin:a.UNDERSCORE_IDENT_RE+"\\s*\\(",returnBegin:!0,relevance:0,contains:[a.UNDERSCORE_TITLE_MODE]},{className:"type",begin:/</,end:/>/,keywords:"reified",relevance:0},{className:"params",begin:/\(/,end:/\)/,endsParent:!0,keywords:b,relevance:0,contains:[{begin:/:/,end:/[=,\/]/,endsWithParent:!0,contains:[j,a.C_LINE_COMMENT_MODE,i],relevance:0},a.C_LINE_COMMENT_MODE,i,g,h,f,a.C_NUMBER_MODE]},i]},{begin:[/class|interface|trait/,/\s+/,a.UNDERSCORE_IDENT_RE],beginScope:{3:"title.class"},keywords:"class interface trait",end:/[:\{(]|$/,excludeEnd:!0,illegal:"extends implements",contains:[{beginKeywords:"public protected internal private constructor"},a.UNDERSCORE_TITLE_MODE,{className:"type",begin:/</,end:/>/,excludeBegin:!0,excludeEnd:!0,relevance:0},{className:"type",begin:/[,:]\s*/,end:/[<\(,){\s]|$/,excludeBegin:!0,returnEnd:!0},g,h]},f,{className:"meta",begin:"^#!/usr/bin/env",end:"$",illegal:"\n"},dJ]}},less:function(a){let b={IMPORTANT:{scope:"meta",begin:"!important"},BLOCK_COMMENT:a.C_BLOCK_COMMENT_MODE,HEXCOLOR:{scope:"number",begin:/#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\b/},FUNCTION_DISPATCH:{className:"built_in",begin:/[\w-]+(?=\()/},ATTRIBUTE_SELECTOR_MODE:{scope:"selector-attr",begin:/\[/,end:/\]/,illegal:"$",contains:[a.APOS_STRING_MODE,a.QUOTE_STRING_MODE]},CSS_NUMBER_MODE:{scope:"number",begin:a.NUMBER_RE+"(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?",relevance:0},CSS_VARIABLE:{className:"attr",begin:/--[A-Za-z_][A-Za-z0-9_-]*/}},c="[\\w-]+",d="("+c+"|@\\{"+c+"\\})",e=[],f=[],g=function(a){return{className:"string",begin:"~?"+a+".*?"+a}},h=function(a,b,c){return{className:a,begin:b,relevance:c}},i={$pattern:/[a-z-]+/,keyword:"and or not only",attribute:dL.join(" ")};f.push(a.C_LINE_COMMENT_MODE,a.C_BLOCK_COMMENT_MODE,g("'"),g('"'),b.CSS_NUMBER_MODE,{begin:"(url|data-uri)\\(",starts:{className:"string",end:"[\\)\\n]",excludeEnd:!0}},b.HEXCOLOR,{begin:"\\(",end:"\\)",contains:f,keywords:i,relevance:0},h("variable","@@?"+c,10),h("variable","@\\{"+c+"\\}"),h("built_in","~?`[^`]*?`"),{className:"attribute",begin:c+"\\s*:",end:":",returnBegin:!0,excludeEnd:!0},b.IMPORTANT,{beginKeywords:"and not"},b.FUNCTION_DISPATCH);let j=f.concat({begin:/\{/,end:/\}/,contains:e}),k={beginKeywords:"when",endsWithParent:!0,contains:[{beginKeywords:"and not"}].concat(f)},l={begin:d+"\\s*:",returnBegin:!0,end:/[;}]/,relevance:0,contains:[{begin:/-(webkit|moz|ms|o)-/},b.CSS_VARIABLE,{className:"attribute",begin:"\\b("+dO.join("|")+")\\b",end:/(?=:)/,starts:{endsWithParent:!0,illegal:"[<=$]",relevance:0,contains:f}}]},m={variants:[{begin:"[\\.#:&\\[>]",end:"[;{}]"},{begin:d,end:/\{/}],returnBegin:!0,returnEnd:!0,illegal:"[<='$\"]",relevance:0,contains:[a.C_LINE_COMMENT_MODE,a.C_BLOCK_COMMENT_MODE,k,h("keyword","all\\b"),h("variable","@\\{"+c+"\\}"),{begin:"\\b("+dK.join("|")+")\\b",className:"selector-tag"},b.CSS_NUMBER_MODE,h("selector-tag",d,0),h("selector-id","#"+d),h("selector-class","\\."+d,0),h("selector-tag","&",0),b.ATTRIBUTE_SELECTOR_MODE,{className:"selector-pseudo",begin:":("+dM.join("|")+")"},{className:"selector-pseudo",begin:":(:)?("+dN.join("|")+")"},{begin:/\(/,end:/\)/,relevance:0,contains:j},{begin:"!important"},b.FUNCTION_DISPATCH]},n={begin:c+":(:)?"+`(${dP.join("|")})`,returnBegin:!0,contains:[m]};return e.push(a.C_LINE_COMMENT_MODE,a.C_BLOCK_COMMENT_MODE,{className:"keyword",begin:"@(import|media|charset|font-face|(-[a-z]+-)?keyframes|supports|document|namespace|page|viewport|host)\\b",starts:{end:"[;{}]",keywords:i,returnEnd:!0,contains:f,relevance:0}},{className:"variable",variants:[{begin:"@"+c+"\\s*:",relevance:15},{begin:"@"+c}],starts:{end:"[;}]",returnEnd:!0,contains:j}},n,l,m,k,b.FUNCTION_DISPATCH),{name:"Less",case_insensitive:!0,illegal:"[=>'/<($\"]",contains:e}},lua:function(a){let b="\\[=*\\[",c="\\]=*\\]",d={begin:b,end:c,contains:["self"]},e=[a.COMMENT("--(?!"+b+")","$"),a.COMMENT("--"+b,c,{contains:[d],relevance:10})];return{name:"Lua",aliases:["pluto"],keywords:{$pattern:a.UNDERSCORE_IDENT_RE,literal:"true false nil",keyword:"and break do else elseif end for goto if in local not or repeat return then until while",built_in:"_G _ENV _VERSION __index __newindex __mode __call __metatable __tostring __len __gc __add __sub __mul __div __mod __pow __concat __unm __eq __lt __le assert collectgarbage dofile error getfenv getmetatable ipairs load loadfile loadstring module next pairs pcall print rawequal rawget rawset require select setfenv setmetatable tonumber tostring type unpack xpcall arg self coroutine resume yield status wrap create running debug getupvalue debug sethook getmetatable gethook setmetatable setlocal traceback setfenv getinfo setupvalue getlocal getregistry getfenv io lines write close flush open output type read stderr stdin input stdout popen tmpfile math log max acos huge ldexp pi cos tanh pow deg tan cosh sinh random randomseed frexp ceil floor rad abs sqrt modf asin min mod fmod log10 atan2 exp sin atan os exit setlocale date getenv difftime remove time clock tmpname rename execute package preload loadlib loaded loaders cpath config path seeall string sub upper len gfind rep find match char dump gmatch reverse byte format gsub lower table setn insert getn foreachi maxn foreach concat sort remove"},contains:e.concat([{className:"function",beginKeywords:"function",end:"\\)",contains:[a.inherit(a.TITLE_MODE,{begin:"([_a-zA-Z]\\w*\\.)*([_a-zA-Z]\\w*:)?[_a-zA-Z]\\w*"}),{className:"params",begin:"\\(",endsWithParent:!0,contains:e}].concat(e)},a.C_NUMBER_MODE,a.APOS_STRING_MODE,a.QUOTE_STRING_MODE,{className:"string",begin:b,end:c,contains:[d],relevance:5}])}},makefile:function(a){let b={className:"variable",variants:[{begin:"\\$\\("+a.UNDERSCORE_IDENT_RE+"\\)",contains:[a.BACKSLASH_ESCAPE]},{begin:/\$[@%<?\^\+\*]/}]},c={className:"string",begin:/"/,end:/"/,contains:[a.BACKSLASH_ESCAPE,b]},d={begin:"^"+a.UNDERSCORE_IDENT_RE+"\\s*(?=[:+?]?=)"};return{name:"Makefile",aliases:["mk","mak","make"],keywords:{$pattern:/[\w-]+/,keyword:"define endef undefine ifdef ifndef ifeq ifneq else endif include -include sinclude override export unexport private vpath"},contains:[a.HASH_COMMENT_MODE,b,c,{className:"variable",begin:/\$\([\w-]+\s/,end:/\)/,keywords:{built_in:"subst patsubst strip findstring filter filter-out sort word wordlist firstword lastword dir notdir suffix basename addsuffix addprefix join wildcard realpath abspath error warning shell origin flavor foreach if or and call eval file value"},contains:[b,c]},d,{className:"meta",begin:/^\.PHONY:/,end:/$/,keywords:{$pattern:/[\.\w]+/,keyword:".PHONY"}},{className:"section",begin:/^[^\s]+:/,end:/$/,contains:[b]}]}},markdown:function(a){let b=a.regex,c={begin:/<\/?[A-Za-z_]/,end:">",subLanguage:"xml",relevance:0},d={variants:[{begin:/\[.+?\]\[.*?\]/,relevance:0},{begin:/\[.+?\]\(((data|javascript|mailto):|(?:http|ftp)s?:\/\/).*?\)/,relevance:2},{begin:b.concat(/\[.+?\]\(/,/[A-Za-z][A-Za-z0-9+.-]*/,/:\/\/.*?\)/),relevance:2},{begin:/\[.+?\]\([./?&#].*?\)/,relevance:1},{begin:/\[.*?\]\(.*?\)/,relevance:0}],returnBegin:!0,contains:[{match:/\[(?=\])/},{className:"string",relevance:0,begin:"\\[",end:"\\]",excludeBegin:!0,returnEnd:!0},{className:"link",relevance:0,begin:"\\]\\(",end:"\\)",excludeBegin:!0,excludeEnd:!0},{className:"symbol",relevance:0,begin:"\\]\\[",end:"\\]",excludeBegin:!0,excludeEnd:!0}]},e={className:"strong",contains:[],variants:[{begin:/_{2}(?!\s)/,end:/_{2}/},{begin:/\*{2}(?!\s)/,end:/\*{2}/}]},f={className:"emphasis",contains:[],variants:[{begin:/\*(?![*\s])/,end:/\*/},{begin:/_(?![_\s])/,end:/_/,relevance:0}]},g=a.inherit(e,{contains:[]}),h=a.inherit(f,{contains:[]});e.contains.push(h),f.contains.push(g);let i=[c,d];[e,f,g,h].forEach(a=>{a.contains=a.contains.concat(i)});let j={className:"section",variants:[{begin:"^#{1,6}",end:"$",contains:i=i.concat(e,f)},{begin:"(?=^.+?\\n[=-]{2,}$)",contains:[{begin:"^[=-]*$"},{begin:"^",end:"\\n",contains:i}]}]};return{name:"Markdown",aliases:["md","mkdown","mkd"],contains:[j,c,{className:"bullet",begin:"^[ 	]*([*+-]|(\\d+\\.))(?=\\s+)",end:"\\s+",excludeEnd:!0},e,f,{className:"quote",begin:"^>\\s+",contains:i,end:"$"},{className:"code",variants:[{begin:"(`{3,})[^`](.|\\n)*?\\1`*[ ]*"},{begin:"(~{3,})[^~](.|\\n)*?\\1~*[ ]*"},{begin:"```",end:"```+[ ]*$"},{begin:"~~~",end:"~~~+[ ]*$"},{begin:"`.+?`"},{begin:"(?=^( {4}|\\t))",contains:[{begin:"^( {4}|\\t)",end:"(\\n)$"}],relevance:0}]},{begin:"^[-\\*]{3,}",end:"$"},d,{begin:/^\[[^\n]+\]:/,returnBegin:!0,contains:[{className:"symbol",begin:/\[/,end:/\]/,excludeBegin:!0,excludeEnd:!0},{className:"link",begin:/:\s*/,end:/$/,excludeBegin:!0}]},{scope:"literal",match:/&([a-zA-Z0-9]+|#[0-9]{1,7}|#[Xx][0-9a-fA-F]{1,6});/}]}},objectivec:function(a){let b=/[a-zA-Z@][a-zA-Z0-9_]*/,c={$pattern:b,keyword:["@interface","@class","@protocol","@implementation"]};return{name:"Objective-C",aliases:["mm","objc","obj-c","obj-c++","objective-c++"],keywords:{"variable.language":["this","super"],$pattern:b,keyword:["while","export","sizeof","typedef","const","struct","for","union","volatile","static","mutable","if","do","return","goto","enum","else","break","extern","asm","case","default","register","explicit","typename","switch","continue","inline","readonly","assign","readwrite","self","@synchronized","id","typeof","nonatomic","IBOutlet","IBAction","strong","weak","copy","in","out","inout","bycopy","byref","oneway","__strong","__weak","__block","__autoreleasing","@private","@protected","@public","@try","@property","@end","@throw","@catch","@finally","@autoreleasepool","@synthesize","@dynamic","@selector","@optional","@required","@encode","@package","@import","@defs","@compatibility_alias","__bridge","__bridge_transfer","__bridge_retained","__bridge_retain","__covariant","__contravariant","__kindof","_Nonnull","_Nullable","_Null_unspecified","__FUNCTION__","__PRETTY_FUNCTION__","__attribute__","getter","setter","retain","unsafe_unretained","nonnull","nullable","null_unspecified","null_resettable","class","instancetype","NS_DESIGNATED_INITIALIZER","NS_UNAVAILABLE","NS_REQUIRES_SUPER","NS_RETURNS_INNER_POINTER","NS_INLINE","NS_AVAILABLE","NS_DEPRECATED","NS_ENUM","NS_OPTIONS","NS_SWIFT_UNAVAILABLE","NS_ASSUME_NONNULL_BEGIN","NS_ASSUME_NONNULL_END","NS_REFINED_FOR_SWIFT","NS_SWIFT_NAME","NS_SWIFT_NOTHROW","NS_DURING","NS_HANDLER","NS_ENDHANDLER","NS_VALUERETURN","NS_VOIDRETURN"],literal:["false","true","FALSE","TRUE","nil","YES","NO","NULL"],built_in:["dispatch_once_t","dispatch_queue_t","dispatch_sync","dispatch_async","dispatch_once"],type:["int","float","char","unsigned","signed","short","long","double","wchar_t","unichar","void","bool","BOOL","id|0","_Bool"]},illegal:"</",contains:[{className:"built_in",begin:"\\b(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)\\w+"},a.C_LINE_COMMENT_MODE,a.C_BLOCK_COMMENT_MODE,a.C_NUMBER_MODE,a.QUOTE_STRING_MODE,a.APOS_STRING_MODE,{className:"string",variants:[{begin:'@"',end:'"',illegal:"\\n",contains:[a.BACKSLASH_ESCAPE]}]},{className:"meta",begin:/#\s*[a-z]+\b/,end:/$/,keywords:{keyword:"if else elif endif define undef warning error line pragma ifdef ifndef include"},contains:[{begin:/\\\n/,relevance:0},a.inherit(a.QUOTE_STRING_MODE,{className:"string"}),{className:"string",begin:/<.*?>/,end:/$/,illegal:"\\n"},a.C_LINE_COMMENT_MODE,a.C_BLOCK_COMMENT_MODE]},{className:"class",begin:"("+c.keyword.join("|")+")\\b",end:/(\{|$)/,excludeEnd:!0,keywords:c,contains:[a.UNDERSCORE_TITLE_MODE]},{begin:"\\."+a.UNDERSCORE_IDENT_RE,relevance:0}]}},perl:function(a){let b=a.regex,c=/[dualxmsipngr]{0,12}/,d={$pattern:/[\w.]+/,keyword:"abs accept alarm and atan2 bind binmode bless break caller chdir chmod chomp chop chown chr chroot class close closedir connect continue cos crypt dbmclose dbmopen defined delete die do dump each else elsif endgrent endhostent endnetent endprotoent endpwent endservent eof eval exec exists exit exp fcntl field fileno flock for foreach fork format formline getc getgrent getgrgid getgrnam gethostbyaddr gethostbyname gethostent getlogin getnetbyaddr getnetbyname getnetent getpeername getpgrp getpriority getprotobyname getprotobynumber getprotoent getpwent getpwnam getpwuid getservbyname getservbyport getservent getsockname getsockopt given glob gmtime goto grep gt hex if index int ioctl join keys kill last lc lcfirst length link listen local localtime log lstat lt ma map method mkdir msgctl msgget msgrcv msgsnd my ne next no not oct open opendir or ord our pack package pipe pop pos print printf prototype push q|0 qq quotemeta qw qx rand read readdir readline readlink readpipe recv redo ref rename require reset return reverse rewinddir rindex rmdir say scalar seek seekdir select semctl semget semop send setgrent sethostent setnetent setpgrp setpriority setprotoent setpwent setservent setsockopt shift shmctl shmget shmread shmwrite shutdown sin sleep socket socketpair sort splice split sprintf sqrt srand stat state study sub substr symlink syscall sysopen sysread sysseek system syswrite tell telldir tie tied time times tr truncate uc ucfirst umask undef unless unlink unpack unshift untie until use utime values vec wait waitpid wantarray warn when while write x|0 xor y|0"},e={className:"subst",begin:"[$@]\\{",end:"\\}",keywords:d},f={begin:/->\{/,end:/\}/},g={scope:"attr",match:/\s+:\s*\w+(\s*\(.*?\))?/},h={scope:"variable",variants:[{begin:/\$\d/},{begin:b.concat(/[$%@](?!")(\^\w\b|#\w+(::\w+)*|\{\w+\}|\w+(::\w*)*)/,"(?![A-Za-z])(?![@$%])")},{begin:/[$%@](?!")[^\s\w{=]|\$=/,relevance:0}],contains:[g]},i={className:"number",variants:[{match:/0?\.[0-9][0-9_]+\b/},{match:/\bv?(0|[1-9][0-9_]*(\.[0-9_]+)?|[1-9][0-9_]*)\b/},{match:/\b0[0-7][0-7_]*\b/},{match:/\b0x[0-9a-fA-F][0-9a-fA-F_]*\b/},{match:/\b0b[0-1][0-1_]*\b/}],relevance:0},j=[a.BACKSLASH_ESCAPE,e,h],k=[/!/,/\//,/\|/,/\?/,/'/,/"/,/#/],l=(a,d,e="\\1")=>{let f="\\1"===e?e:b.concat(e,d);return b.concat(b.concat("(?:",a,")"),d,/(?:\\.|[^\\\/])*?/,f,/(?:\\.|[^\\\/])*?/,e,c)},m=(a,d,e)=>b.concat(b.concat("(?:",a,")"),d,/(?:\\.|[^\\\/])*?/,e,c),n=[h,a.HASH_COMMENT_MODE,a.COMMENT(/^=\w/,/=cut/,{endsWithParent:!0}),f,{className:"string",contains:j,variants:[{begin:"q[qwxr]?\\s*\\(",end:"\\)",relevance:5},{begin:"q[qwxr]?\\s*\\[",end:"\\]",relevance:5},{begin:"q[qwxr]?\\s*\\{",end:"\\}",relevance:5},{begin:"q[qwxr]?\\s*\\|",end:"\\|",relevance:5},{begin:"q[qwxr]?\\s*<",end:">",relevance:5},{begin:"qw\\s+q",end:"q",relevance:5},{begin:"'",end:"'",contains:[a.BACKSLASH_ESCAPE]},{begin:'"',end:'"'},{begin:"`",end:"`",contains:[a.BACKSLASH_ESCAPE]},{begin:/\{\w+\}/,relevance:0},{begin:"-?\\w+\\s*=>",relevance:0}]},i,{begin:"(\\/\\/|"+a.RE_STARTERS_RE+"|\\b(split|return|print|reverse|grep)\\b)\\s*",keywords:"split return print reverse grep",relevance:0,contains:[a.HASH_COMMENT_MODE,{className:"regexp",variants:[{begin:l("s|tr|y",b.either(...k,{capture:!0}))},{begin:l("s|tr|y","\\(","\\)")},{begin:l("s|tr|y","\\[","\\]")},{begin:l("s|tr|y","\\{","\\}")}],relevance:2},{className:"regexp",variants:[{begin:/(m|qr)\/\//,relevance:0},{begin:m("(?:m|qr)?",/\//,/\//)},{begin:m("m|qr",b.either(...k,{capture:!0}),/\1/)},{begin:m("m|qr",/\(/,/\)/)},{begin:m("m|qr",/\[/,/\]/)},{begin:m("m|qr",/\{/,/\}/)}]}]},{className:"function",beginKeywords:"sub method",end:"(\\s*\\(.*?\\))?[;{]",excludeEnd:!0,relevance:5,contains:[a.TITLE_MODE,g]},{className:"class",beginKeywords:"class",end:"[;{]",excludeEnd:!0,relevance:5,contains:[a.TITLE_MODE,g,i]},{begin:"-\\w\\b",relevance:0},{begin:"^__DATA__$",end:"^__END__$",subLanguage:"mojolicious",contains:[{begin:"^@@.*",end:"$",className:"comment"}]}];return e.contains=n,f.contains=n,{name:"Perl",aliases:["pl","pm"],keywords:d,contains:n}},php:function(a){let b=a.regex,c=/(?![A-Za-z0-9])(?![$])/,d=b.concat(/[a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*/,c),e=b.concat(/(\\?[A-Z][a-z0-9_\x7f-\xff]+|\\?[A-Z]+(?=[A-Z][a-z0-9_\x7f-\xff])){1,}/,c),f=b.concat(/[A-Z]+/,c),g={scope:"variable",match:"\\$+"+d},h={scope:"subst",variants:[{begin:/\$\w+/},{begin:/\{\$/,end:/\}/}]},i=a.inherit(a.APOS_STRING_MODE,{illegal:null}),j=a.inherit(a.QUOTE_STRING_MODE,{illegal:null,contains:a.QUOTE_STRING_MODE.contains.concat(h)}),k={begin:/<<<[ \t]*(?:(\w+)|"(\w+)")\n/,end:/[ \t]*(\w+)\b/,contains:a.QUOTE_STRING_MODE.contains.concat(h),"on:begin":(a,b)=>{b.data._beginMatch=a[1]||a[2]},"on:end":(a,b)=>{b.data._beginMatch!==a[1]&&b.ignoreMatch()}},l=a.END_SAME_AS_BEGIN({begin:/<<<[ \t]*'(\w+)'\n/,end:/[ \t]*(\w+)\b/}),m="[ 	\n]",n={scope:"string",variants:[j,i,k,l]},o={scope:"number",variants:[{begin:"\\b0[bB][01]+(?:_[01]+)*\\b"},{begin:"\\b0[oO][0-7]+(?:_[0-7]+)*\\b"},{begin:"\\b0[xX][\\da-fA-F]+(?:_[\\da-fA-F]+)*\\b"},{begin:"(?:\\b\\d+(?:_\\d+)*(\\.(?:\\d+(?:_\\d+)*))?|\\B\\.\\d+)(?:[eE][+-]?\\d+)?"}],relevance:0},p=["false","null","true"],q=["__CLASS__","__DIR__","__FILE__","__FUNCTION__","__COMPILER_HALT_OFFSET__","__LINE__","__METHOD__","__NAMESPACE__","__TRAIT__","die","echo","exit","include","include_once","print","require","require_once","array","abstract","and","as","binary","bool","boolean","break","callable","case","catch","class","clone","const","continue","declare","default","do","double","else","elseif","empty","enddeclare","endfor","endforeach","endif","endswitch","endwhile","enum","eval","extends","final","finally","float","for","foreach","from","global","goto","if","implements","instanceof","insteadof","int","integer","interface","isset","iterable","list","match|0","mixed","new","never","object","or","private","protected","public","readonly","real","return","string","switch","throw","trait","try","unset","use","var","void","while","xor","yield"],r=["Error|0","AppendIterator","ArgumentCountError","ArithmeticError","ArrayIterator","ArrayObject","AssertionError","BadFunctionCallException","BadMethodCallException","CachingIterator","CallbackFilterIterator","CompileError","Countable","DirectoryIterator","DivisionByZeroError","DomainException","EmptyIterator","ErrorException","Exception","FilesystemIterator","FilterIterator","GlobIterator","InfiniteIterator","InvalidArgumentException","IteratorIterator","LengthException","LimitIterator","LogicException","MultipleIterator","NoRewindIterator","OutOfBoundsException","OutOfRangeException","OuterIterator","OverflowException","ParentIterator","ParseError","RangeException","RecursiveArrayIterator","RecursiveCachingIterator","RecursiveCallbackFilterIterator","RecursiveDirectoryIterator","RecursiveFilterIterator","RecursiveIterator","RecursiveIteratorIterator","RecursiveRegexIterator","RecursiveTreeIterator","RegexIterator","RuntimeException","SeekableIterator","SplDoublyLinkedList","SplFileInfo","SplFileObject","SplFixedArray","SplHeap","SplMaxHeap","SplMinHeap","SplObjectStorage","SplObserver","SplPriorityQueue","SplQueue","SplStack","SplSubject","SplTempFileObject","TypeError","UnderflowException","UnexpectedValueException","UnhandledMatchError","ArrayAccess","BackedEnum","Closure","Fiber","Generator","Iterator","IteratorAggregate","Serializable","Stringable","Throwable","Traversable","UnitEnum","WeakReference","WeakMap","Directory","__PHP_Incomplete_Class","parent","php_user_filter","self","static","stdClass"],s={keyword:q,literal:(a=>{let b=[];return a.forEach(a=>{b.push(a),a.toLowerCase()===a?b.push(a.toUpperCase()):b.push(a.toLowerCase())}),b})(p),built_in:r},t=a=>a.map(a=>a.replace(/\|\d+$/,"")),u={variants:[{match:[/new/,b.concat(m,"+"),b.concat("(?!",t(r).join("\\b|"),"\\b)"),e],scope:{1:"keyword",4:"title.class"}}]},v=b.concat(d,"\\b(?!\\()"),w={variants:[{match:[b.concat(/::/,b.lookahead(/(?!class\b)/)),v],scope:{2:"variable.constant"}},{match:[/::/,/class/],scope:{2:"variable.language"}},{match:[e,b.concat(/::/,b.lookahead(/(?!class\b)/)),v],scope:{1:"title.class",3:"variable.constant"}},{match:[e,b.concat("::",b.lookahead(/(?!class\b)/))],scope:{1:"title.class"}},{match:[e,/::/,/class/],scope:{1:"title.class",3:"variable.language"}}]},x={scope:"attr",match:b.concat(d,b.lookahead(":"),b.lookahead(/(?!::)/))},y={relevance:0,begin:/\(/,end:/\)/,keywords:s,contains:[x,g,w,a.C_BLOCK_COMMENT_MODE,n,o,u]},z={relevance:0,match:[/\b/,b.concat("(?!fn\\b|function\\b|",t(q).join("\\b|"),"|",t(r).join("\\b|"),"\\b)"),d,b.concat(m,"*"),b.lookahead(/(?=\()/)],scope:{3:"title.function.invoke"},contains:[y]};y.contains.push(z);let A=[x,w,a.C_BLOCK_COMMENT_MODE,n,o,u],B={begin:b.concat(/#\[\s*\\?/,b.either(e,f)),beginScope:"meta",end:/]/,endScope:"meta",keywords:{literal:p,keyword:["new","array"]},contains:[{begin:/\[/,end:/]/,keywords:{literal:p,keyword:["new","array"]},contains:["self",...A]},...A,{scope:"meta",variants:[{match:e},{match:f}]}]};return{case_insensitive:!1,keywords:s,contains:[B,a.HASH_COMMENT_MODE,a.COMMENT("//","$"),a.COMMENT("/\\*","\\*/",{contains:[{scope:"doctag",match:"@[A-Za-z]+"}]}),{match:/__halt_compiler\(\);/,keywords:"__halt_compiler",starts:{scope:"comment",end:a.MATCH_NOTHING_RE,contains:[{match:/\?>/,scope:"meta",endsParent:!0}]}},{scope:"meta",variants:[{begin:/<\?php/,relevance:10},{begin:/<\?=/},{begin:/<\?/,relevance:.1},{begin:/\?>/}]},{scope:"variable.language",match:/\$this\b/},g,z,w,{match:[/const/,/\s/,d],scope:{1:"keyword",3:"variable.constant"}},u,{scope:"function",relevance:0,beginKeywords:"fn function",end:/[;{]/,excludeEnd:!0,illegal:"[$%\\[]",contains:[{beginKeywords:"use"},a.UNDERSCORE_TITLE_MODE,{begin:"=>",endsParent:!0},{scope:"params",begin:"\\(",end:"\\)",excludeBegin:!0,excludeEnd:!0,keywords:s,contains:["self",B,g,w,a.C_BLOCK_COMMENT_MODE,n,o]}]},{scope:"class",variants:[{beginKeywords:"enum",illegal:/[($"]/},{beginKeywords:"class interface trait",illegal:/[:($"]/}],relevance:0,end:/\{/,excludeEnd:!0,contains:[{beginKeywords:"extends implements"},a.UNDERSCORE_TITLE_MODE]},{beginKeywords:"namespace",relevance:0,end:";",illegal:/[.']/,contains:[a.inherit(a.UNDERSCORE_TITLE_MODE,{scope:"title.class"})]},{beginKeywords:"use",relevance:0,end:";",contains:[{match:/\b(as|const|function)\b/,scope:"keyword"},a.UNDERSCORE_TITLE_MODE]},n,o]}},"php-template":function(a){return{name:"PHP template",subLanguage:"xml",contains:[{begin:/<\?(php|=)?/,end:/\?>/,subLanguage:"php",contains:[{begin:"/\\*",end:"\\*/",skip:!0},{begin:'b"',end:'"',skip:!0},{begin:"b'",end:"'",skip:!0},a.inherit(a.APOS_STRING_MODE,{illegal:null,className:null,contains:null,skip:!0}),a.inherit(a.QUOTE_STRING_MODE,{illegal:null,className:null,contains:null,skip:!0})]}]}},plaintext:function(a){return{name:"Plain text",aliases:["text","txt"],disableAutodetect:!0}},python:function(a){let b=a.regex,c=/[\p{XID_Start}_]\p{XID_Continue}*/u,d=["and","as","assert","async","await","break","case","class","continue","def","del","elif","else","except","finally","for","from","global","if","import","in","is","lambda","match","nonlocal|10","not","or","pass","raise","return","try","while","with","yield"],e={$pattern:/[A-Za-z]\w+|__\w+__/,keyword:d,built_in:["__import__","abs","all","any","ascii","bin","bool","breakpoint","bytearray","bytes","callable","chr","classmethod","compile","complex","delattr","dict","dir","divmod","enumerate","eval","exec","filter","float","format","frozenset","getattr","globals","hasattr","hash","help","hex","id","input","int","isinstance","issubclass","iter","len","list","locals","map","max","memoryview","min","next","object","oct","open","ord","pow","print","property","range","repr","reversed","round","set","setattr","slice","sorted","staticmethod","str","sum","super","tuple","type","vars","zip"],literal:["__debug__","Ellipsis","False","None","NotImplemented","True"],type:["Any","Callable","Coroutine","Dict","List","Literal","Generic","Optional","Sequence","Set","Tuple","Type","Union"]},f={className:"meta",begin:/^(>>>|\.\.\.) /},g={className:"subst",begin:/\{/,end:/\}/,keywords:e,illegal:/#/},h={begin:/\{\{/,relevance:0},i={className:"string",contains:[a.BACKSLASH_ESCAPE],variants:[{begin:/([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?'''/,end:/'''/,contains:[a.BACKSLASH_ESCAPE,f],relevance:10},{begin:/([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?"""/,end:/"""/,contains:[a.BACKSLASH_ESCAPE,f],relevance:10},{begin:/([fF][rR]|[rR][fF]|[fF])'''/,end:/'''/,contains:[a.BACKSLASH_ESCAPE,f,h,g]},{begin:/([fF][rR]|[rR][fF]|[fF])"""/,end:/"""/,contains:[a.BACKSLASH_ESCAPE,f,h,g]},{begin:/([uU]|[rR])'/,end:/'/,relevance:10},{begin:/([uU]|[rR])"/,end:/"/,relevance:10},{begin:/([bB]|[bB][rR]|[rR][bB])'/,end:/'/},{begin:/([bB]|[bB][rR]|[rR][bB])"/,end:/"/},{begin:/([fF][rR]|[rR][fF]|[fF])'/,end:/'/,contains:[a.BACKSLASH_ESCAPE,h,g]},{begin:/([fF][rR]|[rR][fF]|[fF])"/,end:/"/,contains:[a.BACKSLASH_ESCAPE,h,g]},a.APOS_STRING_MODE,a.QUOTE_STRING_MODE]},j="[0-9](_?[0-9])*",k=`(\\b(${j}))?\\.(${j})|\\b(${j})\\.`,l=`\\b|${d.join("|")}`,m={className:"number",relevance:0,variants:[{begin:`(\\b(${j})|(${k}))[eE][+-]?(${j})[jJ]?(?=${l})`},{begin:`(${k})[jJ]?`},{begin:`\\b([1-9](_?[0-9])*|0+(_?0)*)[lLjJ]?(?=${l})`},{begin:`\\b0[bB](_?[01])+[lL]?(?=${l})`},{begin:`\\b0[oO](_?[0-7])+[lL]?(?=${l})`},{begin:`\\b0[xX](_?[0-9a-fA-F])+[lL]?(?=${l})`},{begin:`\\b(${j})[jJ](?=${l})`}]},n={className:"comment",begin:b.lookahead(/# type:/),end:/$/,keywords:e,contains:[{begin:/# type:/},{begin:/#/,end:/\b\B/,endsWithParent:!0}]},o={className:"params",variants:[{className:"",begin:/\(\s*\)/,skip:!0},{begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:e,contains:["self",f,m,i,a.HASH_COMMENT_MODE]}]};return g.contains=[i,m,f],{name:"Python",aliases:["py","gyp","ipython"],unicodeRegex:!0,keywords:e,illegal:/(<\/|\?)|=>/,contains:[f,m,{scope:"variable.language",match:/\bself\b/},{beginKeywords:"if",relevance:0},{match:/\bor\b/,scope:"keyword"},i,n,a.HASH_COMMENT_MODE,{match:[/\bdef/,/\s+/,c],scope:{1:"keyword",3:"title.function"},contains:[o]},{variants:[{match:[/\bclass/,/\s+/,c,/\s*/,/\(\s*/,c,/\s*\)/]},{match:[/\bclass/,/\s+/,c]}],scope:{1:"keyword",3:"title.class",6:"title.class.inherited"}},{className:"meta",begin:/^[\t ]*@/,end:/(?=#)|$/,contains:[m,o,i]}]}},"python-repl":function(a){return{aliases:["pycon"],contains:[{className:"meta.prompt",starts:{end:/ |$/,starts:{end:"$",subLanguage:"python"}},variants:[{begin:/^>>>(?=[ ]|$)/},{begin:/^\.\.\.(?=[ ]|$)/}]}]}},r:function(a){let b=a.regex,c=/(?:(?:[a-zA-Z]|\.[._a-zA-Z])[._a-zA-Z0-9]*)|\.(?!\d)/,d=b.either(/0[xX][0-9a-fA-F]+\.[0-9a-fA-F]*[pP][+-]?\d+i?/,/0[xX][0-9a-fA-F]+(?:[pP][+-]?\d+)?[Li]?/,/(?:\d+(?:\.\d*)?|\.\d+)(?:[eE][+-]?\d+)?[Li]?/),e=/[=!<>:]=|\|\||&&|:::?|<-|<<-|->>|->|\|>|[-+*\/?!$&|:<=>@^~]|\*\*/,f=b.either(/[()]/,/[{}]/,/\[\[/,/[[\]]/,/\\/,/,/);return{name:"R",keywords:{$pattern:c,keyword:"function if in break next repeat else for while",literal:"NULL NA TRUE FALSE Inf NaN NA_integer_|10 NA_real_|10 NA_character_|10 NA_complex_|10",built_in:"LETTERS letters month.abb month.name pi T F abs acos acosh all any anyNA Arg as.call as.character as.complex as.double as.environment as.integer as.logical as.null.default as.numeric as.raw asin asinh atan atanh attr attributes baseenv browser c call ceiling class Conj cos cosh cospi cummax cummin cumprod cumsum digamma dim dimnames emptyenv exp expression floor forceAndCall gamma gc.time globalenv Im interactive invisible is.array is.atomic is.call is.character is.complex is.double is.environment is.expression is.finite is.function is.infinite is.integer is.language is.list is.logical is.matrix is.na is.name is.nan is.null is.numeric is.object is.pairlist is.raw is.recursive is.single is.symbol lazyLoadDBfetch length lgamma list log max min missing Mod names nargs nzchar oldClass on.exit pos.to.env proc.time prod quote range Re rep retracemem return round seq_along seq_len seq.int sign signif sin sinh sinpi sqrt standardGeneric substitute sum switch tan tanh tanpi tracemem trigamma trunc unclass untracemem UseMethod xtfrm"},contains:[a.COMMENT(/#'/,/$/,{contains:[{scope:"doctag",match:/@examples/,starts:{end:b.lookahead(b.either(/\n^#'\s*(?=@[a-zA-Z]+)/,/\n^(?!#')/)),endsParent:!0}},{scope:"doctag",begin:"@param",end:/$/,contains:[{scope:"variable",variants:[{match:c},{match:/`(?:\\.|[^`\\])+`/}],endsParent:!0}]},{scope:"doctag",match:/@[a-zA-Z]+/},{scope:"keyword",match:/\\[a-zA-Z]+/}]}),a.HASH_COMMENT_MODE,{scope:"string",contains:[a.BACKSLASH_ESCAPE],variants:[a.END_SAME_AS_BEGIN({begin:/[rR]"(-*)\(/,end:/\)(-*)"/}),a.END_SAME_AS_BEGIN({begin:/[rR]"(-*)\{/,end:/\}(-*)"/}),a.END_SAME_AS_BEGIN({begin:/[rR]"(-*)\[/,end:/\](-*)"/}),a.END_SAME_AS_BEGIN({begin:/[rR]'(-*)\(/,end:/\)(-*)'/}),a.END_SAME_AS_BEGIN({begin:/[rR]'(-*)\{/,end:/\}(-*)'/}),a.END_SAME_AS_BEGIN({begin:/[rR]'(-*)\[/,end:/\](-*)'/}),{begin:'"',end:'"',relevance:0},{begin:"'",end:"'",relevance:0}]},{relevance:0,variants:[{scope:{1:"operator",2:"number"},match:[e,d]},{scope:{1:"operator",2:"number"},match:[/%[^%]*%/,d]},{scope:{1:"punctuation",2:"number"},match:[f,d]},{scope:{2:"number"},match:[/[^a-zA-Z0-9._]|^/,d]}]},{scope:{3:"operator"},match:[c,/\s+/,/<-/,/\s+/]},{scope:"operator",relevance:0,variants:[{match:e},{match:/%[^%]*%/}]},{scope:"punctuation",relevance:0,match:f},{begin:"`",end:"`",contains:[{begin:/\\./}]}]}},ruby:function(a){let b=a.regex,c="([a-zA-Z_]\\w*[!?=]?|[-+~]@|<<|>>|=~|===?|<=>|[<>]=?|\\*\\*|[-/+%^&*~`|]|\\[\\]=?)",d=b.either(/\b([A-Z]+[a-z0-9]+)+/,/\b([A-Z]+[a-z0-9]+)+[A-Z]+/),e=b.concat(d,/(::\w+)*/),f={"variable.constant":["__FILE__","__LINE__","__ENCODING__"],"variable.language":["self","super"],keyword:["alias","and","begin","BEGIN","break","case","class","defined","do","else","elsif","end","END","ensure","for","if","in","module","next","not","or","redo","require","rescue","retry","return","then","undef","unless","until","when","while","yield","include","extend","prepend","public","private","protected","raise","throw"],built_in:["proc","lambda","attr_accessor","attr_reader","attr_writer","define_method","private_constant","module_function"],literal:["true","false","nil"]},g={className:"doctag",begin:"@[A-Za-z]+"},h={begin:"#<",end:">"},i=[a.COMMENT("#","$",{contains:[g]}),a.COMMENT("^=begin","^=end",{contains:[g],relevance:10}),a.COMMENT("^__END__",a.MATCH_NOTHING_RE)],j={className:"subst",begin:/#\{/,end:/\}/,keywords:f},k={className:"string",contains:[a.BACKSLASH_ESCAPE,j],variants:[{begin:/'/,end:/'/},{begin:/"/,end:/"/},{begin:/`/,end:/`/},{begin:/%[qQwWx]?\(/,end:/\)/},{begin:/%[qQwWx]?\[/,end:/\]/},{begin:/%[qQwWx]?\{/,end:/\}/},{begin:/%[qQwWx]?</,end:/>/},{begin:/%[qQwWx]?\//,end:/\//},{begin:/%[qQwWx]?%/,end:/%/},{begin:/%[qQwWx]?-/,end:/-/},{begin:/%[qQwWx]?\|/,end:/\|/},{begin:/\B\?(\\\d{1,3})/},{begin:/\B\?(\\x[A-Fa-f0-9]{1,2})/},{begin:/\B\?(\\u\{?[A-Fa-f0-9]{1,6}\}?)/},{begin:/\B\?(\\M-\\C-|\\M-\\c|\\c\\M-|\\M-|\\C-\\M-)[\x20-\x7e]/},{begin:/\B\?\\(c|C-)[\x20-\x7e]/},{begin:/\B\?\\?\S/},{begin:b.concat(/<<[-~]?'?/,b.lookahead(/(\w+)(?=\W)[^\n]*\n(?:[^\n]*\n)*?\s*\1\b/)),contains:[a.END_SAME_AS_BEGIN({begin:/(\w+)/,end:/(\w+)/,contains:[a.BACKSLASH_ESCAPE,j]})]}]},l="[0-9](_?[0-9])*",m={className:"number",relevance:0,variants:[{begin:`\\b([1-9](_?[0-9])*|0)(\\.(${l}))?([eE][+-]?(${l})|r)?i?\\b`},{begin:"\\b0[dD][0-9](_?[0-9])*r?i?\\b"},{begin:"\\b0[bB][0-1](_?[0-1])*r?i?\\b"},{begin:"\\b0[oO][0-7](_?[0-7])*r?i?\\b"},{begin:"\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*r?i?\\b"},{begin:"\\b0(_?[0-7])+r?i?\\b"}]},n={variants:[{match:/\(\)/},{className:"params",begin:/\(/,end:/(?=\))/,excludeBegin:!0,endsParent:!0,keywords:f}]},o=[k,{variants:[{match:[/class\s+/,e,/\s+<\s+/,e]},{match:[/\b(class|module)\s+/,e]}],scope:{2:"title.class",4:"title.class.inherited"},keywords:f},{match:[/(include|extend)\s+/,e],scope:{2:"title.class"},keywords:f},{relevance:0,match:[e,/\.new[. (]/],scope:{1:"title.class"}},{relevance:0,match:/\b[A-Z][A-Z_0-9]+\b/,className:"variable.constant"},{relevance:0,match:d,scope:"title.class"},{match:[/def/,/\s+/,c],scope:{1:"keyword",3:"title.function"},contains:[n]},{begin:a.IDENT_RE+"::"},{className:"symbol",begin:a.UNDERSCORE_IDENT_RE+"(!|\\?)?:",relevance:0},{className:"symbol",begin:":(?!\\s)",contains:[k,{begin:c}],relevance:0},m,{className:"variable",begin:"(\\$\\W)|((\\$|@@?)(\\w+))(?=[^@$?])(?![A-Za-z])(?![@$?'])"},{className:"params",begin:/\|(?!=)/,end:/\|/,excludeBegin:!0,excludeEnd:!0,relevance:0,keywords:f},{begin:"("+a.RE_STARTERS_RE+"|unless)\\s*",keywords:"unless",contains:[{className:"regexp",contains:[a.BACKSLASH_ESCAPE,j],illegal:/\n/,variants:[{begin:"/",end:"/[a-z]*"},{begin:/%r\{/,end:/\}[a-z]*/},{begin:"%r\\(",end:"\\)[a-z]*"},{begin:"%r!",end:"![a-z]*"},{begin:"%r\\[",end:"\\][a-z]*"}]}].concat(h,i),relevance:0}].concat(h,i);j.contains=o,n.contains=o;let p=[{begin:/^\s*=>/,starts:{end:"$",contains:o}},{className:"meta.prompt",begin:"^([>?]>|[\\w#]+\\(\\w+\\):\\d+:\\d+[>*]|(\\w+-)?\\d+\\.\\d+\\.\\d+(p\\d+)?[^\\d][^>]+>)(?=[ ])",starts:{end:"$",keywords:f,contains:o}}];return i.unshift(h),{name:"Ruby",aliases:["rb","gemspec","podspec","thor","irb"],keywords:f,illegal:/\/\*/,contains:[a.SHEBANG({binary:"ruby"})].concat(p).concat(i).concat(o)}},rust:function(a){let b=a.regex,c=/(r#)?/,d=b.concat(c,a.UNDERSCORE_IDENT_RE),e=b.concat(c,a.IDENT_RE),f={className:"title.function.invoke",relevance:0,begin:b.concat(/\b/,/(?!let|for|while|if|else|match\b)/,e,b.lookahead(/\s*\(/))},g="([ui](8|16|32|64|128|size)|f(32|64))?",h=["drop ","Copy","Send","Sized","Sync","Drop","Fn","FnMut","FnOnce","ToOwned","Clone","Debug","PartialEq","PartialOrd","Eq","Ord","AsRef","AsMut","Into","From","Default","Iterator","Extend","IntoIterator","DoubleEndedIterator","ExactSizeIterator","SliceConcatExt","ToString","assert!","assert_eq!","bitflags!","bytes!","cfg!","col!","concat!","concat_idents!","debug_assert!","debug_assert_eq!","env!","eprintln!","panic!","file!","format!","format_args!","include_bytes!","include_str!","line!","local_data_key!","module_path!","option_env!","print!","println!","select!","stringify!","try!","unimplemented!","unreachable!","vec!","write!","writeln!","macro_rules!","assert_ne!","debug_assert_ne!"],i=["i8","i16","i32","i64","i128","isize","u8","u16","u32","u64","u128","usize","f32","f64","str","char","bool","Box","Option","Result","String","Vec"];return{name:"Rust",aliases:["rs"],keywords:{$pattern:a.IDENT_RE+"!?",type:i,keyword:["abstract","as","async","await","become","box","break","const","continue","crate","do","dyn","else","enum","extern","false","final","fn","for","if","impl","in","let","loop","macro","match","mod","move","mut","override","priv","pub","ref","return","self","Self","static","struct","super","trait","true","try","type","typeof","union","unsafe","unsized","use","virtual","where","while","yield"],literal:["true","false","Some","None","Ok","Err"],built_in:h},illegal:"</",contains:[a.C_LINE_COMMENT_MODE,a.COMMENT("/\\*","\\*/",{contains:["self"]}),a.inherit(a.QUOTE_STRING_MODE,{begin:/b?"/,illegal:null}),{className:"symbol",begin:/'[a-zA-Z_][a-zA-Z0-9_]*(?!')/},{scope:"string",variants:[{begin:/b?r(#*)"(.|\n)*?"\1(?!#)/},{begin:/b?'/,end:/'/,contains:[{scope:"char.escape",match:/\\('|\w|x\w{2}|u\w{4}|U\w{8})/}]}]},{className:"number",variants:[{begin:"\\b0b([01_]+)"+g},{begin:"\\b0o([0-7_]+)"+g},{begin:"\\b0x([A-Fa-f0-9_]+)"+g},{begin:"\\b(\\d[\\d_]*(\\.[0-9_]+)?([eE][+-]?[0-9_]+)?)"+g}],relevance:0},{begin:[/fn/,/\s+/,d],className:{1:"keyword",3:"title.function"}},{className:"meta",begin:"#!?\\[",end:"\\]",contains:[{className:"string",begin:/"/,end:/"/,contains:[a.BACKSLASH_ESCAPE]}]},{begin:[/let/,/\s+/,/(?:mut\s+)?/,d],className:{1:"keyword",3:"keyword",4:"variable"}},{begin:[/for/,/\s+/,d,/\s+/,/in/],className:{1:"keyword",3:"variable",5:"keyword"}},{begin:[/type/,/\s+/,d],className:{1:"keyword",3:"title.class"}},{begin:[/(?:trait|enum|struct|union|impl|for)/,/\s+/,d],className:{1:"keyword",3:"title.class"}},{begin:a.IDENT_RE+"::",keywords:{keyword:"Self",built_in:h,type:i}},{className:"punctuation",begin:"->"},f]}},scss:function(a){let b={IMPORTANT:{scope:"meta",begin:"!important"},BLOCK_COMMENT:a.C_BLOCK_COMMENT_MODE,HEXCOLOR:{scope:"number",begin:/#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\b/},FUNCTION_DISPATCH:{className:"built_in",begin:/[\w-]+(?=\()/},ATTRIBUTE_SELECTOR_MODE:{scope:"selector-attr",begin:/\[/,end:/\]/,illegal:"$",contains:[a.APOS_STRING_MODE,a.QUOTE_STRING_MODE]},CSS_NUMBER_MODE:{scope:"number",begin:a.NUMBER_RE+"(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?",relevance:0},CSS_VARIABLE:{className:"attr",begin:/--[A-Za-z_][A-Za-z0-9_-]*/}},c="@[a-z-]+",d={className:"variable",begin:"(\\$[a-zA-Z-][a-zA-Z0-9_-]*)\\b",relevance:0};return{name:"SCSS",case_insensitive:!0,illegal:"[=/|']",contains:[a.C_LINE_COMMENT_MODE,a.C_BLOCK_COMMENT_MODE,b.CSS_NUMBER_MODE,{className:"selector-id",begin:"#[A-Za-z0-9_-]+",relevance:0},{className:"selector-class",begin:"\\.[A-Za-z0-9_-]+",relevance:0},b.ATTRIBUTE_SELECTOR_MODE,{className:"selector-tag",begin:"\\b("+dQ.join("|")+")\\b",relevance:0},{className:"selector-pseudo",begin:":("+dS.join("|")+")"},{className:"selector-pseudo",begin:":(:)?("+dT.join("|")+")"},d,{begin:/\(/,end:/\)/,contains:[b.CSS_NUMBER_MODE]},b.CSS_VARIABLE,{className:"attribute",begin:"\\b("+dU.join("|")+")\\b"},{begin:"\\b(whitespace|wait|w-resize|visible|vertical-text|vertical-ideographic|uppercase|upper-roman|upper-alpha|underline|transparent|top|thin|thick|text|text-top|text-bottom|tb-rl|table-header-group|table-footer-group|sw-resize|super|strict|static|square|solid|small-caps|separate|se-resize|scroll|s-resize|rtl|row-resize|ridge|right|repeat|repeat-y|repeat-x|relative|progress|pointer|overline|outside|outset|oblique|nowrap|not-allowed|normal|none|nw-resize|no-repeat|no-drop|newspaper|ne-resize|n-resize|move|middle|medium|ltr|lr-tb|lowercase|lower-roman|lower-alpha|loose|list-item|line|line-through|line-edge|lighter|left|keep-all|justify|italic|inter-word|inter-ideograph|inside|inset|inline|inline-block|inherit|inactive|ideograph-space|ideograph-parenthesis|ideograph-numeric|ideograph-alpha|horizontal|hidden|help|hand|groove|fixed|ellipsis|e-resize|double|dotted|distribute|distribute-space|distribute-letter|distribute-all-lines|disc|disabled|default|decimal|dashed|crosshair|collapse|col-resize|circle|char|center|capitalize|break-word|break-all|bottom|both|bolder|bold|block|bidi-override|below|baseline|auto|always|all-scroll|absolute|table|table-cell)\\b"},{begin:/:/,end:/[;}{]/,relevance:0,contains:[b.BLOCK_COMMENT,d,b.HEXCOLOR,b.CSS_NUMBER_MODE,a.QUOTE_STRING_MODE,a.APOS_STRING_MODE,b.IMPORTANT,b.FUNCTION_DISPATCH]},{begin:"@(page|font-face)",keywords:{$pattern:c,keyword:"@page @font-face"}},{begin:"@",end:"[{;]",returnBegin:!0,keywords:{$pattern:/[a-z-]+/,keyword:"and or not only",attribute:dR.join(" ")},contains:[{begin:c,className:"keyword"},{begin:/[a-z-]+(?=:)/,className:"attribute"},d,a.QUOTE_STRING_MODE,a.APOS_STRING_MODE,b.HEXCOLOR,b.CSS_NUMBER_MODE]},b.FUNCTION_DISPATCH]}},shell:function(a){return{name:"Shell Session",aliases:["console","shellsession"],contains:[{className:"meta.prompt",begin:/^\s{0,3}[/~\w\d[\]()@-]*[>%$#][ ]?/,starts:{end:/[^\\](?=\s*$)/,subLanguage:"bash"}}]}},sql:function(a){let b=a.regex,c=a.COMMENT("--","$"),d=["abs","acos","array_agg","asin","atan","avg","cast","ceil","ceiling","coalesce","corr","cos","cosh","count","covar_pop","covar_samp","cume_dist","dense_rank","deref","element","exp","extract","first_value","floor","json_array","json_arrayagg","json_exists","json_object","json_objectagg","json_query","json_table","json_table_primitive","json_value","lag","last_value","lead","listagg","ln","log","log10","lower","max","min","mod","nth_value","ntile","nullif","percent_rank","percentile_cont","percentile_disc","position","position_regex","power","rank","regr_avgx","regr_avgy","regr_count","regr_intercept","regr_r2","regr_slope","regr_sxx","regr_sxy","regr_syy","row_number","sin","sinh","sqrt","stddev_pop","stddev_samp","substring","substring_regex","sum","tan","tanh","translate","translate_regex","treat","trim","trim_array","unnest","upper","value_of","var_pop","var_samp","width_bucket"],e=["abs","acos","all","allocate","alter","and","any","are","array","array_agg","array_max_cardinality","as","asensitive","asin","asymmetric","at","atan","atomic","authorization","avg","begin","begin_frame","begin_partition","between","bigint","binary","blob","boolean","both","by","call","called","cardinality","cascaded","case","cast","ceil","ceiling","char","char_length","character","character_length","check","classifier","clob","close","coalesce","collate","collect","column","commit","condition","connect","constraint","contains","convert","copy","corr","corresponding","cos","cosh","count","covar_pop","covar_samp","create","cross","cube","cume_dist","current","current_catalog","current_date","current_default_transform_group","current_path","current_role","current_row","current_schema","current_time","current_timestamp","current_path","current_role","current_transform_group_for_type","current_user","cursor","cycle","date","day","deallocate","dec","decimal","decfloat","declare","default","define","delete","dense_rank","deref","describe","deterministic","disconnect","distinct","double","drop","dynamic","each","element","else","empty","end","end_frame","end_partition","end-exec","equals","escape","every","except","exec","execute","exists","exp","external","extract","false","fetch","filter","first_value","float","floor","for","foreign","frame_row","free","from","full","function","fusion","get","global","grant","group","grouping","groups","having","hold","hour","identity","in","indicator","initial","inner","inout","insensitive","insert","int","integer","intersect","intersection","interval","into","is","join","json_array","json_arrayagg","json_exists","json_object","json_objectagg","json_query","json_table","json_table_primitive","json_value","lag","language","large","last_value","lateral","lead","leading","left","like","like_regex","listagg","ln","local","localtime","localtimestamp","log","log10","lower","match","match_number","match_recognize","matches","max","member","merge","method","min","minute","mod","modifies","module","month","multiset","national","natural","nchar","nclob","new","no","none","normalize","not","nth_value","ntile","null","nullif","numeric","octet_length","occurrences_regex","of","offset","old","omit","on","one","only","open","or","order","out","outer","over","overlaps","overlay","parameter","partition","pattern","per","percent","percent_rank","percentile_cont","percentile_disc","period","portion","position","position_regex","power","precedes","precision","prepare","primary","procedure","ptf","range","rank","reads","real","recursive","ref","references","referencing","regr_avgx","regr_avgy","regr_count","regr_intercept","regr_r2","regr_slope","regr_sxx","regr_sxy","regr_syy","release","result","return","returns","revoke","right","rollback","rollup","row","row_number","rows","running","savepoint","scope","scroll","search","second","seek","select","sensitive","session_user","set","show","similar","sin","sinh","skip","smallint","some","specific","specifictype","sql","sqlexception","sqlstate","sqlwarning","sqrt","start","static","stddev_pop","stddev_samp","submultiset","subset","substring","substring_regex","succeeds","sum","symmetric","system","system_time","system_user","table","tablesample","tan","tanh","then","time","timestamp","timezone_hour","timezone_minute","to","trailing","translate","translate_regex","translation","treat","trigger","trim","trim_array","true","truncate","uescape","union","unique","unknown","unnest","update","upper","user","using","value","values","value_of","var_pop","var_samp","varbinary","varchar","varying","versioning","when","whenever","where","width_bucket","window","with","within","without","year","add","asc","collation","desc","final","first","last","view"].filter(a=>!d.includes(a)),f={match:b.concat(/\b/,b.either(...d),/\s*\(/),relevance:0,keywords:{built_in:d}};function g(a){return b.concat(/\b/,b.either(...a.map(a=>a.replace(/\s+/,"\\s+"))),/\b/)}let h={scope:"keyword",match:g(["create table","insert into","primary key","foreign key","not null","alter table","add constraint","grouping sets","on overflow","character set","respect nulls","ignore nulls","nulls first","nulls last","depth first","breadth first"]),relevance:0};return{name:"SQL",case_insensitive:!0,illegal:/[{}]|<\//,keywords:{$pattern:/\b[\w\.]+/,keyword:function(a,{exceptions:b,when:c}={}){return b=b||[],a.map(a=>a.match(/\|\d+$/)||b.includes(a)?a:c(a)?`${a}|0`:a)}(e,{when:a=>a.length<3}),literal:["true","false","unknown"],type:["bigint","binary","blob","boolean","char","character","clob","date","dec","decfloat","decimal","float","int","integer","interval","nchar","nclob","national","numeric","real","row","smallint","time","timestamp","varchar","varying","varbinary"],built_in:["current_catalog","current_date","current_default_transform_group","current_path","current_role","current_schema","current_transform_group_for_type","current_user","session_user","system_time","system_user","current_time","localtime","current_timestamp","localtimestamp"]},contains:[{scope:"type",match:g(["double precision","large object","with timezone","without timezone"])},h,f,{scope:"variable",match:/@[a-z0-9][a-z0-9_]*/},{scope:"string",variants:[{begin:/'/,end:/'/,contains:[{match:/''/}]}]},{begin:/"/,end:/"/,contains:[{match:/""/}]},a.C_NUMBER_MODE,a.C_BLOCK_COMMENT_MODE,c,{scope:"operator",match:/[-+*/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?/,relevance:0}]}},swift:function(a){let b={match:/\s+/,relevance:0},c=a.COMMENT("/\\*","\\*/",{contains:["self"]}),d=[a.C_LINE_COMMENT_MODE,c],e={match:[/\./,dY(...d$,...d_)],className:{2:"keyword"}},f={match:dX(/\./,dY(...d1)),relevance:0},g=d1.filter(a=>"string"==typeof a).concat(["_|0"]),h={variants:[{className:"keyword",match:dY(...d1.filter(a=>"string"!=typeof a).concat(d0).map(dZ),...d_)}]},i={$pattern:dY(/\b\w+/,/#\w+/),keyword:g.concat(d4),literal:d2},j=[e,f,h],k=[{match:dX(/\./,dY(...d5)),relevance:0},{className:"built_in",match:dX(/\b/,dY(...d5),/(?=\()/)}],l={match:/->/,relevance:0},m=[l,{className:"operator",relevance:0,variants:[{match:d8},{match:`\\.(\\.|${d7})+`}]}],n="([0-9]_*)+",o="([0-9a-fA-F]_*)+",p={className:"number",relevance:0,variants:[{match:`\\b(${n})(\\.(${n}))?([eE][+-]?(${n}))?\\b`},{match:`\\b0x(${o})(\\.(${o}))?([pP][+-]?(${n}))?\\b`},{match:/\b0o([0-7]_*)+\b/},{match:/\b0b([01]_*)+\b/}]},q=(a="")=>({className:"subst",variants:[{match:dX(/\\/,a,/[0\\tnr"']/)},{match:dX(/\\/,a,/u\{[0-9a-fA-F]{1,8}\}/)}]}),r=(a="")=>({className:"subst",label:"interpol",begin:dX(/\\/,a,/\(/),end:/\)/}),s=(a="")=>({begin:dX(a,/"""/),end:dX(/"""/,a),contains:[q(a),((a="")=>({className:"subst",match:dX(/\\/,a,/[\t ]*(?:[\r\n]|\r\n)/)}))(a),r(a)]}),t=(a="")=>({begin:dX(a,/"/),end:dX(/"/,a),contains:[q(a),r(a)]}),u={className:"string",variants:[s(),s("#"),s("##"),s("###"),t(),t("#"),t("##"),t("###")]},v=[a.BACKSLASH_ESCAPE,{begin:/\[/,end:/\]/,relevance:0,contains:[a.BACKSLASH_ESCAPE]}],w=a=>{let b=dX(a,/\//),c=dX(/\//,a);return{begin:b,end:c,contains:[...v,{scope:"comment",begin:`#(?!.*${c})`,end:/$/}]}},x={scope:"regexp",variants:[w("###"),w("##"),w("#"),{begin:/\/[^\s](?=[^/\n]*\/)/,end:/\//,contains:v}]},y={match:dX(/`/,eb,/`/)},z=[y,{className:"variable",match:/\$\d+/},{className:"variable",match:`\\$${ea}+`}],A=[{match:/(@|#(un)?)available/,scope:"keyword",starts:{contains:[{begin:/\(/,end:/\)/,keywords:ee,contains:[...m,p,u]}]}},{scope:"keyword",match:dX(/@/,dY(...ed),dW(dY(/\(/,/\s+/)))},{scope:"meta",match:dX(/@/,eb)}],B={match:dW(/\b[A-Z]/),relevance:0,contains:[{className:"type",match:dX(/(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)/,ea,"+")},{className:"type",match:ec,relevance:0},{match:/[?!]+/,relevance:0},{match:/\.\.\./,relevance:0},{match:dX(/\s+&\s+/,dW(ec)),relevance:0}]},C={begin:/</,end:/>/,keywords:i,contains:[...d,...j,...A,l,B]};B.contains.push(C);let D={begin:/\(/,end:/\)/,relevance:0,keywords:i,contains:["self",{match:dX(eb,/\s*:/),keywords:"_|0",relevance:0},...d,x,...j,...k,...m,p,u,...z,...A,B]},E={begin:/</,end:/>/,keywords:"repeat each",contains:[...d,B]},F={begin:/\(/,end:/\)/,keywords:i,contains:[{begin:dY(dW(dX(eb,/\s*:/)),dW(dX(eb,/\s+/,eb,/\s*:/))),end:/:/,relevance:0,contains:[{className:"keyword",match:/\b_\b/},{className:"params",match:eb}]},...d,...j,...m,p,u,...A,B,D],endsParent:!0,illegal:/["']/},G={match:[/(func|macro)/,/\s+/,dY(y.match,eb,d8)],className:{1:"keyword",3:"title.function"},contains:[E,F,b],illegal:[/\[/,/%/]},H={begin:[/precedencegroup/,/\s+/,ec],className:{1:"keyword",3:"title"},contains:[B],keywords:[...d3,...d2],end:/}/},I={begin:[/(struct|protocol|class|extension|enum|actor)/,/\s+/,eb,/\s*/],beginScope:{1:"keyword",3:"title.class"},keywords:i,contains:[E,...j,{begin:/:/,end:/\{/,keywords:i,contains:[{scope:"title.class.inherited",match:ec},...j],relevance:0}]};for(let a of u.variants){let b=a.contains.find(a=>"interpol"===a.label);b.keywords=i;let c=[...j,...k,...m,p,u,...z];b.contains=[...c,{begin:/\(/,end:/\)/,contains:["self",...c]}]}return{name:"Swift",keywords:i,contains:[...d,G,{match:[/\b(?:subscript|init[?!]?)/,/\s*(?=[<(])/],className:{1:"keyword"},contains:[E,F,b],illegal:/\[|%/},{match:[/class\b/,/\s+/,/func\b/,/\s+/,/\b[A-Za-z_][A-Za-z0-9_]*\b/],scope:{1:"keyword",3:"keyword",5:"title.function"}},{match:[/class\b/,/\s+/,/var\b/],scope:{1:"keyword",3:"keyword"}},I,{match:[/operator/,/\s+/,d8],className:{1:"keyword",3:"title"}},H,{beginKeywords:"import",end:/$/,contains:[...d],relevance:0},x,...j,...k,...m,p,u,...z,...A,B,D]}},typescript:function(a){let b=a.regex,c=function(a){var b;let c=a.regex,d=/<[A-Za-z0-9\\._:-]+/,e=/\/[A-Za-z0-9\\._:-]+>|\/>/,f={$pattern:ef,keyword:eg,literal:eh,built_in:em,"variable.language":el},g="[0-9](_?[0-9])*",h=`\\.(${g})`,i="0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*",j={className:"number",variants:[{begin:`(\\b(${i})((${h})|\\.)?|(${h}))[eE][+-]?(${g})\\b`},{begin:`\\b(${i})\\b((${h})\\b|\\.)?|(${h})\\b`},{begin:"\\b(0|[1-9](_?[0-9])*)n\\b"},{begin:"\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\b"},{begin:"\\b0[bB][0-1](_?[0-1])*n?\\b"},{begin:"\\b0[oO][0-7](_?[0-7])*n?\\b"},{begin:"\\b0[0-7]+n?\\b"}],relevance:0},k={className:"subst",begin:"\\$\\{",end:"\\}",keywords:f,contains:[]},l={begin:".?html`",end:"",starts:{end:"`",returnEnd:!1,contains:[a.BACKSLASH_ESCAPE,k],subLanguage:"xml"}},m={begin:".?css`",end:"",starts:{end:"`",returnEnd:!1,contains:[a.BACKSLASH_ESCAPE,k],subLanguage:"css"}},n={begin:".?gql`",end:"",starts:{end:"`",returnEnd:!1,contains:[a.BACKSLASH_ESCAPE,k],subLanguage:"graphql"}},o={className:"string",begin:"`",end:"`",contains:[a.BACKSLASH_ESCAPE,k]},p={className:"comment",variants:[a.COMMENT(/\/\*\*(?!\/)/,"\\*/",{relevance:0,contains:[{begin:"(?=@[A-Za-z]+)",relevance:0,contains:[{className:"doctag",begin:"@[A-Za-z]+"},{className:"type",begin:"\\{",end:"\\}",excludeEnd:!0,excludeBegin:!0,relevance:0},{className:"variable",begin:ef+"(?=\\s*(-)|$)",endsParent:!0,relevance:0},{begin:/(?=[^\n])\s/,relevance:0}]}]}),a.C_BLOCK_COMMENT_MODE,a.C_LINE_COMMENT_MODE]},q=[a.APOS_STRING_MODE,a.QUOTE_STRING_MODE,l,m,n,o,{match:/\$\d+/},j];k.contains=q.concat({begin:/\{/,end:/\}/,keywords:f,contains:["self"].concat(q)});let r=[].concat(p,k.contains),s=r.concat([{begin:/(\s*)\(/,end:/\)/,keywords:f,contains:["self"].concat(r)}]),t={className:"params",begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:f,contains:s},u={variants:[{match:[/class/,/\s+/,ef,/\s+/,/extends/,/\s+/,c.concat(ef,"(",c.concat(/\./,ef),")*")],scope:{1:"keyword",3:"title.class",5:"keyword",7:"title.class.inherited"}},{match:[/class/,/\s+/,ef],scope:{1:"keyword",3:"title.class"}}]},v={relevance:0,match:c.either(/\bJSON/,/\b[A-Z][a-z]+([A-Z][a-z]*|\d)*/,/\b[A-Z]{2,}([A-Z][a-z]+|\d)+([A-Z][a-z]*)*/,/\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\d)*([A-Z][a-z]*)*/),className:"title.class",keywords:{_:[...ei,...ej]}},w={match:c.concat(/\b/,(b=[...ek,"super","import"].map(a=>`${a}\\s*\\(`),c.concat("(?!",b.join("|"),")")),ef,c.lookahead(/\s*\(/)),className:"title.function",relevance:0},x={begin:c.concat(/\./,c.lookahead(c.concat(ef,/(?![0-9A-Za-z$_(])/))),end:ef,excludeBegin:!0,keywords:"prototype",className:"property",relevance:0},y="(\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)|"+a.UNDERSCORE_IDENT_RE+")\\s*=>",z={match:[/const|var|let/,/\s+/,ef,/\s*/,/=\s*/,/(async\s*)?/,c.lookahead(y)],keywords:"async",className:{1:"keyword",3:"title.function"},contains:[t]};return{name:"JavaScript",aliases:["js","jsx","mjs","cjs"],keywords:f,exports:{PARAMS_CONTAINS:s,CLASS_REFERENCE:v},illegal:/#(?![$_A-z])/,contains:[a.SHEBANG({label:"shebang",binary:"node",relevance:5}),{label:"use_strict",className:"meta",relevance:10,begin:/^\s*['"]use (strict|asm)['"]/},a.APOS_STRING_MODE,a.QUOTE_STRING_MODE,l,m,n,o,p,{match:/\$\d+/},j,v,{scope:"attr",match:ef+c.lookahead(":"),relevance:0},z,{begin:"("+a.RE_STARTERS_RE+"|\\b(case|return|throw)\\b)\\s*",keywords:"return throw case",relevance:0,contains:[p,a.REGEXP_MODE,{className:"function",begin:y,returnBegin:!0,end:"\\s*=>",contains:[{className:"params",variants:[{begin:a.UNDERSCORE_IDENT_RE,relevance:0},{className:null,begin:/\(\s*\)/,skip:!0},{begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:f,contains:s}]}]},{begin:/,/,relevance:0},{match:/\s+/,relevance:0},{variants:[{begin:"<>",end:"</>"},{match:/<[A-Za-z0-9\\._:-]+\s*\/>/},{begin:d,"on:begin":(a,b)=>{let c,d=a[0].length+a.index,e=a.input[d];if("<"===e||","===e)return void b.ignoreMatch();">"!==e||((a,{after:b})=>{let c="</"+a[0].slice(1);return -1!==a.input.indexOf(c,b)})(a,{after:d})||b.ignoreMatch();let f=a.input.substring(d);if((c=f.match(/^\s*=/))||(c=f.match(/^\s+extends\s+/))&&0===c.index)return void b.ignoreMatch()},end:e}],subLanguage:"xml",contains:[{begin:d,end:e,skip:!0,contains:["self"]}]}]},{variants:[{match:[/function/,/\s+/,ef,/(?=\s*\()/]},{match:[/function/,/\s*(?=\()/]}],className:{1:"keyword",3:"title.function"},label:"func.def",contains:[t],illegal:/%/},{beginKeywords:"while if switch catch for"},{begin:"\\b(?!function)"+a.UNDERSCORE_IDENT_RE+"\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)\\s*\\{",returnBegin:!0,label:"func.def",contains:[t,a.inherit(a.TITLE_MODE,{begin:ef,className:"title.function"})]},{match:/\.\.\./,relevance:0},x,{match:"\\$"+ef,relevance:0},{match:[/\bconstructor(?=\s*\()/],className:{1:"title.function"},contains:[t]},w,{relevance:0,match:/\b[A-Z][A-Z_0-9]+\b/,className:"variable.constant"},u,{match:[/get|set/,/\s+/,ef,/(?=\()/],className:{1:"keyword",3:"title.function"},contains:[{begin:/\(\)/},t]},{match:/\$[(.]/}]}}(a),d=["any","void","number","boolean","string","object","never","symbol","bigint","unknown"],e={begin:[/namespace/,/\s+/,a.IDENT_RE],beginScope:{1:"keyword",3:"title.class"}},f={beginKeywords:"interface",end:/\{/,excludeEnd:!0,keywords:{keyword:"interface extends",built_in:d},contains:[c.exports.CLASS_REFERENCE]},g={$pattern:ef,keyword:eg.concat(["type","interface","public","private","protected","implements","declare","abstract","readonly","enum","override","satisfies"]),literal:eh,built_in:em.concat(d),"variable.language":el},h={className:"meta",begin:"@"+ef},i=(a,b,c)=>{let d=a.contains.findIndex(a=>a.label===b);if(-1===d)throw Error("can not find mode to replace");a.contains.splice(d,1,c)};Object.assign(c.keywords,g),c.exports.PARAMS_CONTAINS.push(h);let j=c.contains.find(a=>"attr"===a.scope),k=Object.assign({},j,{match:b.concat(ef,b.lookahead(/\s*\?:/))});return c.exports.PARAMS_CONTAINS.push([c.exports.CLASS_REFERENCE,j,k]),c.contains=c.contains.concat([h,e,f,k]),i(c,"shebang",a.SHEBANG()),i(c,"use_strict",{className:"meta",relevance:10,begin:/^\s*['"]use strict['"]/}),c.contains.find(a=>"func.def"===a.label).relevance=0,Object.assign(c,{name:"TypeScript",aliases:["ts","tsx","mts","cts"]}),c},vbnet:function(a){let b=a.regex,c=/\d{1,2}\/\d{1,2}\/\d{4}/,d=/\d{4}-\d{1,2}-\d{1,2}/,e=/(\d|1[012])(:\d+){0,2} *(AM|PM)/,f=/\d{1,2}(:\d{1,2}){1,2}/,g={className:"literal",variants:[{begin:b.concat(/# */,b.either(d,c),/ *#/)},{begin:b.concat(/# */,f,/ *#/)},{begin:b.concat(/# */,e,/ *#/)},{begin:b.concat(/# */,b.either(d,c),/ +/,b.either(e,f),/ *#/)}]},h=a.COMMENT(/'''/,/$/,{contains:[{className:"doctag",begin:/<\/?/,end:/>/}]}),i=a.COMMENT(null,/$/,{variants:[{begin:/'/},{begin:/([\t ]|^)REM(?=\s)/}]});return{name:"Visual Basic .NET",aliases:["vb"],case_insensitive:!0,classNameAliases:{label:"symbol"},keywords:{keyword:"addhandler alias aggregate ansi as async assembly auto binary by byref byval call case catch class compare const continue custom declare default delegate dim distinct do each equals else elseif end enum erase error event exit explicit finally for friend from function get global goto group handles if implements imports in inherits interface into iterator join key let lib loop me mid module mustinherit mustoverride mybase myclass namespace narrowing new next notinheritable notoverridable of off on operator option optional order overloads overridable overrides paramarray partial preserve private property protected public raiseevent readonly redim removehandler resume return select set shadows shared skip static step stop structure strict sub synclock take text then throw to try unicode until using when where while widening with withevents writeonly yield",built_in:"addressof and andalso await directcast gettype getxmlnamespace is isfalse isnot istrue like mod nameof new not or orelse trycast typeof xor cbool cbyte cchar cdate cdbl cdec cint clng cobj csbyte cshort csng cstr cuint culng cushort",type:"boolean byte char date decimal double integer long object sbyte short single string uinteger ulong ushort",literal:"true false nothing"},illegal:"//|\\{|\\}|endif|gosub|variant|wend|^\\$ ",contains:[{className:"string",begin:/"(""|[^/n])"C\b/},{className:"string",begin:/"/,end:/"/,illegal:/\n/,contains:[{begin:/""/}]},g,{className:"number",relevance:0,variants:[{begin:/\b\d[\d_]*((\.[\d_]+(E[+-]?[\d_]+)?)|(E[+-]?[\d_]+))[RFD@!#]?/},{begin:/\b\d[\d_]*((U?[SIL])|[%&])?/},{begin:/&H[\dA-F_]+((U?[SIL])|[%&])?/},{begin:/&O[0-7_]+((U?[SIL])|[%&])?/},{begin:/&B[01_]+((U?[SIL])|[%&])?/}]},{className:"label",begin:/^\w+:/},h,i,{className:"meta",begin:/[\t ]*#(const|disable|else|elseif|enable|end|externalsource|if|region)\b/,end:/$/,keywords:{keyword:"const disable else elseif enable end externalsource if region then"},contains:[i]}]}},wasm:function(a){a.regex;let b=a.COMMENT(/\(;/,/;\)/);return b.contains.push("self"),{name:"WebAssembly",keywords:{$pattern:/[\w.]+/,keyword:["anyfunc","block","br","br_if","br_table","call","call_indirect","data","drop","elem","else","end","export","func","global.get","global.set","local.get","local.set","local.tee","get_global","get_local","global","if","import","local","loop","memory","memory.grow","memory.size","module","mut","nop","offset","param","result","return","select","set_global","set_local","start","table","tee_local","then","type","unreachable"]},contains:[a.COMMENT(/;;/,/$/),b,{match:[/(?:offset|align)/,/\s*/,/=/],className:{1:"keyword",3:"operator"}},{className:"variable",begin:/\$[\w_]+/},{match:/(\((?!;)|\))+/,className:"punctuation",relevance:0},{begin:[/(?:func|call|call_indirect)/,/\s+/,/\$[^\s)]+/],className:{1:"keyword",3:"title.function"}},a.QUOTE_STRING_MODE,{match:/(i32|i64|f32|f64)(?!\.)/,className:"type"},{className:"keyword",match:/\b(f32|f64|i32|i64)(?:\.(?:abs|add|and|ceil|clz|const|convert_[su]\/i(?:32|64)|copysign|ctz|demote\/f64|div(?:_[su])?|eqz?|extend_[su]\/i32|floor|ge(?:_[su])?|gt(?:_[su])?|le(?:_[su])?|load(?:(?:8|16|32)_[su])?|lt(?:_[su])?|max|min|mul|nearest|neg?|or|popcnt|promote\/f32|reinterpret\/[fi](?:32|64)|rem_[su]|rot[lr]|shl|shr_[su]|store(?:8|16|32)?|sqrt|sub|trunc(?:_[su]\/f(?:32|64))?|wrap\/i64|xor))\b/},{className:"number",relevance:0,match:/[+-]?\b(?:\d(?:_?\d)*(?:\.\d(?:_?\d)*)?(?:[eE][+-]?\d(?:_?\d)*)?|0x[\da-fA-F](?:_?[\da-fA-F])*(?:\.[\da-fA-F](?:_?[\da-fA-D])*)?(?:[pP][+-]?\d(?:_?\d)*)?)\b|\binf\b|\bnan(?::0x[\da-fA-F](?:_?[\da-fA-D])*)?\b/}]}},xml:function(a){let b=a.regex,c=b.concat(/[\p{L}_]/u,b.optional(/[\p{L}0-9_.-]*:/u),/[\p{L}0-9_.-]*/u),d={className:"symbol",begin:/&[a-z]+;|&#[0-9]+;|&#x[a-f0-9]+;/},e={begin:/\s/,contains:[{className:"keyword",begin:/#?[a-z_][a-z1-9_-]+/,illegal:/\n/}]},f=a.inherit(e,{begin:/\(/,end:/\)/}),g=a.inherit(a.APOS_STRING_MODE,{className:"string"}),h=a.inherit(a.QUOTE_STRING_MODE,{className:"string"}),i={endsWithParent:!0,illegal:/</,relevance:0,contains:[{className:"attr",begin:/[\p{L}0-9._:-]+/u,relevance:0},{begin:/=\s*/,relevance:0,contains:[{className:"string",endsParent:!0,variants:[{begin:/"/,end:/"/,contains:[d]},{begin:/'/,end:/'/,contains:[d]},{begin:/[^\s"'=<>`]+/}]}]}]};return{name:"HTML, XML",aliases:["html","xhtml","rss","atom","xjb","xsd","xsl","plist","wsf","svg"],case_insensitive:!0,unicodeRegex:!0,contains:[{className:"meta",begin:/<![a-z]/,end:/>/,relevance:10,contains:[e,h,g,f,{begin:/\[/,end:/\]/,contains:[{className:"meta",begin:/<![a-z]/,end:/>/,contains:[e,f,h,g]}]}]},a.COMMENT(/<!--/,/-->/,{relevance:10}),{begin:/<!\[CDATA\[/,end:/\]\]>/,relevance:10},d,{className:"meta",end:/\?>/,variants:[{begin:/<\?xml/,relevance:10,contains:[h]},{begin:/<\?[a-z][a-z0-9]+/}]},{className:"tag",begin:/<style(?=\s|>)/,end:/>/,keywords:{name:"style"},contains:[i],starts:{end:/<\/style>/,returnEnd:!0,subLanguage:["css","xml"]}},{className:"tag",begin:/<script(?=\s|>)/,end:/>/,keywords:{name:"script"},contains:[i],starts:{end:/<\/script>/,returnEnd:!0,subLanguage:["javascript","handlebars","xml"]}},{className:"tag",begin:/<>|<\/>/},{className:"tag",begin:b.concat(/</,b.lookahead(b.concat(c,b.either(/\/>/,/>/,/\s/)))),end:/\/?>/,contains:[{className:"name",begin:c,relevance:0,starts:i}]},{className:"tag",begin:b.concat(/<\//,b.lookahead(b.concat(c,/>/))),contains:[{className:"name",begin:c,relevance:0},{begin:/>/,relevance:0,endsParent:!0}]}]}},yaml:function(a){let b="true false yes no null",c="[\\w#;/?:@&=+$,.~*'()[\\]]+",d={className:"string",relevance:0,variants:[{begin:/"/,end:/"/},{begin:/\S+/}],contains:[a.BACKSLASH_ESCAPE,{className:"template-variable",variants:[{begin:/\{\{/,end:/\}\}/},{begin:/%\{/,end:/\}/}]}]},e=a.inherit(d,{variants:[{begin:/'/,end:/'/,contains:[{begin:/''/,relevance:0}]},{begin:/"/,end:/"/},{begin:/[^\s,{}[\]]+/}]}),f={end:",",endsWithParent:!0,excludeEnd:!0,keywords:b,relevance:0},g=[{className:"attr",variants:[{begin:/[\w*@][\w*@ :()\./-]*:(?=[ \t]|$)/},{begin:/"[\w*@][\w*@ :()\./-]*":(?=[ \t]|$)/},{begin:/'[\w*@][\w*@ :()\./-]*':(?=[ \t]|$)/}]},{className:"meta",begin:"^---\\s*$",relevance:10},{className:"string",begin:"[\\|>]([1-9]?[+-])?[ ]*\\n( +)[^ ][^\\n]*\\n(\\2[^\\n]+\\n?)*"},{begin:"<%[%=-]?",end:"[%-]?%>",subLanguage:"ruby",excludeBegin:!0,excludeEnd:!0,relevance:0},{className:"type",begin:"!\\w+!"+c},{className:"type",begin:"!<"+c+">"},{className:"type",begin:"!"+c},{className:"type",begin:"!!"+c},{className:"meta",begin:"&"+a.UNDERSCORE_IDENT_RE+"$"},{className:"meta",begin:"\\*"+a.UNDERSCORE_IDENT_RE+"$"},{className:"bullet",begin:"-(?=[ ]|$)",relevance:0},a.HASH_COMMENT_MODE,{beginKeywords:b,keywords:{literal:b}},{className:"number",begin:"\\b[0-9]{4}(-[0-9][0-9]){0,2}([Tt \\t][0-9][0-9]?(:[0-9][0-9]){2})?(\\.[0-9]*)?([ \\t])*(Z|[-+][0-9][0-9]?(:[0-9][0-9])?)?\\b"},{className:"number",begin:a.C_NUMBER_RE+"\\b",relevance:0},{begin:/\{/,end:/\}/,contains:[f],illegal:"\\n",relevance:0},{begin:"\\[",end:"\\]",contains:[f],illegal:"\\n",relevance:0},{className:"string",relevance:0,begin:/'/,end:/'/,contains:[{match:/''/,scope:"char.escape",relevance:0}]},d],h=[...g];return h.pop(),h.push(e),f.contains=h,{name:"YAML",case_insensitive:!0,aliases:["yml"],contains:g}}};var eo=c(50699);let ep={};class eq{constructor(a){this.options=a,this.root={type:"root",children:[],data:{language:void 0,relevance:0}},this.stack=[this.root]}addText(a){if(""===a)return;let b=this.stack[this.stack.length-1],c=b.children[b.children.length-1];c&&"text"===c.type?c.value+=a:b.children.push({type:"text",value:a})}startScope(a){this.openNode(String(a))}endScope(){this.closeNode()}__addSublanguage(a,b){let c=this.stack[this.stack.length-1],d=a.root.children;b?c.children.push({type:"element",tagName:"span",properties:{className:[b]},children:d}):c.children.push(...d)}openNode(a){let b=this,c=a.split(".").map(function(a,c){return c?a+"_".repeat(c):b.options.classPrefix+a}),d=this.stack[this.stack.length-1],e={type:"element",tagName:"span",properties:{className:c},children:[]};d.children.push(e),this.stack.push(e)}closeNode(){this.stack.pop()}finalize(){}toHTML(){return""}}let er={};function es(a){let b=a||er,c=b.aliases,d=b.detect||!1,e=b.languages||en,f=b.plainText,g=b.prefix,h=b.subset,i="hljs",j=function(a){let b=eo.newInstance();return a&&e(a),{highlight:c,highlightAuto:function(a,e){let f,g=(e||ep).subset||d(),h=-1,i=0;for(;++h<g.length;){let d=g[h];if(!b.getLanguage(d))continue;let j=c(d,a,e);j.data&&void 0!==j.data.relevance&&j.data.relevance>i&&(i=j.data.relevance,f=j)}return f||{type:"root",children:[],data:{language:void 0,relevance:i}}},listLanguages:d,register:e,registerAlias:function(a,c){if("string"==typeof a)b.registerAliases("string"==typeof c?c:[...c],{languageName:a});else{let c;for(c in a)if(Object.hasOwn(a,c)){let d=a[c];b.registerAliases("string"==typeof d?d:[...d],{languageName:c})}}},registered:function(a){return!!b.getLanguage(a)}};function c(a,c,d){let e=d||ep,f="string"==typeof e.prefix?e.prefix:"hljs-";if(!b.getLanguage(a))throw Error("Unknown language: `"+a+"` is not registered");b.configure({__emitter:eq,classPrefix:f});let g=b.highlight(c,{ignoreIllegals:!0,language:a});if(g.errorRaised)throw Error("Could not highlight with `Highlight.js`",{cause:g.errorRaised});let h=g._emitter.root,i=h.data;return i.language=g.language,i.relevance=g.relevance,h}function d(){return b.listLanguages()}function e(a,c){if("string"==typeof a)b.registerLanguage(a,c);else{let c;for(c in a)Object.hasOwn(a,c)&&b.registerLanguage(c,a[c])}}}(e);if(c&&j.registerAlias(c),g){let a=g.indexOf("-");i=-1===a?g:g.slice(0,a)}return function(a,b){bQ(a,"element",function(a,c,e){let k;if("code"!==a.tagName||!e||"element"!==e.type||"pre"!==e.tagName)return;let l=function(a){let b,c=a.properties.className,d=-1;if(Array.isArray(c)){for(;++d<c.length;){let a=String(c[d]);if("no-highlight"===a||"nohighlight"===a)return!1;b||"lang-"!==a.slice(0,5)||(b=a.slice(5)),b||"language-"!==a.slice(0,9)||(b=a.slice(9))}return b}}(a);if(!1===l||!l&&!d||l&&f&&f.includes(l))return;Array.isArray(a.properties.className)||(a.properties.className=[]),a.properties.className.includes(i)||a.properties.className.unshift(i);let m=function(a,b){let c,d="children"in a?a.children:[],e=dl(a),f=dn(a,{whitespace:(b||{}).whitespace||"normal",breakBefore:!1,breakAfter:!1}),g=[];("text"===a.type||"comment"===a.type)&&g.push(...dm(a,{whitespace:f,breakBefore:!0,breakAfter:!0}));let h=-1;for(;++h<d.length;)g.push(...function a(b,c,d){return"element"===b.type?function(b,c,d){let e,f,g=dn(b,d),h=b.children||[],i=-1,j=[];if(dk(b))return j;for(dg(b)||dj(b)&&dd(c,b,dj)?f="\n":di(b)?(e=2,f=2):dl(b)&&(e=1,f=1);++i<h.length;)j=j.concat(a(h[i],b,{whitespace:g,breakBefore:i?void 0:e,breakAfter:i<h.length-1?dg(h[i+1]):f}));return dh(b)&&dd(c,b,dh)&&j.push("	"),e&&j.unshift(e),f&&j.push(f),j}(b,c,d):"text"===b.type?"normal"===d.whitespace?dm(b,d):[String(b.value)]:[]}(d[h],a,{whitespace:f,breakBefore:h?void 0:e,breakAfter:h<d.length-1?dg(d[h+1]):e}));let i=[];for(h=-1;++h<g.length;){let a=g[h];"number"==typeof a?void 0!==c&&a>c&&(c=a):a&&(void 0!==c&&c>-1&&i.push("\n".repeat(c)||" "),c=-1,i.push(a))}return i.join("")}(a,{whitespace:"pre"});try{k=l?j.highlight(l,m,{prefix:g}):j.highlightAuto(m,{prefix:g,subset:h})}catch(c){if(l&&/Unknown language/.test(c.message))return void b.message("Cannot highlight as `"+l+"`, it’s not registered",{ancestors:[e,a],cause:c,place:a.position,ruleId:"missing-language",source:"rehype-highlight"});throw c}!l&&k.data&&k.data.language&&a.properties.className.push("language-"+k.data.language),k.children.length>0&&(a.children=k.children)})}}let et=["area","base","basefont","bgsound","br","col","command","embed","frame","hr","image","img","input","keygen","link","meta","param","source","track","wbr"];class eu{constructor(a,b,c){this.normal=b,this.property=a,c&&(this.space=c)}}function ev(a,b){let c={},d={};for(let b of a)Object.assign(c,b.property),Object.assign(d,b.normal);return new eu(c,d,b)}function ew(a){return a.toLowerCase()}eu.prototype.normal={},eu.prototype.property={},eu.prototype.space=void 0;class ex{constructor(a,b){this.attribute=b,this.property=a}}ex.prototype.attribute="",ex.prototype.booleanish=!1,ex.prototype.boolean=!1,ex.prototype.commaOrSpaceSeparated=!1,ex.prototype.commaSeparated=!1,ex.prototype.defined=!1,ex.prototype.mustUseProperty=!1,ex.prototype.number=!1,ex.prototype.overloadedBoolean=!1,ex.prototype.property="",ex.prototype.spaceSeparated=!1,ex.prototype.space=void 0;let ey=0,ez=eG(),eA=eG(),eB=eG(),eC=eG(),eD=eG(),eE=eG(),eF=eG();function eG(){return 2**++ey}let eH=Object.keys(f);class eI extends ex{constructor(a,b,c,d){let e=-1;if(super(a,b),function(a,b,c){c&&(a[b]=c)}(this,"space",d),"number"==typeof c)for(;++e<eH.length;){let a=eH[e];!function(a,b,c){c&&(a[b]=c)}(this,eH[e],(c&f[a])===f[a])}}}function eJ(a){let b={},c={};for(let[d,e]of Object.entries(a.properties)){let f=new eI(d,a.transform(a.attributes||{},d),e,a.space);a.mustUseProperty&&a.mustUseProperty.includes(d)&&(f.mustUseProperty=!0),b[d]=f,c[ew(d)]=d,c[ew(f.attribute)]=d}return new eu(b,c,a.space)}eI.prototype.defined=!0;let eK=eJ({properties:{ariaActiveDescendant:null,ariaAtomic:eA,ariaAutoComplete:null,ariaBusy:eA,ariaChecked:eA,ariaColCount:eC,ariaColIndex:eC,ariaColSpan:eC,ariaControls:eD,ariaCurrent:null,ariaDescribedBy:eD,ariaDetails:null,ariaDisabled:eA,ariaDropEffect:eD,ariaErrorMessage:null,ariaExpanded:eA,ariaFlowTo:eD,ariaGrabbed:eA,ariaHasPopup:null,ariaHidden:eA,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:eD,ariaLevel:eC,ariaLive:null,ariaModal:eA,ariaMultiLine:eA,ariaMultiSelectable:eA,ariaOrientation:null,ariaOwns:eD,ariaPlaceholder:null,ariaPosInSet:eC,ariaPressed:eA,ariaReadOnly:eA,ariaRelevant:null,ariaRequired:eA,ariaRoleDescription:eD,ariaRowCount:eC,ariaRowIndex:eC,ariaRowSpan:eC,ariaSelected:eA,ariaSetSize:eC,ariaSort:null,ariaValueMax:eC,ariaValueMin:eC,ariaValueNow:eC,ariaValueText:null,role:null},transform:(a,b)=>"role"===b?b:"aria-"+b.slice(4).toLowerCase()});function eL(a,b){return b in a?a[b]:b}function eM(a,b){return eL(a,b.toLowerCase())}let eN=eJ({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:eE,acceptCharset:eD,accessKey:eD,action:null,allow:null,allowFullScreen:ez,allowPaymentRequest:ez,allowUserMedia:ez,alt:null,as:null,async:ez,autoCapitalize:null,autoComplete:eD,autoFocus:ez,autoPlay:ez,blocking:eD,capture:null,charSet:null,checked:ez,cite:null,className:eD,cols:eC,colSpan:null,content:null,contentEditable:eA,controls:ez,controlsList:eD,coords:eC|eE,crossOrigin:null,data:null,dateTime:null,decoding:null,default:ez,defer:ez,dir:null,dirName:null,disabled:ez,download:eB,draggable:eA,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:ez,formTarget:null,headers:eD,height:eC,hidden:eB,high:eC,href:null,hrefLang:null,htmlFor:eD,httpEquiv:eD,id:null,imageSizes:null,imageSrcSet:null,inert:ez,inputMode:null,integrity:null,is:null,isMap:ez,itemId:null,itemProp:eD,itemRef:eD,itemScope:ez,itemType:eD,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:ez,low:eC,manifest:null,max:null,maxLength:eC,media:null,method:null,min:null,minLength:eC,multiple:ez,muted:ez,name:null,nonce:null,noModule:ez,noValidate:ez,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:ez,optimum:eC,pattern:null,ping:eD,placeholder:null,playsInline:ez,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:ez,referrerPolicy:null,rel:eD,required:ez,reversed:ez,rows:eC,rowSpan:eC,sandbox:eD,scope:null,scoped:ez,seamless:ez,selected:ez,shadowRootClonable:ez,shadowRootDelegatesFocus:ez,shadowRootMode:null,shape:null,size:eC,sizes:null,slot:null,span:eC,spellCheck:eA,src:null,srcDoc:null,srcLang:null,srcSet:null,start:eC,step:null,style:null,tabIndex:eC,target:null,title:null,translate:null,type:null,typeMustMatch:ez,useMap:null,value:eA,width:eC,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:eD,axis:null,background:null,bgColor:null,border:eC,borderColor:null,bottomMargin:eC,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:ez,declare:ez,event:null,face:null,frame:null,frameBorder:null,hSpace:eC,leftMargin:eC,link:null,longDesc:null,lowSrc:null,marginHeight:eC,marginWidth:eC,noResize:ez,noHref:ez,noShade:ez,noWrap:ez,object:null,profile:null,prompt:null,rev:null,rightMargin:eC,rules:null,scheme:null,scrolling:eA,standby:null,summary:null,text:null,topMargin:eC,valueType:null,version:null,vAlign:null,vLink:null,vSpace:eC,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:ez,disableRemotePlayback:ez,prefix:null,property:null,results:eC,security:null,unselectable:null},space:"html",transform:eM}),eO=eJ({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:eF,accentHeight:eC,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:eC,amplitude:eC,arabicForm:null,ascent:eC,attributeName:null,attributeType:null,azimuth:eC,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:eC,by:null,calcMode:null,capHeight:eC,className:eD,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:eC,diffuseConstant:eC,direction:null,display:null,dur:null,divisor:eC,dominantBaseline:null,download:ez,dx:null,dy:null,edgeMode:null,editable:null,elevation:eC,enableBackground:null,end:null,event:null,exponent:eC,externalResourcesRequired:null,fill:null,fillOpacity:eC,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:eE,g2:eE,glyphName:eE,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:eC,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:eC,horizOriginX:eC,horizOriginY:eC,id:null,ideographic:eC,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:eC,k:eC,k1:eC,k2:eC,k3:eC,k4:eC,kernelMatrix:eF,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:eC,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:eC,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:eC,overlineThickness:eC,paintOrder:null,panose1:null,path:null,pathLength:eC,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:eD,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:eC,pointsAtY:eC,pointsAtZ:eC,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:eF,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:eF,rev:eF,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:eF,requiredFeatures:eF,requiredFonts:eF,requiredFormats:eF,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:eC,specularExponent:eC,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:eC,strikethroughThickness:eC,string:null,stroke:null,strokeDashArray:eF,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:eC,strokeOpacity:eC,strokeWidth:null,style:null,surfaceScale:eC,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:eF,tabIndex:eC,tableValues:null,target:null,targetX:eC,targetY:eC,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:eF,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:eC,underlineThickness:eC,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:eC,values:null,vAlphabetic:eC,vMathematical:eC,vectorEffect:null,vHanging:eC,vIdeographic:eC,version:null,vertAdvY:eC,vertOriginX:eC,vertOriginY:eC,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:eC,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:eL}),eP=eJ({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(a,b)=>"xlink:"+b.slice(5).toLowerCase()}),eQ=eJ({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:eM}),eR=eJ({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(a,b)=>"xml:"+b.slice(3).toLowerCase()}),eS=ev([eK,eN,eP,eQ,eR],"html"),eT=ev([eK,eO,eP,eQ,eR],"svg"),eU={}.hasOwnProperty,eV=/["&'<>`]/g,eW=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,eX=/[\x01-\t\v\f\x0E-\x1F\x7F\x81\x8D\x8F\x90\x9D\xA0-\uFFFF]/g,eY=/[|\\{}()[\]^$+*?.]/g,eZ=new WeakMap,e$=/[\dA-Fa-f]/,e_=/\d/,e0=["AElig","AMP","Aacute","Acirc","Agrave","Aring","Atilde","Auml","COPY","Ccedil","ETH","Eacute","Ecirc","Egrave","Euml","GT","Iacute","Icirc","Igrave","Iuml","LT","Ntilde","Oacute","Ocirc","Ograve","Oslash","Otilde","Ouml","QUOT","REG","THORN","Uacute","Ucirc","Ugrave","Uuml","Yacute","aacute","acirc","acute","aelig","agrave","amp","aring","atilde","auml","brvbar","ccedil","cedil","cent","copy","curren","deg","divide","eacute","ecirc","egrave","eth","euml","frac12","frac14","frac34","gt","iacute","icirc","iexcl","igrave","iquest","iuml","laquo","lt","macr","micro","middot","nbsp","not","ntilde","oacute","ocirc","ograve","ordf","ordm","oslash","otilde","ouml","para","plusmn","pound","quot","raquo","reg","sect","shy","sup1","sup2","sup3","szlig","thorn","times","uacute","ucirc","ugrave","uml","uuml","yacute","yen","yuml"],e1={nbsp:"\xa0",iexcl:"\xa1",cent:"\xa2",pound:"\xa3",curren:"\xa4",yen:"\xa5",brvbar:"\xa6",sect:"\xa7",uml:"\xa8",copy:"\xa9",ordf:"\xaa",laquo:"\xab",not:"\xac",shy:"\xad",reg:"\xae",macr:"\xaf",deg:"\xb0",plusmn:"\xb1",sup2:"\xb2",sup3:"\xb3",acute:"\xb4",micro:"\xb5",para:"\xb6",middot:"\xb7",cedil:"\xb8",sup1:"\xb9",ordm:"\xba",raquo:"\xbb",frac14:"\xbc",frac12:"\xbd",frac34:"\xbe",iquest:"\xbf",Agrave:"\xc0",Aacute:"\xc1",Acirc:"\xc2",Atilde:"\xc3",Auml:"\xc4",Aring:"\xc5",AElig:"\xc6",Ccedil:"\xc7",Egrave:"\xc8",Eacute:"\xc9",Ecirc:"\xca",Euml:"\xcb",Igrave:"\xcc",Iacute:"\xcd",Icirc:"\xce",Iuml:"\xcf",ETH:"\xd0",Ntilde:"\xd1",Ograve:"\xd2",Oacute:"\xd3",Ocirc:"\xd4",Otilde:"\xd5",Ouml:"\xd6",times:"\xd7",Oslash:"\xd8",Ugrave:"\xd9",Uacute:"\xda",Ucirc:"\xdb",Uuml:"\xdc",Yacute:"\xdd",THORN:"\xde",szlig:"\xdf",agrave:"\xe0",aacute:"\xe1",acirc:"\xe2",atilde:"\xe3",auml:"\xe4",aring:"\xe5",aelig:"\xe6",ccedil:"\xe7",egrave:"\xe8",eacute:"\xe9",ecirc:"\xea",euml:"\xeb",igrave:"\xec",iacute:"\xed",icirc:"\xee",iuml:"\xef",eth:"\xf0",ntilde:"\xf1",ograve:"\xf2",oacute:"\xf3",ocirc:"\xf4",otilde:"\xf5",ouml:"\xf6",divide:"\xf7",oslash:"\xf8",ugrave:"\xf9",uacute:"\xfa",ucirc:"\xfb",uuml:"\xfc",yacute:"\xfd",thorn:"\xfe",yuml:"\xff",fnof:"ƒ",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",bull:"•",hellip:"…",prime:"′",Prime:"″",oline:"‾",frasl:"⁄",weierp:"℘",image:"ℑ",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦",quot:'"',amp:"&",lt:"<",gt:">",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",circ:"ˆ",tilde:"˜",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",permil:"‰",lsaquo:"‹",rsaquo:"›",euro:"€"},e2=["cent","copy","divide","gt","lt","not","para","times"],e3={}.hasOwnProperty,e4={};for(d in e1)e3.call(e1,d)&&(e4[e1[d]]=d);let e5=/[^\dA-Za-z]/;function e6(a,b,c){let d,e=function(a,b,c){let d="&#x"+a.toString(16).toUpperCase();return c&&b&&!e$.test(String.fromCharCode(b))?d:d+";"}(a,b,c.omitOptionalSemicolons);if((c.useNamedReferences||c.useShortestReferences)&&(d=function(a,b,c,d){let e=String.fromCharCode(a);if(e3.call(e4,e)){let a=e4[e],f="&"+a;return c&&e0.includes(a)&&!e2.includes(a)&&(!d||b&&61!==b&&e5.test(String.fromCharCode(b)))?f:f+";"}return""}(a,b,c.omitOptionalSemicolons,c.attribute)),(c.useShortestReferences||!d)&&c.useShortestReferences){let d=function(a,b,c){let d="&#"+String(a);return c&&b&&!e_.test(String.fromCharCode(b))?d:d+";"}(a,b,c.omitOptionalSemicolons);d.length<e.length&&(e=d)}return d&&(!c.useShortestReferences||d.length<e.length)?d:e}function e7(a,b){let c;var d,e=a,f=Object.assign({format:e6},b);if(e=e.replace(f.subset?(d=f.subset,(c=eZ.get(d))||(c=function(a){let b=[],c=-1;for(;++c<a.length;)b.push(a[c].replace(eY,"\\$&"));return RegExp("(?:"+b.join("|")+")","g")}(d),eZ.set(d,c)),c):eV,g),f.subset||f.escapeOnly)return e;return e.replace(eW,function(a,b,c){return f.format((a.charCodeAt(0)-55296)*1024+a.charCodeAt(1)-56320+65536,c.charCodeAt(b+2),f)}).replace(eX,g);function g(a,b,c){return f.format(a.charCodeAt(0),c.charCodeAt(b+1),f)}}let e8=/^>|^->|<!--|-->|--!>|<!-$/g,e9=[">"],fa=["<",">"],fb=/[A-Z]/g,fc=/-[a-z]/g,fd=/^data[-\w.:]+$/i;function fe(a){return"-"+a.toLowerCase()}function ff(a){return a.charAt(1).toUpperCase()}let fg=/[ \t\n\f\r]/g;function fh(a){return"object"==typeof a?"text"===a.type&&fi(a.value):fi(a)}function fi(a){return""===a.replace(fg,"")}let fj=fm(1),fk=fm(-1),fl=[];function fm(a){return function(b,c,d){let e=b?b.children:fl,f=(c||0)+a,g=e[f];if(!d)for(;g&&fh(g);)f+=a,g=e[f];return g}}let fn={}.hasOwnProperty;function fo(a){return function(b,c,d){return fn.call(a,b.tagName)&&a[b.tagName](b,c,d)}}let fp=fo({body:function(a,b,c){let d=fj(c,b);return!d||"comment"!==d.type},caption:fq,colgroup:fq,dd:function(a,b,c){let d=fj(c,b);return!d||"element"===d.type&&("dt"===d.tagName||"dd"===d.tagName)},dt:function(a,b,c){let d=fj(c,b);return!!(d&&"element"===d.type&&("dt"===d.tagName||"dd"===d.tagName))},head:fq,html:function(a,b,c){let d=fj(c,b);return!d||"comment"!==d.type},li:function(a,b,c){let d=fj(c,b);return!d||"element"===d.type&&"li"===d.tagName},optgroup:function(a,b,c){let d=fj(c,b);return!d||"element"===d.type&&"optgroup"===d.tagName},option:function(a,b,c){let d=fj(c,b);return!d||"element"===d.type&&("option"===d.tagName||"optgroup"===d.tagName)},p:function(a,b,c){let d=fj(c,b);return d?"element"===d.type&&("address"===d.tagName||"article"===d.tagName||"aside"===d.tagName||"blockquote"===d.tagName||"details"===d.tagName||"div"===d.tagName||"dl"===d.tagName||"fieldset"===d.tagName||"figcaption"===d.tagName||"figure"===d.tagName||"footer"===d.tagName||"form"===d.tagName||"h1"===d.tagName||"h2"===d.tagName||"h3"===d.tagName||"h4"===d.tagName||"h5"===d.tagName||"h6"===d.tagName||"header"===d.tagName||"hgroup"===d.tagName||"hr"===d.tagName||"main"===d.tagName||"menu"===d.tagName||"nav"===d.tagName||"ol"===d.tagName||"p"===d.tagName||"pre"===d.tagName||"section"===d.tagName||"table"===d.tagName||"ul"===d.tagName):!c||"element"!==c.type||"a"!==c.tagName&&"audio"!==c.tagName&&"del"!==c.tagName&&"ins"!==c.tagName&&"map"!==c.tagName&&"noscript"!==c.tagName&&"video"!==c.tagName},rp:fr,rt:fr,tbody:function(a,b,c){let d=fj(c,b);return!d||"element"===d.type&&("tbody"===d.tagName||"tfoot"===d.tagName)},td:fs,tfoot:function(a,b,c){return!fj(c,b)},th:fs,thead:function(a,b,c){let d=fj(c,b);return!!(d&&"element"===d.type&&("tbody"===d.tagName||"tfoot"===d.tagName))},tr:function(a,b,c){let d=fj(c,b);return!d||"element"===d.type&&"tr"===d.tagName}});function fq(a,b,c){let d=fj(c,b,!0);return!d||"comment"!==d.type&&!("text"===d.type&&fh(d.value.charAt(0)))}function fr(a,b,c){let d=fj(c,b);return!d||"element"===d.type&&("rp"===d.tagName||"rt"===d.tagName)}function fs(a,b,c){let d=fj(c,b);return!d||"element"===d.type&&("td"===d.tagName||"th"===d.tagName)}let ft=fo({body:function(a){let b=fj(a,-1,!0);return!b||"comment"!==b.type&&!("text"===b.type&&fh(b.value.charAt(0)))&&("element"!==b.type||"meta"!==b.tagName&&"link"!==b.tagName&&"script"!==b.tagName&&"style"!==b.tagName&&"template"!==b.tagName)},colgroup:function(a,b,c){let d=fk(c,b),e=fj(a,-1,!0);return!(c&&d&&"element"===d.type&&"colgroup"===d.tagName&&fp(d,c.children.indexOf(d),c))&&!!(e&&"element"===e.type&&"col"===e.tagName)},head:function(a){let b=new Set;for(let c of a.children)if("element"===c.type&&("base"===c.tagName||"title"===c.tagName)){if(b.has(c.tagName))return!1;b.add(c.tagName)}let c=a.children[0];return!c||"element"===c.type},html:function(a){let b=fj(a,-1);return!b||"comment"!==b.type},tbody:function(a,b,c){let d=fk(c,b),e=fj(a,-1);return!(c&&d&&"element"===d.type&&("thead"===d.tagName||"tbody"===d.tagName)&&fp(d,c.children.indexOf(d),c))&&!!(e&&"element"===e.type&&"tr"===e.tagName)}}),fu={name:[["	\n\f\r &/=>".split(""),"	\n\f\r \"&'/=>`".split("")],["\0	\n\f\r \"&'/<=>".split(""),"\0	\n\f\r \"&'/<=>`".split("")]],unquoted:[["	\n\f\r &>".split(""),"\0	\n\f\r \"&'<=>`".split("")],["\0	\n\f\r \"&'<=>`".split(""),"\0	\n\f\r \"&'<=>`".split("")]],single:[["&'".split(""),"\"&'`".split("")],["\0&'".split(""),"\0\"&'`".split("")]],double:[['"&'.split(""),"\"&'`".split("")],['\0"&'.split(""),"\0\"&'`".split("")]]},fv=["<","&"];function fw(a,b,c,d){return c&&"element"===c.type&&("script"===c.tagName||"style"===c.tagName)?a.value:e7(a.value,Object.assign({},d.settings.characterReferences,{subset:fv}))}let fx=function(a,b){let c=b||{};function d(b,...c){let e=d.invalid,f=d.handlers;if(b&&eU.call(b,a)){let c=String(b[a]);e=eU.call(f,c)?f[c]:d.unknown}if(e)return e.call(this,b,...c)}return d.handlers=c.handlers||{},d.invalid=c.invalid,d.unknown=c.unknown,d}("type",{invalid:function(a){throw Error("Expected node, not `"+a+"`")},unknown:function(a){throw Error("Cannot compile unknown node `"+a.type+"`")},handlers:{comment:function(a,b,c,d){return d.settings.bogusComments?"<?"+e7(a.value,Object.assign({},d.settings.characterReferences,{subset:e9}))+">":"\x3c!--"+a.value.replace(e8,function(a){return e7(a,Object.assign({},d.settings.characterReferences,{subset:fa}))})+"--\x3e"},doctype:function(a,b,c,d){return"<!"+(d.settings.upperDoctype?"DOCTYPE":"doctype")+(d.settings.tightDoctype?"":" ")+"html>"},element:function(a,b,c,d){let e,f=d.schema,g="svg"!==f.space&&d.settings.omitOptionalTags,h="svg"===f.space?d.settings.closeEmptyElements:d.settings.voids.includes(a.tagName.toLowerCase()),i=[];"html"===f.space&&"svg"===a.tagName&&(d.schema=eT);let j=function(a,b){let c,d=[],e=-1;if(b){for(c in b)if(null!==b[c]&&void 0!==b[c]){let e=function(a,b,c){let d,e=function(a,b){let c=ew(b),d=b,e=ex;if(c in a.normal)return a.property[a.normal[c]];if(c.length>4&&"data"===c.slice(0,4)&&fd.test(b)){if("-"===b.charAt(4)){let a=b.slice(5).replace(fc,ff);d="data"+a.charAt(0).toUpperCase()+a.slice(1)}else{let a=b.slice(4);if(!fc.test(a)){let c=a.replace(fb,fe);"-"!==c.charAt(0)&&(c="-"+c),b="data"+c}}e=eI}return new e(d,b)}(a.schema,b),f=a.settings.allowParseErrors&&"html"===a.schema.space?0:1,g=+!a.settings.allowDangerousCharacters,h=a.quote;if(e.overloadedBoolean&&(c===e.attribute||""===c)?c=!0:(e.boolean||e.overloadedBoolean)&&("string"!=typeof c||c===e.attribute||""===c)&&(c=!!c),null==c||!1===c||"number"==typeof c&&Number.isNaN(c))return"";let i=e7(e.attribute,Object.assign({},a.settings.characterReferences,{subset:fu.name[f][g]}));return!0===c||(c=Array.isArray(c)?(e.commaSeparated?function(a,b){let c=b||{};return(""===a[a.length-1]?[...a,""]:a).join((c.padRight?" ":"")+","+(!1===c.padLeft?"":" ")).trim()}:function(a){return a.join(" ").trim()})(c,{padLeft:!a.settings.tightCommaSeparatedLists}):String(c),a.settings.collapseEmptyAttributes&&!c)?i:(a.settings.preferUnquoted&&(d=e7(c,Object.assign({},a.settings.characterReferences,{attribute:!0,subset:fu.unquoted[f][g]}))),d!==c&&(a.settings.quoteSmart&&bg(c,h)>bg(c,a.alternative)&&(h=a.alternative),d=h+e7(c,Object.assign({},a.settings.characterReferences,{subset:("'"===h?fu.single:fu.double)[f][g],attribute:!0}))+h),i+(d?"="+d:d))}(a,c,b[c]);e&&d.push(e)}}for(;++e<d.length;){let b=a.settings.tightAttributes?d[e].charAt(d[e].length-1):void 0;e!==d.length-1&&'"'!==b&&"'"!==b&&(d[e]+=" ")}return d.join("")}(d,a.properties),k=d.all("html"===f.space&&"template"===a.tagName?a.content:a);return d.schema=f,k&&(h=!1),!j&&g&&ft(a,b,c)||(i.push("<",a.tagName,j?" "+j:""),h&&("svg"===f.space||d.settings.closeSelfClosing)&&(e=j.charAt(j.length-1),(!d.settings.tightSelfClosing||"/"===e||e&&'"'!==e&&"'"!==e)&&i.push(" "),i.push("/")),i.push(">")),i.push(k),h||g&&fp(a,b,c)||i.push("</"+a.tagName+">"),i.join("")},raw:function(a,b,c,d){return d.settings.allowDangerousHtml?a.value:fw(a,b,c,d)},root:function(a,b,c,d){return d.all(a)},text:fw}}),fy={},fz={},fA=[];function fB(a,b,c){return fx(a,b,c,this)}function fC(a){let b=[],c=a&&a.children||fA,d=-1;for(;++d<c.length;)b[d]=this.one(c[d],d,a);return b.join("")}function fD(a){let b={...this.data("settings"),...a};this.compiler=function(a){let c=b||fy,d=c.quote||'"';if('"'!==d&&"'"!==d)throw Error("Invalid quote `"+d+"`, expected `'` or `\"`");return({one:fB,all:fC,settings:{omitOptionalTags:c.omitOptionalTags||!1,allowParseErrors:c.allowParseErrors||!1,allowDangerousCharacters:c.allowDangerousCharacters||!1,quoteSmart:c.quoteSmart||!1,preferUnquoted:c.preferUnquoted||!1,tightAttributes:c.tightAttributes||!1,upperDoctype:c.upperDoctype||!1,tightDoctype:c.tightDoctype||!1,bogusComments:c.bogusComments||!1,tightCommaSeparatedLists:c.tightCommaSeparatedLists||!1,tightSelfClosing:c.tightSelfClosing||!1,collapseEmptyAttributes:c.collapseEmptyAttributes||!1,allowDangerousHtml:c.allowDangerousHtml||!1,voids:c.voids||et,characterReferences:c.characterReferences||fz,closeSelfClosing:c.closeSelfClosing||!1,closeEmptyElements:c.closeEmptyElements||!1},schema:"svg"===c.space?eT:eS,quote:d,alternative:'"'===d?"'":'"'}).one(Array.isArray(a)?{type:"root",children:a}:a,void 0,void 0)}}async function fE(a){return String(await A().use(bf).use(cw).use(cX,{allowDangerousHtml:!0}).use(c2).use(c8,{behavior:"wrap"}).use(es).use(fD,{allowDangerousHtml:!0}).process(a))}},41891:a=>{"use strict";var b=Object.prototype.hasOwnProperty,c=Object.prototype.toString,d=Object.defineProperty,e=Object.getOwnPropertyDescriptor,f=function(a){return"function"==typeof Array.isArray?Array.isArray(a):"[object Array]"===c.call(a)},g=function(a){if(!a||"[object Object]"!==c.call(a))return!1;var d,e=b.call(a,"constructor"),f=a.constructor&&a.constructor.prototype&&b.call(a.constructor.prototype,"isPrototypeOf");if(a.constructor&&!e&&!f)return!1;for(d in a);return void 0===d||b.call(a,d)},h=function(a,b){d&&"__proto__"===b.name?d(a,b.name,{enumerable:!0,configurable:!0,value:b.newValue,writable:!0}):a[b.name]=b.newValue},i=function(a,c){if("__proto__"===c){if(!b.call(a,c))return;else if(e)return e(a,c).value}return a[c]};a.exports=function a(){var b,c,d,e,j,k,l=arguments[0],m=1,n=arguments.length,o=!1;for("boolean"==typeof l&&(o=l,l=arguments[1]||{},m=2),(null==l||"object"!=typeof l&&"function"!=typeof l)&&(l={});m<n;++m)if(b=arguments[m],null!=b)for(c in b)d=i(l,c),l!==(e=i(b,c))&&(o&&e&&(g(e)||(j=f(e)))?(j?(j=!1,k=d&&f(d)?d:[]):k=d&&g(d)?d:{},h(l,{name:c,newValue:a(o,k,e)})):void 0!==e&&h(l,{name:c,newValue:e}));return l}},50699:a=>{class b{constructor(a){void 0===a.data&&(a.data={}),this.data=a.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}}function c(a){return a.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function d(a,...b){let c=Object.create(null);for(let b in a)c[b]=a[b];return b.forEach(function(a){for(let b in a)c[b]=a[b]}),c}class e{constructor(a,b){this.buffer="",this.classPrefix=b.classPrefix,a.walk(this)}addText(a){this.buffer+=c(a)}openNode(a){if(!a.scope)return;let b=((a,{prefix:b})=>{if(a.startsWith("language:"))return a.replace("language:","language-");if(a.includes(".")){let c=a.split(".");return[`${b}${c.shift()}`,...c.map((a,b)=>`${a}${"_".repeat(b+1)}`)].join(" ")}return`${b}${a}`})(a.scope,{prefix:this.classPrefix});this.span(b)}closeNode(a){a.scope&&(this.buffer+="</span>")}value(){return this.buffer}span(a){this.buffer+=`<span class="${a}">`}}let f=(a={})=>{let b={children:[]};return Object.assign(b,a),b};class g{constructor(){this.rootNode=f(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(a){this.top.children.push(a)}openNode(a){let b=f({scope:a});this.add(b),this.stack.push(b)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(a){return this.constructor._walk(a,this.rootNode)}static _walk(a,b){return"string"==typeof b?a.addText(b):b.children&&(a.openNode(b),b.children.forEach(b=>this._walk(a,b)),a.closeNode(b)),a}static _collapse(a){"string"!=typeof a&&a.children&&(a.children.every(a=>"string"==typeof a)?a.children=[a.children.join("")]:a.children.forEach(a=>{g._collapse(a)}))}}class h extends g{constructor(a){super(),this.options=a}addText(a){""!==a&&this.add(a)}startScope(a){this.openNode(a)}endScope(){this.closeNode()}__addSublanguage(a,b){let c=a.root;b&&(c.scope=`language:${b}`),this.add(c)}toHTML(){return new e(this,this.options).value()}finalize(){return this.closeAllNodes(),!0}}function i(a){return a?"string"==typeof a?a:a.source:null}function j(a){return m("(?=",a,")")}function k(a){return m("(?:",a,")*")}function l(a){return m("(?:",a,")?")}function m(...a){return a.map(a=>i(a)).join("")}function n(...a){return"("+(function(a){let b=a[a.length-1];return"object"==typeof b&&b.constructor===Object?(a.splice(a.length-1,1),b):{}}(a).capture?"":"?:")+a.map(a=>i(a)).join("|")+")"}function o(a){return RegExp(a.toString()+"|").exec("").length-1}let p=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function q(a,{joinWith:b}){let c=0;return a.map(a=>{let b=c+=1,d=i(a),e="";for(;d.length>0;){let a=p.exec(d);if(!a){e+=d;break}e+=d.substring(0,a.index),d=d.substring(a.index+a[0].length),"\\"===a[0][0]&&a[1]?e+="\\"+String(Number(a[1])+b):(e+=a[0],"("===a[0]&&c++)}return e}).map(a=>`(${a})`).join(b)}let r="[a-zA-Z]\\w*",s="[a-zA-Z_]\\w*",t="\\b\\d+(\\.\\d+)?",u="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",v="\\b(0b[01]+)",w={begin:"\\\\[\\s\\S]",relevance:0},x=function(a,b,c={}){let e=d({scope:"comment",begin:a,end:b,contains:[]},c);e.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});let f=n("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return e.contains.push({begin:m(/[ ]+/,"(",f,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),e},y=x("//","$"),z=x("/\\*","\\*/"),A=x("#","$");var B=Object.freeze({__proto__:null,APOS_STRING_MODE:{scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[w]},BACKSLASH_ESCAPE:w,BINARY_NUMBER_MODE:{scope:"number",begin:v,relevance:0},BINARY_NUMBER_RE:v,COMMENT:x,C_BLOCK_COMMENT_MODE:z,C_LINE_COMMENT_MODE:y,C_NUMBER_MODE:{scope:"number",begin:u,relevance:0},C_NUMBER_RE:u,END_SAME_AS_BEGIN:function(a){return Object.assign(a,{"on:begin":(a,b)=>{b.data._beginMatch=a[1]},"on:end":(a,b)=>{b.data._beginMatch!==a[1]&&b.ignoreMatch()}})},HASH_COMMENT_MODE:A,IDENT_RE:r,MATCH_NOTHING_RE:/\b\B/,METHOD_GUARD:{begin:"\\.\\s*"+s,relevance:0},NUMBER_MODE:{scope:"number",begin:t,relevance:0},NUMBER_RE:t,PHRASAL_WORDS_MODE:{begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},QUOTE_STRING_MODE:{scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[w]},REGEXP_MODE:{scope:"regexp",begin:/\/(?=[^/\n]*\/)/,end:/\/[gimuy]*/,contains:[w,{begin:/\[/,end:/\]/,relevance:0,contains:[w]}]},RE_STARTERS_RE:"!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",SHEBANG:(a={})=>{let b=/^#![ ]*\//;return a.binary&&(a.begin=m(b,/.*\b/,a.binary,/\b.*/)),d({scope:"meta",begin:b,end:/$/,relevance:0,"on:begin":(a,b)=>{0!==a.index&&b.ignoreMatch()}},a)},TITLE_MODE:{scope:"title",begin:r,relevance:0},UNDERSCORE_IDENT_RE:s,UNDERSCORE_TITLE_MODE:{scope:"title",begin:s,relevance:0}});function C(a,b){"."===a.input[a.index-1]&&b.ignoreMatch()}function D(a,b){void 0!==a.className&&(a.scope=a.className,delete a.className)}function E(a,b){b&&a.beginKeywords&&(a.begin="\\b("+a.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",a.__beforeBegin=C,a.keywords=a.keywords||a.beginKeywords,delete a.beginKeywords,void 0===a.relevance&&(a.relevance=0))}function F(a,b){Array.isArray(a.illegal)&&(a.illegal=n(...a.illegal))}function G(a,b){if(a.match){if(a.begin||a.end)throw Error("begin & end are not supported with match");a.begin=a.match,delete a.match}}function H(a,b){void 0===a.relevance&&(a.relevance=1)}let I=(a,b)=>{if(!a.beforeMatch)return;if(a.starts)throw Error("beforeMatch cannot be used with starts");let c=Object.assign({},a);Object.keys(a).forEach(b=>{delete a[b]}),a.keywords=c.keywords,a.begin=m(c.beforeMatch,j(c.begin)),a.starts={relevance:0,contains:[Object.assign(c,{endsParent:!0})]},a.relevance=0,delete c.beforeMatch},J=["of","and","for","in","not","or","if","then","parent","list","value"],K={},L=a=>{console.error(a)},M=(a,...b)=>{console.log(`WARN: ${a}`,...b)},N=(a,b)=>{K[`${a}/${b}`]||(console.log(`Deprecated as of ${a}. ${b}`),K[`${a}/${b}`]=!0)},O=Error();function P(a,b,{key:c}){let d=0,e=a[c],f={},g={};for(let a=1;a<=b.length;a++)g[a+d]=e[a],f[a+d]=!0,d+=o(b[a-1]);a[c]=g,a[c]._emit=f,a[c]._multi=!0}function Q(a){if(a.scope&&"object"==typeof a.scope&&null!==a.scope&&(a.beginScope=a.scope,delete a.scope),"string"==typeof a.beginScope&&(a.beginScope={_wrap:a.beginScope}),"string"==typeof a.endScope&&(a.endScope={_wrap:a.endScope}),Array.isArray(a.begin)){if(a.skip||a.excludeBegin||a.returnBegin)throw L("skip, excludeBegin, returnBegin not compatible with beginScope: {}"),O;if("object"!=typeof a.beginScope||null===a.beginScope)throw L("beginScope must be object"),O;P(a,a.begin,{key:"beginScope"}),a.begin=q(a.begin,{joinWith:""})}if(Array.isArray(a.end)){if(a.skip||a.excludeEnd||a.returnEnd)throw L("skip, excludeEnd, returnEnd not compatible with endScope: {}"),O;if("object"!=typeof a.endScope||null===a.endScope)throw L("endScope must be object"),O;P(a,a.end,{key:"endScope"}),a.end=q(a.end,{joinWith:""})}}class R extends Error{constructor(a,b){super(a),this.name="HTMLInjectionError",this.html=b}}let S=Symbol("nomatch"),T=function(a){let e=Object.create(null),f=Object.create(null),g=[],p=!0,r="Could not find the language '{}', did you forget to load/include a language module?",s={disableAutodetect:!0,name:"Plain text",contains:[]},t={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:h};function u(a){return t.noHighlightRe.test(a)}function v(a,b,c){let d="",e="";"object"==typeof b?(d=a,c=b.ignoreIllegals,e=b.language):(N("10.7.0","highlight(lang, code, ...args) has been deprecated."),N("10.7.0","Please use highlight(code, options) instead.\nhttps://github.com/highlightjs/highlight.js/issues/2277"),e=a,d=b),void 0===c&&(c=!0);let f={code:d,language:e};P("before:highlight",f);let g=f.result?f.result:w(f.language,f.code,c);return g.code=f.code,P("after:highlight",g),g}function w(a,f,g,h){let j=Object.create(null);function k(){if(!B.keywords)return void M.addText(O);let a=0;B.keywordPatternRe.lastIndex=0;let b=B.keywordPatternRe.exec(O),c="";for(;b;){c+=O.substring(a,b.index);let d=y.case_insensitive?b[0].toLowerCase():b[0],e=B.keywords[d];if(e){let[a,f]=e;if(M.addText(c),c="",j[d]=(j[d]||0)+1,j[d]<=7&&(P+=f),a.startsWith("_"))c+=b[0];else{let c=y.classNameAliases[a]||a;m(b[0],c)}}else c+=b[0];a=B.keywordPatternRe.lastIndex,b=B.keywordPatternRe.exec(O)}c+=O.substring(a),M.addText(c)}function l(){null!=B.subLanguage?function(){if(""===O)return;let a=null;if("string"==typeof B.subLanguage){if(!e[B.subLanguage])return M.addText(O);a=w(B.subLanguage,O,!0,K[B.subLanguage]),K[B.subLanguage]=a._top}else a=x(O,B.subLanguage.length?B.subLanguage:null);B.relevance>0&&(P+=a.relevance),M.__addSublanguage(a._emitter,a.language)}():k(),O=""}function m(a,b){""!==a&&(M.startScope(b),M.addText(a),M.endScope())}function n(a,b){let c=1,d=b.length-1;for(;c<=d;){if(!a._emit[c]){c++;continue}let d=y.classNameAliases[a[c]]||a[c],e=b[c];d?m(e,d):(O=e,k(),O=""),c++}}function s(a,b){return a.scope&&"string"==typeof a.scope&&M.openNode(y.classNameAliases[a.scope]||a.scope),a.beginScope&&(a.beginScope._wrap?(m(O,y.classNameAliases[a.beginScope._wrap]||a.beginScope._wrap),O=""):a.beginScope._multi&&(n(a.beginScope,b),O="")),B=Object.create(a,{parent:{value:B}})}let u={};function v(c,d){let e=d&&d[0];if(O+=c,null==e)return l(),0;if("begin"===u.type&&"end"===d.type&&u.index===d.index&&""===e){if(O+=f.slice(d.index,d.index+1),!p){let b=Error(`0 width match regex (${a})`);throw b.languageName=a,b.badRule=u.rule,b}return 1}if(u=d,"begin"===d.type){let a=d[0],c=d.rule,e=new b(c);for(let b of[c.__beforeBegin,c["on:begin"]])if(b&&(b(d,e),e.isMatchIgnored))return 0===B.matcher.regexIndex?(O+=a[0],1):(U=!0,0);return c.skip?O+=a:(c.excludeBegin&&(O+=a),l(),c.returnBegin||c.excludeBegin||(O=a)),s(c,d),c.returnBegin?0:a.length}if("illegal"!==d.type||g){if("end"===d.type){let a=function(a){let c=a[0],d=f.substring(a.index),e=function a(c,d,e){let f=function(a,b){let c=a&&a.exec(b);return c&&0===c.index}(c.endRe,e);if(f){if(c["on:end"]){let a=new b(c);c["on:end"](d,a),a.isMatchIgnored&&(f=!1)}if(f){for(;c.endsParent&&c.parent;)c=c.parent;return c}}if(c.endsWithParent)return a(c.parent,d,e)}(B,a,d);if(!e)return S;let g=B;B.endScope&&B.endScope._wrap?(l(),m(c,B.endScope._wrap)):B.endScope&&B.endScope._multi?(l(),n(B.endScope,a)):g.skip?O+=c:(g.returnEnd||g.excludeEnd||(O+=c),l(),g.excludeEnd&&(O=c));do B.scope&&M.closeNode(),B.skip||B.subLanguage||(P+=B.relevance),B=B.parent;while(B!==e.parent);return e.starts&&s(e.starts,a),g.returnEnd?0:c.length}(d);if(a!==S)return a}}else{let a=Error('Illegal lexeme "'+e+'" for mode "'+(B.scope||"<unnamed>")+'"');throw a.mode=B,a}if("illegal"===d.type&&""===e)return O+="\n",1;if(T>1e5&&T>3*d.index)throw Error("potential infinite loop, way more iterations than matches");return O+=e,e.length}let y=C(a);if(!y)throw L(r.replace("{}",a)),Error('Unknown language: "'+a+'"');let z=function(a){function b(b,c){return RegExp(i(b),"m"+(a.case_insensitive?"i":"")+(a.unicodeRegex?"u":"")+(c?"g":""))}class c{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(a,b){b.position=this.position++,this.matchIndexes[this.matchAt]=b,this.regexes.push([b,a]),this.matchAt+=o(a)+1}compile(){0===this.regexes.length&&(this.exec=()=>null);let a=this.regexes.map(a=>a[1]);this.matcherRe=b(q(a,{joinWith:"|"}),!0),this.lastIndex=0}exec(a){this.matcherRe.lastIndex=this.lastIndex;let b=this.matcherRe.exec(a);if(!b)return null;let c=b.findIndex((a,b)=>b>0&&void 0!==a),d=this.matchIndexes[c];return b.splice(0,c),Object.assign(b,d)}}class e{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(a){if(this.multiRegexes[a])return this.multiRegexes[a];let b=new c;return this.rules.slice(a).forEach(([a,c])=>b.addRule(a,c)),b.compile(),this.multiRegexes[a]=b,b}resumingScanAtSamePosition(){return 0!==this.regexIndex}considerAll(){this.regexIndex=0}addRule(a,b){this.rules.push([a,b]),"begin"===b.type&&this.count++}exec(a){let b=this.getMatcher(this.regexIndex);b.lastIndex=this.lastIndex;let c=b.exec(a);if(this.resumingScanAtSamePosition())if(c&&c.index===this.lastIndex);else{let b=this.getMatcher(0);b.lastIndex=this.lastIndex+1,c=b.exec(a)}return c&&(this.regexIndex+=c.position+1,this.regexIndex===this.count&&this.considerAll()),c}}if(a.compilerExtensions||(a.compilerExtensions=[]),a.contains&&a.contains.includes("self"))throw Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return a.classNameAliases=d(a.classNameAliases||{}),function c(f,g){if(f.isCompiled)return f;[D,G,Q,I].forEach(a=>a(f,g)),a.compilerExtensions.forEach(a=>a(f,g)),f.__beforeBegin=null,[E,F,H].forEach(a=>a(f,g)),f.isCompiled=!0;let h=null;return"object"==typeof f.keywords&&f.keywords.$pattern&&(f.keywords=Object.assign({},f.keywords),h=f.keywords.$pattern,delete f.keywords.$pattern),h=h||/\w+/,f.keywords&&(f.keywords=function a(b,c,d="keyword"){let e=Object.create(null);return"string"==typeof b?f(d,b.split(" ")):Array.isArray(b)?f(d,b):Object.keys(b).forEach(function(d){Object.assign(e,a(b[d],c,d))}),e;function f(a,b){c&&(b=b.map(a=>a.toLowerCase())),b.forEach(function(b){var c,d,f;let g=b.split("|");e[g[0]]=[a,(c=g[0],(d=g[1])?Number(d):+(f=c,!J.includes(f.toLowerCase())))]})}}(f.keywords,a.case_insensitive)),f.keywordPatternRe=b(h,!0),g&&(f.begin||(f.begin=/\B|\b/),f.beginRe=b(f.begin),f.end||f.endsWithParent||(f.end=/\B|\b/),f.end&&(f.endRe=b(f.end)),f.terminatorEnd=i(f.end)||"",f.endsWithParent&&g.terminatorEnd&&(f.terminatorEnd+=(f.end?"|":"")+g.terminatorEnd)),f.illegal&&(f.illegalRe=b(f.illegal)),f.contains||(f.contains=[]),f.contains=[].concat(...f.contains.map(function(a){var b;return((b="self"===a?f:a).variants&&!b.cachedVariants&&(b.cachedVariants=b.variants.map(function(a){return d(b,{variants:null},a)})),b.cachedVariants)?b.cachedVariants:!function a(b){return!!b&&(b.endsWithParent||a(b.starts))}(b)?Object.isFrozen(b)?d(b):b:d(b,{starts:b.starts?d(b.starts):null})})),f.contains.forEach(function(a){c(a,f)}),f.starts&&c(f.starts,g),f.matcher=function(a){let b=new e;return a.contains.forEach(a=>b.addRule(a.begin,{rule:a,type:"begin"})),a.terminatorEnd&&b.addRule(a.terminatorEnd,{type:"end"}),a.illegal&&b.addRule(a.illegal,{type:"illegal"}),b}(f),f}(a)}(y),A="",B=h||z,K={},M=new t.__emitter(t),N=[];for(let a=B;a!==y;a=a.parent)a.scope&&N.unshift(a.scope);N.forEach(a=>M.openNode(a));let O="",P=0,R=0,T=0,U=!1;try{if(y.__emitTokens)y.__emitTokens(f,M);else{for(B.matcher.considerAll();;){T++,U?U=!1:B.matcher.considerAll(),B.matcher.lastIndex=R;let a=B.matcher.exec(f);if(!a)break;let b=f.substring(R,a.index),c=v(b,a);R=a.index+c}v(f.substring(R))}return M.finalize(),A=M.toHTML(),{language:a,value:A,relevance:P,illegal:!1,_emitter:M,_top:B}}catch(b){if(b.message&&b.message.includes("Illegal"))return{language:a,value:c(f),illegal:!0,relevance:0,_illegalBy:{message:b.message,index:R,context:f.slice(R-100,R+100),mode:b.mode,resultSoFar:A},_emitter:M};if(p)return{language:a,value:c(f),illegal:!1,relevance:0,errorRaised:b,_emitter:M,_top:B};throw b}}function x(a,b){b=b||t.languages||Object.keys(e);let d=function(a){let b={value:c(a),illegal:!1,relevance:0,_top:s,_emitter:new t.__emitter(t)};return b._emitter.addText(a),b}(a),f=b.filter(C).filter(O).map(b=>w(b,a,!1));f.unshift(d);let[g,h]=f.sort((a,b)=>{if(a.relevance!==b.relevance)return b.relevance-a.relevance;if(a.language&&b.language){if(C(a.language).supersetOf===b.language)return 1;else if(C(b.language).supersetOf===a.language)return -1}return 0});return g.secondBest=h,g}function y(a){let b=function(a){let b=a.className+" ";b+=a.parentNode?a.parentNode.className:"";let c=t.languageDetectRe.exec(b);if(c){let b=C(c[1]);return b||(M(r.replace("{}",c[1])),M("Falling back to no-highlight mode for this block.",a)),b?c[1]:"no-highlight"}return b.split(/\s+/).find(a=>u(a)||C(a))}(a);if(u(b))return;if(P("before:highlightElement",{el:a,language:b}),a.dataset.highlighted)return void console.log("Element previously highlighted. To highlight again, first unset `dataset.highlighted`.",a);if(a.children.length>0&&(t.ignoreUnescapedHTML||(console.warn("One of your code blocks includes unescaped HTML. This is a potentially serious security risk."),console.warn("https://github.com/highlightjs/highlight.js/wiki/security"),console.warn("The element with unescaped HTML:"),console.warn(a)),t.throwUnescapedHTML))throw new R("One of your code blocks includes unescaped HTML.",a.innerHTML);let c=a.textContent,d=b?v(c,{language:b,ignoreIllegals:!0}):x(c);a.innerHTML=d.value,a.dataset.highlighted="yes";var e=d.language;let g=b&&f[b]||e;a.classList.add("hljs"),a.classList.add(`language-${g}`),a.result={language:d.language,re:d.relevance,relevance:d.relevance},d.secondBest&&(a.secondBest={language:d.secondBest.language,relevance:d.secondBest.relevance}),P("after:highlightElement",{el:a,result:d,text:c})}let z=!1;function A(){if("loading"===document.readyState){z||window.addEventListener("DOMContentLoaded",function(){A()},!1),z=!0;return}document.querySelectorAll(t.cssSelector).forEach(y)}function C(a){return e[a=(a||"").toLowerCase()]||e[f[a]]}function K(a,{languageName:b}){"string"==typeof a&&(a=[a]),a.forEach(a=>{f[a.toLowerCase()]=b})}function O(a){let b=C(a);return b&&!b.disableAutodetect}function P(a,b){g.forEach(function(c){c[a]&&c[a](b)})}for(let b in Object.assign(a,{highlight:v,highlightAuto:x,highlightAll:A,highlightElement:y,highlightBlock:function(a){return N("10.7.0","highlightBlock will be removed entirely in v12.0"),N("10.7.0","Please use highlightElement now."),y(a)},configure:function(a){t=d(t,a)},initHighlighting:()=>{A(),N("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")},initHighlightingOnLoad:function(){A(),N("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")},registerLanguage:function(b,c){let d=null;try{d=c(a)}catch(a){if(L("Language definition for '{}' could not be registered.".replace("{}",b)),p)L(a);else throw a;d=s}d.name||(d.name=b),e[b]=d,d.rawDefinition=c.bind(null,a),d.aliases&&K(d.aliases,{languageName:b})},unregisterLanguage:function(a){for(let b of(delete e[a],Object.keys(f)))f[b]===a&&delete f[b]},listLanguages:function(){return Object.keys(e)},getLanguage:C,registerAliases:K,autoDetection:O,inherit:d,addPlugin:function(a){var b;(b=a)["before:highlightBlock"]&&!b["before:highlightElement"]&&(b["before:highlightElement"]=a=>{b["before:highlightBlock"](Object.assign({block:a.el},a))}),b["after:highlightBlock"]&&!b["after:highlightElement"]&&(b["after:highlightElement"]=a=>{b["after:highlightBlock"](Object.assign({block:a.el},a))}),g.push(a)},removePlugin:function(a){let b=g.indexOf(a);-1!==b&&g.splice(b,1)}}),a.debugMode=function(){p=!1},a.safeMode=function(){p=!0},a.versionString="11.11.1",a.regex={concat:m,lookahead:j,either:n,optional:l,anyNumberOfTimes:k},B)"object"==typeof B[b]&&function a(b){return b instanceof Map?b.clear=b.delete=b.set=function(){throw Error("map is read-only")}:b instanceof Set&&(b.add=b.clear=b.delete=function(){throw Error("set is read-only")}),Object.freeze(b),Object.getOwnPropertyNames(b).forEach(c=>{let d=b[c],e=typeof d;"object"!==e&&"function"!==e||Object.isFrozen(d)||a(d)}),b}(B[b]);return Object.assign(a,B),a},U=T({});U.newInstance=()=>T({}),a.exports=U,U.HighlightJS=U,U.default=U}};