(()=>{var a={};a.id=7128,a.ids=[7128],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15168:()=>{},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44544:()=>{},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51702:(a,b,c)=>{"use strict";c.d(b,{z:()=>e});var d=c(96330);let e=global.prisma??new d.PrismaClient},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},85394:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>I,patchFetch:()=>H,routeModule:()=>D,serverHooks:()=>G,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>F});var d={};c.r(d),c.d(d,{GET:()=>C,POST:()=>B});var e=c(27372),f=c(83729),g=c(17064),h=c(20762),i=c(19096),j=c(261),k=c(78006),l=c(62876),m=c(44724),n=c(60167),o=c(88305),p=c(28675),q=c(25133),r=c(79082),s=c(86439),t=c(40904),u=c(93701),v=c(51702);let w={};var x=c(17293);let y=x.Ik({postId:x.ai().int().positive(),author:x.Yj().min(1).max(50),email:x.Yj().email().optional().or(x.eu("")),content:x.Yj().min(1).max(1e3)}),z=["垃圾","广告","spam","色情"];function A(a){return z.some(b=>a.toLowerCase().includes(b.toLowerCase()))}async function B(a){try{let b=function(a){let b=a.headers.get("x-forwarded-for"),c=a.headers.get("x-real-ip");return b?b.split(",")[0].trim():c||"unknown"}(a),c=function(a,b){let c=Date.now(),{windowMs:d,maxRequests:e}=b;w[a]&&c>w[a].resetTime&&delete w[a],w[a]||(w[a]={count:0,resetTime:c+d});let f=w[a];return f.count++,{allowed:f.count<=e,remaining:Math.max(0,e-f.count),resetTime:f.resetTime}}(`comment:${b}`,{windowMs:9e5,maxRequests:5});if(!c.allowed)return u.NextResponse.json({success:!1,error:{code:"RATE_LIMIT_EXCEEDED",message:`评论过于频繁，请${Math.ceil((c.resetTime-Date.now())/6e4)}分钟后再试`}},{status:429});let d=await a.json(),e=y.safeParse(d);if(!e.success)return u.NextResponse.json({success:!1,error:{code:"VALIDATION_ERROR",message:"请检查输入内容",details:e.error.issues}},{status:400});let{postId:f,author:g,email:h,content:i}=e.data;if(!await v.z.post.findUnique({where:{id:f,status:"PUBLISHED"}}))return u.NextResponse.json({success:!1,error:{code:"POST_NOT_FOUND",message:"文章不存在或未发布"}},{status:404});if(A(i)||A(g))return u.NextResponse.json({success:!1,error:{code:"CONTENT_REJECTED",message:"评论内容包含敏感词，请修改后重试"}},{status:400});let j=await v.z.comment.create({data:{postId:f,author:g,email:h||null,content:i,status:"PENDING",ipHash:b,createdAt:new Date}});return u.NextResponse.json({success:!0,data:{id:j.id,author:j.author,content:j.content,createdAt:j.createdAt,status:j.status},message:"评论提交成功，等待审核"})}catch(a){return console.error("Comment submission error:",a),u.NextResponse.json({success:!1,error:{code:"INTERNAL_ERROR",message:"提交失败，请稍后重试"}},{status:500})}}async function C(a){try{let{searchParams:b}=new URL(a.url),c=b.get("postId");if(!c)return u.NextResponse.json({success:!1,error:{code:"MISSING_POST_ID",message:"缺少文章ID"}},{status:400});let d=await v.z.comment.findMany({where:{postId:parseInt(c),status:"APPROVED"},orderBy:{createdAt:"desc"},select:{id:!0,author:!0,content:!0,createdAt:!0}});return u.NextResponse.json({success:!0,data:d})}catch(a){return console.error("Get comments error:",a),u.NextResponse.json({success:!1,error:{code:"INTERNAL_ERROR",message:"获取评论失败"}},{status:500})}}let D=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/comments/route",pathname:"/api/comments",filename:"route",bundlePath:"app/api/comments/route"},distDir:".next",projectDir:"",resolvedPagePath:"D:\\YQ_SOURCE_CODE\\MY_PROJ\\personal-blog\\app\\api\\comments\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:E,workUnitAsyncStorage:F,serverHooks:G}=D;function H(){return(0,g.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:F})}async function I(a,b,c){var d;let e="/api/comments/route";"/index"===e&&(e="/");let g=await D.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,resolvedPathname:C}=g,E=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[C]);if(F&&!x){let a=!!y.routes[C],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||D.isDev||x||(G="/index"===(G=C)?"/":G);let H=!0===D.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>D.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>D.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&A&&B&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await D.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})},z),b}},l=await D.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await D.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96330:a=>{"use strict";a.exports=require("@prisma/client")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[223,7526,7293],()=>b(b.s=85394));module.exports=c})();