(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9627],{663:(e,t,n)=>{"use strict";n.d(t,{CommentSection:()=>v});var s=n(5696),r=n(6060),i=n(5581),a=n(9728),o=n(4264),l=n(1473),c=n(142),d=n(2989),u=n(7855);class h extends r.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=(0,d.s)(e)&&e.offsetWidth||0,s=this.props.sizeRef.current;s.height=t.offsetHeight||0,s.width=t.offsetWidth||0,s.top=t.offsetTop,s.left=t.offsetLeft,s.right=n-s.width-s.left}return null}componentDidUpdate(){}render(){return this.props.children}}function p(e){let{children:t,isPresent:n,anchorX:i,root:a}=e,o=(0,r.useId)(),l=(0,r.useRef)(null),c=(0,r.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:d}=(0,r.useContext)(u.Q);return(0,r.useInsertionEffect)(()=>{let{width:e,height:t,top:s,left:r,right:u}=c.current;if(n||!l.current||!e||!t)return;l.current.dataset.motionPopId=o;let h=document.createElement("style");d&&(h.nonce=d);let p=null!=a?a:document.head;return p.appendChild(h),h.sheet&&h.sheet.insertRule('\n          [data-motion-pop-id="'.concat(o,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===i?"left: ".concat(r):"right: ".concat(u),"px !important;\n            top: ").concat(s,"px !important;\n          }\n        ")),()=>{p.contains(h)&&p.removeChild(h)}},[n]),(0,s.jsx)(h,{isPresent:n,childRef:l,sizeRef:c,children:r.cloneElement(t,{ref:l})})}let m=e=>{let{children:t,initial:n,isPresent:i,onExitComplete:a,custom:l,presenceAffectsLayout:d,mode:u,anchorX:h,root:m}=e,g=(0,o.M)(f),x=(0,r.useId)(),y=!0,b=(0,r.useMemo)(()=>(y=!1,{id:x,initial:n,isPresent:i,custom:l,onExitComplete:e=>{for(let t of(g.set(e,!0),g.values()))if(!t)return;a&&a()},register:e=>(g.set(e,!1),()=>g.delete(e))}),[i,g,a]);return d&&y&&(b={...b}),(0,r.useMemo)(()=>{g.forEach((e,t)=>g.set(t,!1))},[i]),r.useEffect(()=>{i||g.size||!a||a()},[i]),"popLayout"===u&&(t=(0,s.jsx)(p,{isPresent:i,anchorX:h,root:m,children:t})),(0,s.jsx)(c.t.Provider,{value:b,children:t})};function f(){return new Map}var g=n(7353);let x=e=>e.key||"";function y(e){let t=[];return r.Children.forEach(e,e=>{(0,r.isValidElement)(e)&&t.push(e)}),t}let b=e=>{let{children:t,custom:n,initial:i=!0,onExitComplete:c,presenceAffectsLayout:d=!0,mode:u="sync",propagate:h=!1,anchorX:p="left",root:f}=e,[b,v]=(0,g.xQ)(h),j=(0,r.useMemo)(()=>y(t),[t]),N=h&&!b?[]:j.map(x),w=(0,r.useRef)(!0),C=(0,r.useRef)(j),P=(0,o.M)(()=>new Map),[S,E]=(0,r.useState)(j),[k,R]=(0,r.useState)(j);(0,l.E)(()=>{w.current=!1,C.current=j;for(let e=0;e<k.length;e++){let t=x(k[e]);N.includes(t)?P.delete(t):!0!==P.get(t)&&P.set(t,!1)}},[k,N.length,N.join("-")]);let M=[];if(j!==S){let e=[...j];for(let t=0;t<k.length;t++){let n=k[t],s=x(n);N.includes(s)||(e.splice(t,0,n),M.push(n))}return"wait"===u&&M.length&&(e=M),R(y(e)),E(j),null}let{forceRender:_}=(0,r.useContext)(a.L);return(0,s.jsx)(s.Fragment,{children:k.map(e=>{let t=x(e),r=(!h||!!b)&&(j===k||N.includes(t));return(0,s.jsx)(m,{isPresent:r,initial:(!w.current||!!i)&&void 0,custom:n,presenceAffectsLayout:d,mode:u,root:f,onExitComplete:r?void 0:()=>{if(!P.has(t))return;P.set(t,!0);let e=!0;P.forEach(t=>{t||(e=!1)}),e&&(null==_||_(),R(C.current),h&&(null==v||v()),c&&c())},anchorX:p,children:e},t)})})};function v(e){let{postId:t}=e,[n,a]=(0,r.useState)([]),[o,l]=(0,r.useState)(!0),[c,d]=(0,r.useState)(!1),[u,h]=(0,r.useState)(!1),[p,m]=(0,r.useState)(""),[f,g]=(0,r.useState)(""),[x,y]=(0,r.useState)(""),[v,j]=(0,r.useState)(""),[N,w]=(0,r.useState)("");async function C(){try{let e=await fetch("/api/comments?postId=".concat(t)),n=await e.json();n.success&&a(n.data)}catch(e){console.error("Failed to fetch comments:",e)}finally{l(!1)}}async function P(e){e.preventDefault(),d(!0),w(""),j("");try{let e=await fetch("/api/comments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({postId:t,author:p.trim(),email:f.trim()||void 0,content:x.trim()})}),s=await e.json();if(s.success)j(s.message||"评论提交成功，等待审核"),m(""),g(""),y(""),h(!1);else{var n;w((null==(n=s.error)?void 0:n.message)||"提交失败")}}catch(e){w("网络错误，请稍后重试")}finally{d(!1)}}(0,r.useEffect)(()=>{C()},[t]);let S={hidden:{opacity:0,y:20},visible:{opacity:1,y:0}};return(0,s.jsxs)(i.P.section,{className:"mt-12 border-t pt-8",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:"visible",children:[(0,s.jsxs)(i.P.div,{variants:S,className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("h3",{className:"text-xl font-semibold",children:["评论 ",n.length>0&&"(".concat(n.length,")")]}),(0,s.jsx)("button",{onClick:()=>h(!u),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:u?"取消":"发表评论"})]}),(0,s.jsxs)(b,{children:[v&&(0,s.jsx)(i.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"mb-4 p-3 bg-green-100 text-green-800 rounded-lg",children:v}),N&&(0,s.jsx)(i.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"mb-4 p-3 bg-red-100 text-red-800 rounded-lg",children:N})]}),(0,s.jsx)(b,{children:u&&(0,s.jsxs)(i.P.form,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},onSubmit:P,className:"mb-8 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,s.jsx)("input",{type:"text",placeholder:"昵称 *",value:p,onChange:e=>m(e.target.value),required:!0,className:"px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,s.jsx)("input",{type:"email",placeholder:"邮箱（可选）",value:f,onChange:e=>g(e.target.value),className:"px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,s.jsx)("textarea",{placeholder:"写下你的评论...",value:x,onChange:e=>y(e.target.value),required:!0,rows:4,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent mb-4"}),(0,s.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,s.jsx)("button",{type:"button",onClick:()=>h(!1),className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"取消"}),(0,s.jsx)("button",{type:"submit",disabled:c||!p.trim()||!x.trim(),className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:c?"提交中...":"发表评论"})]})]})}),(0,s.jsx)("div",{className:"space-y-4",children:o?(0,s.jsx)(i.P.div,{variants:S,className:"text-center py-8 text-gray-500",children:"加载中..."}):0===n.length?(0,s.jsx)(i.P.div,{variants:S,className:"text-center py-8 text-gray-500",children:"暂无评论，来发表第一条评论吧！"}):n.map((e,t)=>(0,s.jsxs)(i.P.div,{variants:S,custom:t,className:"p-4 border rounded-lg bg-white dark:bg-gray-900 hover:shadow-md transition-shadow",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("span",{className:"font-medium text-gray-900 dark:text-gray-100",children:e.author}),(0,s.jsx)("time",{className:"text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})})]}),(0,s.jsx)("p",{className:"text-gray-700 dark:text-gray-300 whitespace-pre-wrap",children:e.content})]},e.id))})]})}},6465:(e,t,n)=>{Promise.resolve().then(n.bind(n,663))}},e=>{e.O(0,[5581,9571,1266,7358],()=>e(e.s=6465)),_N_E=e.O()}]);