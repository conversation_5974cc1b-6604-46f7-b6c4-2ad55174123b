hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@esbuild/aix-ppc64@0.25.8':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.8':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.8':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.8':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.8':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.8':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.8':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.8':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.8':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.8':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.8':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.8':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.8':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.8':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.8':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.8':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.8':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.8':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.8':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.8':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.8':
    '@esbuild/openbsd-x64': private
  '@esbuild/openharmony-arm64@0.25.8':
    '@esbuild/openharmony-arm64': private
  '@esbuild/sunos-x64@0.25.8':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.8':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.8':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.8':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.33.0(jiti@2.5.1))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.21.0':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.3.1':
    '@eslint/config-helpers': private
  '@eslint/core@0.15.2':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/js@9.33.0':
    '@eslint/js': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.5':
    '@eslint/plugin-kit': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@img/sharp-darwin-arm64@0.34.3':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.34.3':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.2.0':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.2.0':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.2.0':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.2.0':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-ppc64@1.2.0':
    '@img/sharp-libvips-linux-ppc64': private
  '@img/sharp-libvips-linux-s390x@1.2.0':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.2.0':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.2.0':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.2.0':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.34.3':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.34.3':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-ppc64@0.34.3':
    '@img/sharp-linux-ppc64': private
  '@img/sharp-linux-s390x@0.34.3':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.34.3':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.34.3':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.34.3':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.34.3':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-arm64@0.34.3':
    '@img/sharp-win32-arm64': private
  '@img/sharp-win32-ia32@0.34.3':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.34.3':
    '@img/sharp-win32-x64': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@next/env@15.4.6':
    '@next/env': private
  '@next/swc-darwin-arm64@15.4.6':
    '@next/swc-darwin-arm64': private
  '@next/swc-darwin-x64@15.4.6':
    '@next/swc-darwin-x64': private
  '@next/swc-linux-arm64-gnu@15.4.6':
    '@next/swc-linux-arm64-gnu': private
  '@next/swc-linux-arm64-musl@15.4.6':
    '@next/swc-linux-arm64-musl': private
  '@next/swc-linux-x64-gnu@15.4.6':
    '@next/swc-linux-x64-gnu': private
  '@next/swc-linux-x64-musl@15.4.6':
    '@next/swc-linux-x64-musl': private
  '@next/swc-win32-arm64-msvc@15.4.6':
    '@next/swc-win32-arm64-msvc': private
  '@next/swc-win32-x64-msvc@15.4.6':
    '@next/swc-win32-x64-msvc': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@prisma/config@6.13.0':
    '@prisma/config': private
  '@prisma/debug@6.13.0':
    '@prisma/debug': private
  '@prisma/engines-version@6.13.0-35.361e86d0ea4987e9f53a565309b3eed797a6bcbd':
    '@prisma/engines-version': private
  '@prisma/engines@6.13.0':
    '@prisma/engines': private
  '@prisma/fetch-engine@6.13.0':
    '@prisma/fetch-engine': private
  '@prisma/get-platform@6.13.0':
    '@prisma/get-platform': private
  '@standard-schema/spec@1.0.0':
    '@standard-schema/spec': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@types/body-parser@1.19.6':
    '@types/body-parser': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/express-serve-static-core@5.0.7':
    '@types/express-serve-static-core': private
  '@types/express@5.0.3':
    '@types/express': private
  '@types/hast@3.0.4':
    '@types/hast': private
  '@types/http-errors@2.0.5':
    '@types/http-errors': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/normalize-package-data@2.4.4':
    '@types/normalize-package-data': private
  '@types/qs@6.14.0':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/readdir-glob@1.1.5':
    '@types/readdir-glob': private
  '@types/send@0.17.5':
    '@types/send': private
  '@types/serve-static@1.15.8':
    '@types/serve-static': private
  '@types/unist@3.0.3':
    '@types/unist': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  abort-controller@3.0.0:
    abort-controller: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  append-field@1.0.0:
    append-field: private
  archiver-utils@5.0.2:
    archiver-utils: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  async@3.2.6:
    async: private
  atomic-sleep@1.0.0:
    atomic-sleep: private
  b4a@1.6.7:
    b4a: private
  bail@2.0.2:
    bail: private
  balanced-match@1.0.2:
    balanced-match: private
  bare-events@2.6.1:
    bare-events: private
  base64-js@1.5.1:
    base64-js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.2:
    browserslist: private
  buffer-crc32@1.0.0:
    buffer-crc32: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@6.0.3:
    buffer: private
  busboy@1.6.0:
    busboy: private
  c12@3.1.0:
    c12: private
  callsites@3.1.0:
    callsites: private
  camelcase-css@2.0.1:
    camelcase-css: private
  caniuse-lite@1.0.30001734:
    caniuse-lite: private
  ccount@2.0.1:
    ccount: private
  chalk@4.1.2:
    chalk: private
  character-entities-html4@2.1.0:
    character-entities-html4: private
  character-entities-legacy@3.0.0:
    character-entities-legacy: private
  character-entities@2.0.2:
    character-entities: private
  chokidar@3.6.0:
    chokidar: private
  citty@0.1.6:
    citty: private
  client-only@0.0.1:
    client-only: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  comma-separated-tokens@2.0.3:
    comma-separated-tokens: private
  commander@4.1.1:
    commander: private
  compress-commons@6.0.2:
    compress-commons: private
  concat-map@0.0.1:
    concat-map: private
  concat-stream@2.0.0:
    concat-stream: private
  confbox@0.2.2:
    confbox: private
  consola@3.4.2:
    consola: private
  core-util-is@1.0.3:
    core-util-is: private
  crc-32@1.2.2:
    crc-32: private
  crc32-stream@6.0.0:
    crc32-stream: private
  cross-spawn@7.0.6:
    cross-spawn: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  debug@4.4.1:
    debug: private
  decode-named-character-reference@1.2.0:
    decode-named-character-reference: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge-ts@7.1.5:
    deepmerge-ts: private
  defu@6.1.4:
    defu: private
  dequal@2.0.3:
    dequal: private
  destr@2.0.5:
    destr: private
  detect-libc@2.0.4:
    detect-libc: private
  devlop@1.1.0:
    devlop: private
  didyoumean@1.2.2:
    didyoumean: private
  dlv@1.1.3:
    dlv: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  effect@3.16.12:
    effect: private
  electron-to-chromium@1.5.200:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  entities@6.0.1:
    entities: private
  esbuild@0.25.8:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: private
  espree@10.4.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  event-target-shim@5.0.1:
    event-target-shim: private
  events@3.3.0:
    events: private
  exsolve@1.0.7:
    exsolve: private
  extend@3.0.2:
    extend: private
  fast-check@3.23.2:
    fast-check: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-fifo@1.3.2:
    fast-fifo: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-redact@3.5.0:
    fast-redact: private
  fastq@1.19.1:
    fastq: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up-simple@1.0.1:
    find-up-simple: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  foreground-child@3.3.1:
    foreground-child: private
  fraction.js@4.3.7:
    fraction.js: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  giget@2.0.0:
    giget: private
  github-slugger@2.0.0:
    github-slugger: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@10.4.5:
    glob: private
  globals@14.0.0:
    globals: private
  graceful-fs@4.2.11:
    graceful-fs: private
  has-flag@4.0.0:
    has-flag: private
  hasown@2.0.2:
    hasown: private
  hast-util-from-html@2.0.3:
    hast-util-from-html: private
  hast-util-from-parse5@8.0.3:
    hast-util-from-parse5: private
  hast-util-heading-rank@3.0.0:
    hast-util-heading-rank: private
  hast-util-is-element@3.0.0:
    hast-util-is-element: private
  hast-util-parse-selector@4.0.0:
    hast-util-parse-selector: private
  hast-util-raw@9.1.0:
    hast-util-raw: private
  hast-util-to-html@9.0.5:
    hast-util-to-html: private
  hast-util-to-parse5@8.0.0:
    hast-util-to-parse5: private
  hast-util-to-string@3.0.1:
    hast-util-to-string: private
  hast-util-to-text@4.0.2:
    hast-util-to-text: private
  hast-util-whitespace@3.0.0:
    hast-util-whitespace: private
  hastscript@9.0.1:
    hastscript: private
  highlight.js@11.11.1:
    highlight.js: private
  hosted-git-info@7.0.2:
    hosted-git-info: private
  html-void-elements@3.0.0:
    html-void-elements: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  index-to-position@1.1.0:
    index-to-position: private
  inherits@2.0.4:
    inherits: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-core-module@2.16.1:
    is-core-module: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-stream@2.0.1:
    is-stream: private
  isarray@1.0.0:
    isarray: private
  isexe@2.0.0:
    isexe: private
  jackspeak@3.4.3:
    jackspeak: private
  jiti@2.5.1:
    jiti: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  keyv@4.5.4:
    keyv: private
  lazystream@1.0.1:
    lazystream: private
  levn@0.4.1:
    levn: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  locate-path@6.0.0:
    locate-path: private
  lodash.castarray@4.4.0:
    lodash.castarray: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash@4.17.21:
    lodash: private
  longest-streak@3.1.0:
    longest-streak: private
  lowlight@3.3.0:
    lowlight: private
  lru-cache@10.4.3:
    lru-cache: private
  markdown-table@3.0.4:
    markdown-table: private
  mdast-util-find-and-replace@3.0.2:
    mdast-util-find-and-replace: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-gfm-autolink-literal@2.0.1:
    mdast-util-gfm-autolink-literal: private
  mdast-util-gfm-footnote@2.1.0:
    mdast-util-gfm-footnote: private
  mdast-util-gfm-strikethrough@2.0.0:
    mdast-util-gfm-strikethrough: private
  mdast-util-gfm-table@2.0.0:
    mdast-util-gfm-table: private
  mdast-util-gfm-task-list-item@2.0.0:
    mdast-util-gfm-task-list-item: private
  mdast-util-gfm@3.1.0:
    mdast-util-gfm: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-hast@13.2.0:
    mdast-util-to-hast: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@4.0.0:
    mdast-util-to-string: private
  media-typer@0.3.0:
    media-typer: private
  merge2@1.4.1:
    merge2: private
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: private
  micromark-extension-gfm-autolink-literal@2.1.0:
    micromark-extension-gfm-autolink-literal: private
  micromark-extension-gfm-footnote@2.1.0:
    micromark-extension-gfm-footnote: private
  micromark-extension-gfm-strikethrough@2.1.0:
    micromark-extension-gfm-strikethrough: private
  micromark-extension-gfm-table@2.1.1:
    micromark-extension-gfm-table: private
  micromark-extension-gfm-tagfilter@2.0.0:
    micromark-extension-gfm-tagfilter: private
  micromark-extension-gfm-task-list-item@2.1.0:
    micromark-extension-gfm-task-list-item: private
  micromark-extension-gfm@3.0.0:
    micromark-extension-gfm: private
  micromark-factory-destination@2.0.1:
    micromark-factory-destination: private
  micromark-factory-label@2.0.1:
    micromark-factory-label: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@2.0.1:
    micromark-factory-title: private
  micromark-factory-whitespace@2.0.1:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@2.0.2:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@2.0.1:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-html-tag-name@2.0.1:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@2.0.1:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@2.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.2:
    micromark-util-types: private
  micromark@4.0.2:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.54.0:
    mime-db: private
  mini-svg-data-uri@1.4.4:
    mini-svg-data-uri: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  mkdirp@0.5.6:
    mkdirp: private
  motion-dom@12.23.12:
    motion-dom: private
  motion-utils@12.23.6:
    motion-utils: private
  ms@2.1.3:
    ms: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  node-fetch-native@1.6.7:
    node-fetch-native: private
  node-releases@2.0.19:
    node-releases: private
  normalize-package-data@6.0.2:
    normalize-package-data: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  nypm@0.6.1:
    nypm: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  ohash@2.0.11:
    ohash: private
  on-exit-leak-free@2.1.2:
    on-exit-leak-free: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@8.3.0:
    parse-json: private
  parse5@7.3.0:
    parse5: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  pathe@2.0.3:
    pathe: private
  perfect-debounce@1.0.0:
    perfect-debounce: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pify@2.3.0:
    pify: private
  pino-abstract-transport@2.0.0:
    pino-abstract-transport: private
  pino-std-serializers@7.0.0:
    pino-std-serializers: private
  pirates@4.0.7:
    pirates: private
  pkg-types@2.2.0:
    pkg-types: private
  postcss-import@15.1.0(postcss@8.5.6):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.5.6):
    postcss-js: private
  postcss-load-config@4.0.2(postcss@8.5.6):
    postcss-load-config: private
  postcss-nested@6.2.0(postcss@8.5.6):
    postcss-nested: private
  postcss-selector-parser@6.0.10:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  prelude-ls@1.2.1:
    prelude-ls: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  process-warning@5.0.0:
    process-warning: private
  process@0.11.10:
    process: private
  property-information@7.1.0:
    property-information: private
  punycode@2.3.1:
    punycode: private
  pure-rand@6.1.0:
    pure-rand: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quick-format-unescaped@4.0.4:
    quick-format-unescaped: private
  rc9@2.1.2:
    rc9: private
  read-cache@1.0.0:
    read-cache: private
  read-package-up@11.0.0:
    read-package-up: private
  read-pkg@9.0.1:
    read-pkg: private
  readable-stream@4.7.0:
    readable-stream: private
  readdir-glob@1.1.3:
    readdir-glob: private
  readdirp@3.6.0:
    readdirp: private
  real-require@0.2.0:
    real-require: private
  rehype-parse@9.0.1:
    rehype-parse: private
  remark-stringify@11.0.0:
    remark-stringify: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-stable-stringify@2.5.0:
    safe-stable-stringify: private
  scheduler@0.26.0:
    scheduler: private
  semver@7.7.2:
    semver: private
  sharp@0.34.3:
    sharp: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  sonic-boom@4.2.0:
    sonic-boom: private
  source-map-js@1.2.1:
    source-map-js: private
  space-separated-tokens@2.0.2:
    space-separated-tokens: private
  spdx-correct@3.2.0:
    spdx-correct: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@3.0.1:
    spdx-expression-parse: private
  spdx-license-ids@3.0.22:
    spdx-license-ids: private
  split2@4.2.0:
    split2: private
  streamsearch@1.1.0:
    streamsearch: private
  streamx@2.22.1:
    streamx: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@5.1.2:
    string-width: private
  string_decoder@1.3.0:
    string_decoder: private
  stringify-entities@4.0.4:
    stringify-entities: private
  strip-ansi@6.0.1:
    strip-ansi-cjs: private
  strip-ansi@7.1.0:
    strip-ansi: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  styled-jsx@5.1.6(react@19.1.1):
    styled-jsx: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  tar-stream@3.1.7:
    tar-stream: private
  text-decoder@1.2.3:
    text-decoder: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  thread-stream@3.1.0:
    thread-stream: private
  tinyexec@1.0.1:
    tinyexec: private
  to-regex-range@5.0.1:
    to-regex-range: private
  trim-lines@3.0.1:
    trim-lines: private
  trough@2.2.0:
    trough: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  tslib@2.8.1:
    tslib: private
  type-check@0.4.0:
    type-check: private
  type-fest@4.41.0:
    type-fest: private
  type-is@1.6.18:
    type-is: private
  typedarray@0.0.6:
    typedarray: private
  undici-types@7.10.0:
    undici-types: private
  unicorn-magic@0.1.0:
    unicorn-magic: private
  unist-util-find-after@5.0.0:
    unist-util-find-after: private
  unist-util-is@6.0.0:
    unist-util-is: private
  unist-util-position@5.0.0:
    unist-util-position: private
  unist-util-stringify-position@4.0.0:
    unist-util-stringify-position: private
  unist-util-visit-parents@6.0.1:
    unist-util-visit-parents: private
  unist-util-visit@5.0.0:
    unist-util-visit: private
  update-browserslist-db@1.1.3(browserslist@4.25.2):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  util-deprecate@1.0.2:
    util-deprecate: private
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: private
  vfile-location@5.0.3:
    vfile-location: private
  vfile-message@4.0.3:
    vfile-message: private
  vfile@6.0.3:
    vfile: private
  web-namespaces@2.0.1:
    web-namespaces: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
  xtend@4.0.2:
    xtend: private
  yaml@2.8.1:
    yaml: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zip-stream@6.0.1:
    zip-stream: private
  zwitch@2.0.4:
    zwitch: private
ignoredBuilds:
  - esbuild
  - sharp
  - '@prisma/engines'
  - prisma
  - '@prisma/client'
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Tue, 12 Aug 2025 03:02:18 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped:
  - '@emnapi/runtime@1.4.5'
  - '@esbuild/aix-ppc64@0.25.8'
  - '@esbuild/android-arm64@0.25.8'
  - '@esbuild/android-arm@0.25.8'
  - '@esbuild/android-x64@0.25.8'
  - '@esbuild/darwin-arm64@0.25.8'
  - '@esbuild/darwin-x64@0.25.8'
  - '@esbuild/freebsd-arm64@0.25.8'
  - '@esbuild/freebsd-x64@0.25.8'
  - '@esbuild/linux-arm64@0.25.8'
  - '@esbuild/linux-arm@0.25.8'
  - '@esbuild/linux-ia32@0.25.8'
  - '@esbuild/linux-loong64@0.25.8'
  - '@esbuild/linux-mips64el@0.25.8'
  - '@esbuild/linux-ppc64@0.25.8'
  - '@esbuild/linux-riscv64@0.25.8'
  - '@esbuild/linux-s390x@0.25.8'
  - '@esbuild/linux-x64@0.25.8'
  - '@esbuild/netbsd-arm64@0.25.8'
  - '@esbuild/netbsd-x64@0.25.8'
  - '@esbuild/openbsd-arm64@0.25.8'
  - '@esbuild/openbsd-x64@0.25.8'
  - '@esbuild/openharmony-arm64@0.25.8'
  - '@esbuild/sunos-x64@0.25.8'
  - '@esbuild/win32-arm64@0.25.8'
  - '@esbuild/win32-ia32@0.25.8'
  - '@img/sharp-darwin-arm64@0.34.3'
  - '@img/sharp-darwin-x64@0.34.3'
  - '@img/sharp-libvips-darwin-arm64@1.2.0'
  - '@img/sharp-libvips-darwin-x64@1.2.0'
  - '@img/sharp-libvips-linux-arm64@1.2.0'
  - '@img/sharp-libvips-linux-arm@1.2.0'
  - '@img/sharp-libvips-linux-ppc64@1.2.0'
  - '@img/sharp-libvips-linux-s390x@1.2.0'
  - '@img/sharp-libvips-linux-x64@1.2.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.2.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.2.0'
  - '@img/sharp-linux-arm64@0.34.3'
  - '@img/sharp-linux-arm@0.34.3'
  - '@img/sharp-linux-ppc64@0.34.3'
  - '@img/sharp-linux-s390x@0.34.3'
  - '@img/sharp-linux-x64@0.34.3'
  - '@img/sharp-linuxmusl-arm64@0.34.3'
  - '@img/sharp-linuxmusl-x64@0.34.3'
  - '@img/sharp-wasm32@0.34.3'
  - '@img/sharp-win32-arm64@0.34.3'
  - '@img/sharp-win32-ia32@0.34.3'
  - '@next/swc-darwin-arm64@15.4.6'
  - '@next/swc-darwin-x64@15.4.6'
  - '@next/swc-linux-arm64-gnu@15.4.6'
  - '@next/swc-linux-arm64-musl@15.4.6'
  - '@next/swc-linux-x64-gnu@15.4.6'
  - '@next/swc-linux-x64-musl@15.4.6'
  - '@next/swc-win32-arm64-msvc@15.4.6'
  - fsevents@2.3.3
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\YQ_SOURCE_CODE\MY_PROJ\personal-blog\node_modules\.pnpm
virtualStoreDirMaxLength: 60
