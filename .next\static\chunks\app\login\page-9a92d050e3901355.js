(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{259:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>n});var t=s(5696),l=s(6060);function n(){let[e,a]=(0,l.useState)(""),[s,n]=(0,l.useState)(""),[r,i]=(0,l.useState)("");async function o(a){var t;a.preventDefault(),i("");let l=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:e,password:s})}),n=await l.json();n.success?window.location.href="/admin":i((null==(t=n.error)?void 0:t.message)||"登录失败")}return(0,t.jsx)("main",{className:"min-h-screen flex items-center justify-center p-6",children:(0,t.jsxs)("form",{onSubmit:o,className:"w-full max-w-sm space-y-3",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"登录"}),r&&(0,t.jsx)("div",{className:"text-red-600 text-sm",children:r}),(0,t.jsx)("input",{className:"w-full border rounded px-3 py-2",placeholder:"用户名",value:e,onChange:e=>a(e.target.value)}),(0,t.jsx)("input",{type:"password",className:"w-full border rounded px-3 py-2",placeholder:"密码",value:s,onChange:e=>n(e.target.value)}),(0,t.jsx)("button",{className:"w-full bg-black text-white py-2 rounded",children:"登录"})]})})}},7695:(e,a,s)=>{Promise.resolve().then(s.bind(s,259))}},e=>{e.O(0,[9571,1266,7358],()=>e(e.s=7695)),_N_E=e.O()}]);