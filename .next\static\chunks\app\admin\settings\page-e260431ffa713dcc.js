(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7122],{75:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var l=t(5696),a=t(6060);function d(){let[e,s]=(0,a.useState)({title:"",description:"",keywords:"",author:"",baseUrl:"",aboutContent:""}),[t,d]=(0,a.useState)(null),[r,n]=(0,a.useState)([]),[c,o]=(0,a.useState)([]),[x,m]=(0,a.useState)(!0),[u,g]=(0,a.useState)(!1),[h,b]=(0,a.useState)(""),[p,j]=(0,a.useState)("settings");async function v(){try{let e=await fetch("/api/admin/settings"),t=await e.json();t.success&&s(t.data)}catch(e){console.error("Failed to fetch settings:",e)}finally{m(!1)}}async function y(){try{let e=await fetch("/api/admin/settings",{method:"PUT"}),s=await e.json();s.success&&(d(s.data.stats),n(s.data.recentPosts),o(s.data.recentComments))}catch(e){console.error("Failed to fetch stats:",e)}}async function N(s){s.preventDefault(),g(!0);try{let s=await fetch("/api/admin/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),l=await s.json();if(l.success)b(l.message),setTimeout(()=>b(""),3e3);else{var t;alert((null==(t=l.error)?void 0:t.message)||"保存失败")}}catch(e){console.error("Save settings error:",e),alert("保存失败")}finally{g(!1)}}function f(e,t){s(s=>({...s,[e]:t}))}return((0,a.useEffect)(()=>{v(),y()},[]),x)?(0,l.jsx)("div",{className:"text-center py-8",children:"加载中..."}):(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold",children:"站点设置"}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)("button",{onClick:()=>j("settings"),className:"px-4 py-2 rounded-lg ".concat("settings"===p?"bg-blue-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:"基本设置"}),(0,l.jsx)("button",{onClick:()=>j("stats"),className:"px-4 py-2 rounded-lg ".concat("stats"===p?"bg-blue-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:"统计信息"}),(0,l.jsx)("button",{onClick:()=>j("backup"),className:"px-4 py-2 rounded-lg ".concat("backup"===p?"bg-blue-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:"备份管理"})]})]}),h&&(0,l.jsx)("div",{className:"p-3 bg-green-100 text-green-800 rounded-lg",children:h}),"settings"===p?(0,l.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-2",children:"站点标题"}),(0,l.jsx)("input",{type:"text",value:e.title,onChange:e=>f("title",e.target.value),className:"w-full px-3 py-2 border rounded-lg",required:!0})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-2",children:"作者"}),(0,l.jsx)("input",{type:"text",value:e.author,onChange:e=>f("author",e.target.value),className:"w-full px-3 py-2 border rounded-lg"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-2",children:"站点描述"}),(0,l.jsx)("textarea",{value:e.description,onChange:e=>f("description",e.target.value),rows:3,className:"w-full px-3 py-2 border rounded-lg",required:!0})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-2",children:"关键词"}),(0,l.jsx)("input",{type:"text",value:e.keywords,onChange:e=>f("keywords",e.target.value),placeholder:"用逗号分隔",className:"w-full px-3 py-2 border rounded-lg"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-2",children:"站点 URL"}),(0,l.jsx)("input",{type:"url",value:e.baseUrl,onChange:e=>f("baseUrl",e.target.value),placeholder:"https://example.com",className:"w-full px-3 py-2 border rounded-lg"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-2",children:"关于页面内容"}),(0,l.jsx)("textarea",{value:e.aboutContent,onChange:e=>f("aboutContent",e.target.value),rows:8,placeholder:"支持 Markdown 格式",className:"w-full px-3 py-2 border rounded-lg"})]}),(0,l.jsx)("div",{className:"flex justify-end",children:(0,l.jsx)("button",{type:"submit",disabled:u,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50",children:u?"保存中...":"保存设置"})})]}):"stats"===p?(0,l.jsxs)("div",{className:"space-y-6",children:[t&&(0,l.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4",children:[(0,l.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:t.totalPosts}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:"总文章数"})]}),(0,l.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-green-600",children:t.publishedPosts}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:"已发布"})]}),(0,l.jsxs)("div",{className:"bg-purple-50 p-4 rounded-lg",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:t.totalComments}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:"总评论数"})]}),(0,l.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:t.pendingComments}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:"待审评论"})]}),(0,l.jsxs)("div",{className:"bg-indigo-50 p-4 rounded-lg",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-indigo-600",children:t.totalCategories}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:"分类数"})]}),(0,l.jsxs)("div",{className:"bg-pink-50 p-4 rounded-lg",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-pink-600",children:t.totalTags}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:"标签数"})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"最近文章"}),(0,l.jsx)("div",{className:"space-y-3",children:r.map(e=>(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"font-medium",children:e.title}),(0,l.jsx)("div",{className:"text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString("zh-CN")})]}),(0,l.jsx)("span",{className:"px-2 py-1 text-xs rounded ".concat("PUBLISHED"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:"PUBLISHED"===e.status?"已发布":"草稿"})]},e.id))})]}),(0,l.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"最近评论"}),(0,l.jsx)("div",{className:"space-y-3",children:c.map(e=>(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"font-medium",children:e.author}),(0,l.jsx)("span",{className:"px-2 py-1 text-xs rounded ".concat("APPROVED"===e.status?"bg-green-100 text-green-800":"REJECTED"===e.status?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"),children:"APPROVED"===e.status?"已通过":"REJECTED"===e.status?"已拒绝":"待审核"})]}),(0,l.jsx)("div",{className:"text-sm text-gray-600 line-clamp-2",children:e.content}),(0,l.jsxs)("div",{className:"text-xs text-gray-500",children:["文章：",e.post.title," • ",new Date(e.createdAt).toLocaleDateString("zh-CN")]})]},e.id))})]})]})]}):(0,l.jsx)(i,{})]})}function i(){let[e,s]=(0,a.useState)(!1),[t,d]=(0,a.useState)(!1),[i,r]=(0,a.useState)("");async function n(){d(!0);try{var e,s,t;let l=await fetch("/api/backup/export");if(l.ok){let t=await l.blob(),a=window.URL.createObjectURL(t),d=document.createElement("a");d.href=a,d.download=(null==(s=l.headers.get("Content-Disposition"))||null==(e=s.split("filename=")[1])?void 0:e.replace(/"/g,""))||"backup.zip",document.body.appendChild(d),d.click(),window.URL.revokeObjectURL(a),document.body.removeChild(d),r("备份导出成功")}else{let e=await l.json();alert((null==(t=e.error)?void 0:t.message)||"导出失败")}}catch(e){console.error("Export error:",e),alert("导出失败")}finally{d(!1),setTimeout(()=>r(""),3e3)}}async function c(e){var t,l;let a=null==(t=e.target.files)?void 0:t[0];if(a){if(!confirm("导入备份将覆盖现有数据，确定要继续吗？")){e.target.value="";return}s(!0);try{let e=new FormData;e.append("backup",a);let s=await fetch("/api/backup/import",{method:"POST",body:e}),t=await s.json();t.success?(r("备份导入成功！导入了 ".concat(t.data.stats.posts," 篇文章、").concat(t.data.stats.comments," 条评论、").concat(t.data.stats.files," 个文件")),setTimeout(()=>window.location.reload(),2e3)):alert((null==(l=t.error)?void 0:l.message)||"导入失败")}catch(e){console.error("Import error:",e),alert("导入失败")}finally{s(!1),e.target.value="",setTimeout(()=>r(""),5e3)}}}return(0,l.jsxs)("div",{className:"space-y-6",children:[i&&(0,l.jsx)("div",{className:"p-3 bg-green-100 text-green-800 rounded-lg",children:i}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{className:"border rounded-lg p-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"导出备份"}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"导出包含所有文章、评论、分类、标签和上传文件的完整备份。"}),(0,l.jsx)("button",{onClick:n,disabled:t,className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50",children:t?"导出中...":"导出备份"})]}),(0,l.jsxs)("div",{className:"border rounded-lg p-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"导入备份"}),(0,l.jsxs)("p",{className:"text-gray-600 mb-4",children:["从备份文件恢复数据。",(0,l.jsx)("span",{className:"text-red-600 font-medium",children:"注意：这将覆盖现有数据！"})]}),(0,l.jsx)("input",{type:"file",accept:".zip",onChange:c,disabled:e,className:"w-full px-3 py-2 border rounded-lg file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"}),e&&(0,l.jsx)("div",{className:"mt-2 text-sm text-blue-600",children:"导入中，请稍候..."})]})]}),(0,l.jsxs)("div",{className:"border rounded-lg p-6 bg-yellow-50",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-2 text-yellow-800",children:"重要提示"}),(0,l.jsxs)("ul",{className:"text-sm text-yellow-700 space-y-1",children:[(0,l.jsx)("li",{children:"• 导出的备份文件包含完整的数据库数据和上传文件"}),(0,l.jsx)("li",{children:"• 导入备份前请确保已备份当前数据"}),(0,l.jsx)("li",{children:"• 导入过程中请勿关闭浏览器或刷新页面"}),(0,l.jsx)("li",{children:"• 建议定期导出备份以防数据丢失"})]})]})]})}},3150:(e,s,t)=>{Promise.resolve().then(t.bind(t,75))}},e=>{e.O(0,[9571,1266,7358],()=>e(e.s=3150)),_N_E=e.O()}]);