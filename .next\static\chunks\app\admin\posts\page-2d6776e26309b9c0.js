(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9358],{4348:(e,t,a)=>{Promise.resolve().then(a.bind(a,7649))},7649:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(5696),n=a(6060);function r(){let[e,t]=(0,n.useState)([]),[a,r]=(0,n.useState)(""),[l,d]=(0,n.useState)(""),[o,i]=(0,n.useState)(""),[c,u]=(0,n.useState)(""),[p,m]=(0,n.useState)(!1),[x,h]=(0,n.useState)(!1),g=(0,n.useRef)(null);async function b(){let s=await fetch("/api/admin/posts",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:a,slug:l,summary:o,contentMd:c})}),n=await s.json();n.success&&(t([n.data,...e]),r(""),d(""),i(""),u(""))}async function y(a){let s=await fetch("/api/admin/posts/".concat(a),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:"PUBLISHED",publishedAt:new Date().toISOString()})}),n=await s.json();n.success&&t(e.map(e=>e.id===a?n.data:e))}async function f(e){h(!0);let t=new FormData;t.append("file",e);try{let s=await fetch("/api/upload",{method:"POST",body:t}),n=await s.json();if(n.success){let t="![".concat(e.name,"](").concat(n.data.url,")");u(e=>e+"\n"+t)}else{var a;alert((null==(a=n.error)?void 0:a.message)||"上传失败")}}catch(e){alert("上传失败")}finally{h(!1)}}function j(e){e.preventDefault();let t=e.dataTransfer.files[0];t&&t.type.startsWith("image/")&&f(t)}function v(e){e.preventDefault()}function N(e){u(t=>t+e)}return(0,n.useEffect)(()=>{fetch("/api/admin/posts").then(e=>e.json()).then(e=>t(e.data||[]))},[]),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"文章管理"}),(0,s.jsxs)("div",{className:"border rounded-lg p-4 space-y-3",children:[(0,s.jsx)("h2",{className:"text-lg font-medium",children:"新建文章"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,s.jsx)("input",{className:"border rounded px-3 py-2",placeholder:"标题",value:a,onChange:e=>r(e.target.value)}),(0,s.jsx)("input",{className:"border rounded px-3 py-2",placeholder:"URL slug",value:l,onChange:e=>d(e.target.value)})]}),(0,s.jsx)("input",{className:"w-full border rounded px-3 py-2",placeholder:"摘要（可选）",value:o,onChange:e=>i(e.target.value)}),(0,s.jsxs)("div",{className:"flex items-center gap-2 p-2 border-b",children:[(0,s.jsx)("button",{onClick:()=>N("**粗体**"),className:"px-2 py-1 text-sm border rounded hover:bg-gray-50",children:"粗体"}),(0,s.jsx)("button",{onClick:()=>N("*斜体*"),className:"px-2 py-1 text-sm border rounded hover:bg-gray-50",children:"斜体"}),(0,s.jsx)("button",{onClick:()=>N("\n## 标题\n"),className:"px-2 py-1 text-sm border rounded hover:bg-gray-50",children:"标题"}),(0,s.jsx)("button",{onClick:()=>N("\n- 列表项\n"),className:"px-2 py-1 text-sm border rounded hover:bg-gray-50",children:"列表"}),(0,s.jsx)("button",{onClick:()=>N("\n```\n代码\n```\n"),className:"px-2 py-1 text-sm border rounded hover:bg-gray-50",children:"代码"}),(0,s.jsx)("button",{onClick:function(){var e;null==(e=g.current)||e.click()},disabled:x,className:"px-2 py-1 text-sm border rounded hover:bg-gray-50 disabled:opacity-50",children:x?"上传中...":"图片"}),(0,s.jsx)("button",{onClick:()=>m(!p),className:"px-2 py-1 text-sm border rounded ".concat(p?"bg-blue-100":"hover:bg-gray-50"),children:p?"编辑":"预览"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-3",style:{gridTemplateColumns:p?"1fr 1fr":"1fr"},children:[!p&&(0,s.jsx)("textarea",{className:"border rounded px-3 py-2 font-mono text-sm min-h-[300px]",placeholder:"Markdown 内容（支持拖拽图片上传）",value:c,onChange:e=>u(e.target.value),onDrop:j,onDragOver:v}),p&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("textarea",{className:"border rounded px-3 py-2 font-mono text-sm min-h-[300px]",placeholder:"Markdown 内容",value:c,onChange:e=>u(e.target.value),onDrop:j,onDragOver:v}),(0,s.jsx)("div",{className:"border rounded px-3 py-2 min-h-[300px] prose prose-sm max-w-none",children:(0,s.jsx)("div",{dangerouslySetInnerHTML:{__html:c.replace(/\n/g,"<br>")}})})]})]}),(0,s.jsx)("button",{onClick:b,className:"bg-black text-white px-4 py-2 rounded",children:"新建文章"}),(0,s.jsx)("input",{ref:g,type:"file",accept:"image/*",onChange:function(e){var t;let a=null==(t=e.target.files)?void 0:t[0];a&&f(a)},className:"hidden"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-lg font-medium mb-3",children:"文章列表"}),(0,s.jsx)("ul",{className:"divide-y border rounded-lg",children:e.map(e=>(0,s.jsxs)("li",{className:"p-3 flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.title}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.slug}),e.summary&&(0,s.jsx)("div",{className:"text-sm text-gray-400 mt-1",children:e.summary})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"px-2 py-1 text-xs rounded ".concat("PUBLISHED"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:"PUBLISHED"===e.status?"已发布":"草稿"}),"PUBLISHED"!==e.status&&(0,s.jsx)("button",{onClick:()=>y(e.id),className:"px-3 py-1 rounded bg-emerald-600 text-white text-sm",children:"发布"})]})]},e.id))})]})]})}}},e=>{e.O(0,[9571,1266,7358],()=>e(e.s=4348)),_N_E=e.O()}]);