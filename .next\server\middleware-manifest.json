{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/admin(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/admin/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "E6deXmL2DioRPQwYES42i", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "jdHCA+V9y9VcWrTEzER3SmZUUh41SknsB47SvfQeBuM=", "__NEXT_PREVIEW_MODE_ID": "f8383c2e9b877f1294d2b65579524d01", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "81ea9470f57d4542c694b5117592b583672f9a44612af037bcf0df9859e8a9bb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b581d397c811705a9ce05c12f0bceb4d72d13ea3fd6298726d1ec0753c1fbe93"}}}, "functions": {}, "sortedMiddleware": ["/"]}