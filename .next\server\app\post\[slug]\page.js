(()=>{var a={};a.id=9627,a.ids=[9627],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5485:(a,b,c)=>{"use strict";c.d(b,{CommentSection:()=>d});let d=(0,c(59990).registerClientReference)(function(){throw Error("Attempted to call CommentSection() from the server but CommentSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\YQ_SOURCE_CODE\\MY_PROJ\\personal-blog\\components\\site\\CommentSection.tsx","CommentSection")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22943:(a,b,c)=>{Promise.resolve().then(c.bind(c,5485))},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},35849:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(37750),e=c(83729),f=c(60167),g=c(19096),h=c(20762),i=c(44724),j=c(68531),k=c(78006),l=c(42484),m=c(41542),n=c(95481),o=c(29153),p=c(25426),q=c(261),r=c(14001),s=c(78078),t=c(26713),u=c(40904),v=c(18665),w=c(53917),x=c(79082),y=c(18902),z=c(61762),A=c(86439),B=c(67854),C=c.n(B),D=c(69830),E=c(19551),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["post",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,49556)),"D:\\YQ_SOURCE_CODE\\MY_PROJ\\personal-blog\\app\\post\\[slug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,12750)),"D:\\YQ_SOURCE_CODE\\MY_PROJ\\personal-blog\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,67854,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,95832,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,37545,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,77548,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["D:\\YQ_SOURCE_CODE\\MY_PROJ\\personal-blog\\app\\post\\[slug]\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/post/[slug]/page",pathname:"/post/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/post/[slug]/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},49556:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>i,generateMetadata:()=>h});var d=c(91374),e=c(51702),f=c(86461),g=c(5485);async function h({params:a}){let{slug:b}=await a,c=await e.z.post.findUnique({where:{slug:b},include:{categories:{include:{category:!0}},tags:{include:{tag:!0}}}});if(!c)return{};let d=process.env.BASE_URL||"http://localhost:3000",f=`${d}/post/${b}`,g=c.publishedAt?.toISOString(),h=c.updatedAt.toISOString();return{title:c.title,description:c.summary||c.title,keywords:c.tags.map(a=>a.tag.name),authors:[{name:"博主"}],alternates:{canonical:f},openGraph:{type:"article",url:f,title:c.title,description:c.summary||c.title,publishedTime:g,modifiedTime:h,authors:["博主"],tags:c.tags.map(a=>a.tag.name),images:c.coverUrl?[{url:c.coverUrl}]:void 0},twitter:{card:"summary_large_image",title:c.title,description:c.summary||c.title,images:c.coverUrl?[c.coverUrl]:void 0}}}async function i({params:a}){let{slug:b}=await a,c=await e.z.post.findUnique({where:{slug:b},include:{categories:{include:{category:!0}},tags:{include:{tag:!0}}}});return c?(0,d.jsxs)("main",{className:"prose dark:prose-invert max-w-3xl mx-auto p-6",children:[(0,d.jsxs)("header",{className:"mb-8",children:[(0,d.jsx)("h1",{className:"mb-2",children:c.title}),(0,d.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400 space-y-1",children:[c.publishedAt&&(0,d.jsxs)("div",{children:["发布于 ",new Date(c.publishedAt).toLocaleDateString("zh-CN")]}),c.categories.length>0&&(0,d.jsxs)("div",{children:["分类：",c.categories.map(a=>a.category.name).join(", ")]}),c.tags.length>0&&(0,d.jsxs)("div",{children:["标签：",c.tags.map(a=>a.tag.name).join(", ")]})]})]}),(0,d.jsx)("article",{dangerouslySetInnerHTML:{__html:c.contentHtml}}),(0,d.jsx)(g.CommentSection,{postId:c.id})]}):(0,f.notFound)()}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67607:(a,b,c)=>{"use strict";c.d(b,{CommentSection:()=>u});var d=c(4468),e=c(52173),f=c(4209),g=c(72160),h=c(21552),i=c(59943),j=c(68136),k=c(89063),l=c(67657);class m extends e.Component{getSnapshotBeforeUpdate(a){let b=this.props.childRef.current;if(b&&a.isPresent&&!this.props.isPresent){let a=b.offsetParent,c=(0,k.s)(a)&&a.offsetWidth||0,d=this.props.sizeRef.current;d.height=b.offsetHeight||0,d.width=b.offsetWidth||0,d.top=b.offsetTop,d.left=b.offsetLeft,d.right=c-d.width-d.left}return null}componentDidUpdate(){}render(){return this.props.children}}function n({children:a,isPresent:b,anchorX:c,root:f}){let g=(0,e.useId)(),h=(0,e.useRef)(null),i=(0,e.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:j}=(0,e.useContext)(l.Q);return(0,e.useInsertionEffect)(()=>{let{width:a,height:d,top:e,left:k,right:l}=i.current;if(b||!h.current||!a||!d)return;let m="left"===c?`left: ${k}`:`right: ${l}`;h.current.dataset.motionPopId=g;let n=document.createElement("style");j&&(n.nonce=j);let o=f??document.head;return o.appendChild(n),n.sheet&&n.sheet.insertRule(`
          [data-motion-pop-id="${g}"] {
            position: absolute !important;
            width: ${a}px !important;
            height: ${d}px !important;
            ${m}px !important;
            top: ${e}px !important;
          }
        `),()=>{o.contains(n)&&o.removeChild(n)}},[b]),(0,d.jsx)(m,{isPresent:b,childRef:h,sizeRef:i,children:e.cloneElement(a,{ref:h})})}let o=({children:a,initial:b,isPresent:c,onExitComplete:f,custom:g,presenceAffectsLayout:i,mode:k,anchorX:l,root:m})=>{let o=(0,h.M)(p),q=(0,e.useId)(),r=!0,s=(0,e.useMemo)(()=>(r=!1,{id:q,initial:b,isPresent:c,custom:g,onExitComplete:a=>{for(let b of(o.set(a,!0),o.values()))if(!b)return;f&&f()},register:a=>(o.set(a,!1),()=>o.delete(a))}),[c,o,f]);return i&&r&&(s={...s}),(0,e.useMemo)(()=>{o.forEach((a,b)=>o.set(b,!1))},[c]),e.useEffect(()=>{c||o.size||!f||f()},[c]),"popLayout"===k&&(a=(0,d.jsx)(n,{isPresent:c,anchorX:l,root:m,children:a})),(0,d.jsx)(j.t.Provider,{value:s,children:a})};function p(){return new Map}var q=c(22971);let r=a=>a.key||"";function s(a){let b=[];return e.Children.forEach(a,a=>{(0,e.isValidElement)(a)&&b.push(a)}),b}let t=({children:a,custom:b,initial:c=!0,onExitComplete:f,presenceAffectsLayout:j=!0,mode:k="sync",propagate:l=!1,anchorX:m="left",root:n})=>{let[p,t]=(0,q.xQ)(l),u=(0,e.useMemo)(()=>s(a),[a]),v=l&&!p?[]:u.map(r),w=(0,e.useRef)(!0),x=(0,e.useRef)(u),y=(0,h.M)(()=>new Map),[z,A]=(0,e.useState)(u),[B,C]=(0,e.useState)(u);(0,i.E)(()=>{w.current=!1,x.current=u;for(let a=0;a<B.length;a++){let b=r(B[a]);v.includes(b)?y.delete(b):!0!==y.get(b)&&y.set(b,!1)}},[B,v.length,v.join("-")]);let D=[];if(u!==z){let a=[...u];for(let b=0;b<B.length;b++){let c=B[b],d=r(c);v.includes(d)||(a.splice(b,0,c),D.push(c))}return"wait"===k&&D.length&&(a=D),C(s(a)),A(u),null}let{forceRender:E}=(0,e.useContext)(g.L);return(0,d.jsx)(d.Fragment,{children:B.map(a=>{let e=r(a),g=(!l||!!p)&&(u===B||v.includes(e));return(0,d.jsx)(o,{isPresent:g,initial:(!w.current||!!c)&&void 0,custom:b,presenceAffectsLayout:j,mode:k,root:n,onExitComplete:g?void 0:()=>{if(!y.has(e))return;y.set(e,!0);let a=!0;y.forEach(b=>{b||(a=!1)}),a&&(E?.(),C(x.current),l&&t?.(),f&&f())},anchorX:m,children:a},e)})})};function u({postId:a}){let[b,c]=(0,e.useState)([]),[g,h]=(0,e.useState)(!0),[i,j]=(0,e.useState)(!1),[k,l]=(0,e.useState)(!1),[m,n]=(0,e.useState)(""),[o,p]=(0,e.useState)(""),[q,r]=(0,e.useState)(""),[s,u]=(0,e.useState)(""),[v,w]=(0,e.useState)("");async function x(b){b.preventDefault(),j(!0),w(""),u("");try{let b=await fetch("/api/comments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({postId:a,author:m.trim(),email:o.trim()||void 0,content:q.trim()})}),c=await b.json();c.success?(u(c.message||"评论提交成功，等待审核"),n(""),p(""),r(""),l(!1)):w(c.error?.message||"提交失败")}catch(a){w("网络错误，请稍后重试")}finally{j(!1)}}let y={hidden:{opacity:0,y:20},visible:{opacity:1,y:0}};return(0,d.jsxs)(f.P.section,{className:"mt-12 border-t pt-8",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},initial:"hidden",animate:"visible",children:[(0,d.jsxs)(f.P.div,{variants:y,className:"flex items-center justify-between mb-6",children:[(0,d.jsxs)("h3",{className:"text-xl font-semibold",children:["评论 ",b.length>0&&`(${b.length})`]}),(0,d.jsx)("button",{onClick:()=>l(!k),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:k?"取消":"发表评论"})]}),(0,d.jsxs)(t,{children:[s&&(0,d.jsx)(f.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"mb-4 p-3 bg-green-100 text-green-800 rounded-lg",children:s}),v&&(0,d.jsx)(f.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"mb-4 p-3 bg-red-100 text-red-800 rounded-lg",children:v})]}),(0,d.jsx)(t,{children:k&&(0,d.jsxs)(f.P.form,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},onSubmit:x,className:"mb-8 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,d.jsx)("input",{type:"text",placeholder:"昵称 *",value:m,onChange:a=>n(a.target.value),required:!0,className:"px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,d.jsx)("input",{type:"email",placeholder:"邮箱（可选）",value:o,onChange:a=>p(a.target.value),className:"px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,d.jsx)("textarea",{placeholder:"写下你的评论...",value:q,onChange:a=>r(a.target.value),required:!0,rows:4,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent mb-4"}),(0,d.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,d.jsx)("button",{type:"button",onClick:()=>l(!1),className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"取消"}),(0,d.jsx)("button",{type:"submit",disabled:i||!m.trim()||!q.trim(),className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:i?"提交中...":"发表评论"})]})]})}),(0,d.jsx)("div",{className:"space-y-4",children:g?(0,d.jsx)(f.P.div,{variants:y,className:"text-center py-8 text-gray-500",children:"加载中..."}):0===b.length?(0,d.jsx)(f.P.div,{variants:y,className:"text-center py-8 text-gray-500",children:"暂无评论，来发表第一条评论吧！"}):b.map((a,b)=>(0,d.jsxs)(f.P.div,{variants:y,custom:b,className:"p-4 border rounded-lg bg-white dark:bg-gray-900 hover:shadow-md transition-shadow",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsx)("span",{className:"font-medium text-gray-900 dark:text-gray-100",children:a.author}),(0,d.jsx)("time",{className:"text-sm text-gray-500",children:new Date(a.createdAt).toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})})]}),(0,d.jsx)("p",{className:"text-gray-700 dark:text-gray-300 whitespace-pre-wrap",children:a.content})]},a.id))})]})}},83191:(a,b,c)=>{Promise.resolve().then(c.bind(c,67607))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96330:a=>{"use strict";a.exports=require("@prisma/client")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[223,9549,4209,5851],()=>b(b.s=35849));module.exports=c})();